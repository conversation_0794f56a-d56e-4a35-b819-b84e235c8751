import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { TechPark } from 'app/core/models/tech-park-model';
import { SharedModule } from 'app/shared/shared.module';
import {
  actionType,
  buttonActions,
  CommonService,
  HTTP_STATUS,
  ICON_BUTTON_SEVERITY,
  ICON_PRIMENG_LIST,
  InvokeService,
  TABLE_ACTION_TYPES,
  toolbarActions,
} from 'app/shared/tapas-ui';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { finalize, ReplaySubject, takeUntil } from 'rxjs';

import { CreateEditTechParkComponent } from './create-edit-tech-park/create-edit-tech-park.component';

@Component({
  selector: 'tps-tech-park',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './tech-park.component.html'
})
export class TechParkComponent implements OnInit, OnD<PERSON>roy {
  hdr: string = 'Tech Park';
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);
  columnDefs: any[] = [];
  techParks: TechPark[] = [];
  ACL_LIST = this._commonService.getACLList();
  buttonActions: buttonActions[] = [];
  toolBarActions: toolbarActions[] = [];
  isMobile: boolean = false;

  constructor(
    private _commonService: CommonService,
    private _invokeService: InvokeService,
    private _dialogService: DialogService,
  ) { }

  public ngOnInit(): void {
    this.prepareTableActions();
    this.onRefresh();
  }

  private prepareTableActions(): void {
    this.buttonActions = [
      {
        name: 'Create Tech Park',
        icon: 'pi-plus',
        primary: true,
        isSvg: false,
        show: this.ACL_LIST.TECH_PARK_CREATE,
        command: () => {
          this.onOpenTechParkDialog('Create Tech Park', new TechPark(), TABLE_ACTION_TYPES.CREATE);
        }
      }
    ];
    this.toolBarActions = [];
  }

  private prepareTableColumns(): void {
    this.columnDefs = [
      {
        headerName: "Code",
        field: "code",
        sortable: true,
      },
      { headerName: "Vendor Code", field: "vendorCode", sortable: true },
      { headerName: "Factory Code", field: "factoryCode", sortable: true },
      {
        headerName: "Document Type",
        field: "type",
        sortable: true,
      },
      {
        headerName: "Gold Seal",
        field: "goldSeal",
        sortable: true,
      },
      {
        headerName: "Created By",
        field: "createdByUserCode",
        sortable: true,
      },
      {
        headerName: "Modified By",
        field: "modifiedBy",
        sortable: true,
      },
      {
        headerName: 'Actions',
        pinned: 'right',
        maxWidth: 100,
        sortable: false,
        isActionCol: true,
        actions: (params) => this.prepareTableActionIcons(params)
      }
    ];
  }

  public prepareTableActionIcons(row): actionType[] {
    let iconsList: actionType[] = [];
    iconsList.push({
      type: TABLE_ACTION_TYPES.VIEW,
      icon: ICON_PRIMENG_LIST.PI_EYE,
      title: "View",
      data: row,
      severity: ICON_BUTTON_SEVERITY.SECONDARY,
      onAction: (rowItem) => this.onOpenTechParkDialog('View Tech Park', row, TABLE_ACTION_TYPES.VIEW)
    });

    if (this.ACL_LIST?.TECH_PARK_UPDATE) {
      iconsList.push({
        type: TABLE_ACTION_TYPES.EDIT,
        icon: ICON_PRIMENG_LIST.PI_PENCIL,
        title: "Edit",
        data: row,
        severity: ICON_BUTTON_SEVERITY.INFO,
        onAction: (rowItem) => this.onOpenTechParkDialog('Edit Tech Park', row, TABLE_ACTION_TYPES.EDIT)
      });
    }

    if (this.ACL_LIST?.TECH_PARK_DELETE) {
      iconsList.push({
        type: TABLE_ACTION_TYPES.DELETE,
        icon: ICON_PRIMENG_LIST.PI_TRASH,
        title: "Delete",
        data: row,
        severity: ICON_BUTTON_SEVERITY.DANGER,
        onAction: (rowItem) => this.deleteTechPark(row)
      });
    }

    return iconsList;
  }

  public onRefresh(): void {
    this.getTechParks();
    this.prepareTableColumns();
  }

  private getTechParks(): void {
    this._commonService.loadingTableData.next(true);
    this._invokeService.serviceInvocation(APP_UI_CONFIG.document.techPark.get)
      .pipe(
        takeUntil(this.$destroyed),
        finalize(() => {
          setTimeout(() => {
            this._commonService.loadingTableData.next(false);
          }, 500);
        })
      )
      .subscribe({
        next: response => {
          this.prepareTechParkData(response);
        },
        error: error => {
          this._commonService.handleError(error);
        }
      });
  }
  private prepareTechParkData(data: any[]): void {
    this.techParks = data;
    this._commonService.loadingTableData.next(false);
  }

  private onOpenTechParkDialog(title, row, status): void {
    const ref: DynamicDialogRef = this._dialogService.open(CreateEditTechParkComponent, {
      data: { data: row, status: status },
      header: title,
      modal: true,
      width: this.isMobile ? '100%' : '60%',
      closeOnEscape: false,
      dismissableMask: false,
      transitionOptions: "300ms",
      baseZIndex: 10000,
      closable: true,
      focusOnShow: false,
      style: {
        'max-height': '90vh',
        'margin-top': this.isMobile ? '0' : '2rem'
      },
      breakpoints: {
        '768px': '100vw',
        '576px': '100vw'
      }
    });
    ref.onClose.subscribe((response: any) => {
      if (response) {
        this.onRefresh();
      }
    });
  }

  private deleteTechPark(row: TechPark): void {
    this._commonService.confirm('Are you sure you want to delete this tech park?', row.code)
      .pipe(takeUntil(this.$destroyed))
      .subscribe(confirm => {
        if (confirm) {
          this._invokeService.serviceInvocation(APP_UI_CONFIG.document.techPark.delete, row.uuid)
            .pipe(takeUntil(this.$destroyed))
            .subscribe({
              next: response => {
                if (response.code == HTTP_STATUS.SUCCESS) {
                  this._commonService.success('Tech Park deleted successfully');
                  this.onRefresh();
                } else {
                  this._commonService.error('Failed to delete the tech park');
                }
              },
              error: error => {
                this._commonService.handleError(error);
              }
            });
        }
      });
  }

  public ngOnDestroy(): void {
    this.$destroyed.next(null);
    this.$destroyed.complete();
  }
}