import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { DefectType } from 'app/core/models/defect-type.model';
import { SharedModule } from 'app/shared/shared.module';
import {
    actionType,
    buttonActions,
    CommonService,
    ICON_BUTTON_SEVERITY,
    ICON_PRIMENG_LIST,
    InvokeService,
    TABLE_ACTION_TYPES,
    toolbarActions,
} from 'app/shared/tapas-ui';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { finalize, ReplaySubject, takeUntil } from 'rxjs';

import { CreateEditDefectTypeComponent } from './create-edit-defect-type/create-edit-defect-type.component';


@Component({
  selector: 'tps-defect-type',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './defect-type.component.html',
})
export class DefectTypeComponent implements OnInit, OnD<PERSON>roy {
  hdr: string = 'Defect Types';
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);
  columnDefs: any[] = [];
  defectTypes: DefectType[] = [];
  ACL_LIST = this._commonService.getACLList();
  buttonActions: buttonActions[] = [];
  toolBarActions: toolbarActions[] = [];
  isMobile: boolean = false;

  constructor(
    private _commonService: CommonService,
    private _invokeService: InvokeService,
    private _dialogService: DialogService,
  ) { }

  public ngOnInit(): void {
    // Monitor screen size changes
    this._commonService.isMobile().subscribe(result => {
      this.isMobile = result.matches;
    });
    this.prepareTableActions();
    this.onRefresh();
  }

  private prepareTableActions(): void {
    this.buttonActions = [
      {
        name: 'Create Defect Type',
        icon: 'pi-plus',
        primary: true,
        isSvg: false,
        show: this.ACL_LIST.DEFECT_TYPE_MASTER_CREATE,
        command: () => {
          this.onOpenDefectTypeDialog('Create Defect Type', new DefectType(), TABLE_ACTION_TYPES.CREATE);
        }
      }
    ];
    this.toolBarActions = [];
  }

  public onRefresh(): void {
    this.getDefectTypes();
    this.prepareTableColumns();
  }

  private prepareTableColumns(): void {
    this.columnDefs = [
      {
        headerName: "Code",
        field: "code",
        sortable: true,
      },
      {
        headerName: "Name",
        field: "name",
        sortable: true,
      },
      {
        headerName: "Description",
        field: "description",
        sortable: true,
      },
      {
        headerName: "Defect Category",
        field: "defectTypeCategory",
        sortable: true,
      },
      //for madura
      // {
      //   headerName: "Category",
      //   field: "category",
      //   sortable: true,
      // },
      // {
      //   headerName: "Type",
      //   field: "defectNature",
      //   sortable: true,
      // },

      {
        headerName: "Created Date",
        field: "createdDateText",
        sortable: true,
      },
      {
        headerName: "Status",
        field: "status",
        sortable: true,
      },
      {
        headerName: 'Actions',
        pinned: 'right',
        maxWidth: 100,
        sortable: false,
        isActionCol: true,
        actions: (params) => this.prepareTableActionIcons(params)
      }
    ];
  }

  public prepareTableActionIcons(row): actionType[] {
    let iconsList: actionType[] = [];
    iconsList.push({
      type: TABLE_ACTION_TYPES.VIEW,
      icon: ICON_PRIMENG_LIST.PI_EYE,
      title: "View",
      data: row,
      severity: ICON_BUTTON_SEVERITY.SECONDARY,
      onAction: (rowItem) => this.onOpenDefectTypeDialog('View Auditor Details', row, TABLE_ACTION_TYPES.VIEW)
    });

    if (this.ACL_LIST?.DEFECT_TYPE_MASTER_EDIT) {
      iconsList.push({
        type: TABLE_ACTION_TYPES.EDIT,
        icon: ICON_PRIMENG_LIST.PI_PENCIL,
        title: "Edit",
        data: row,
        severity: ICON_BUTTON_SEVERITY.INFO,
        onAction: (rowItem) => this.onOpenDefectTypeDialog('Edit Auditor Details', row, TABLE_ACTION_TYPES.EDIT)
      });
    }

    if (row.deleted == 1 && this.ACL_LIST?.DEFECT_TYPE_MASTER_ACTIVATE) {
      iconsList.push({
        type: TABLE_ACTION_TYPES.ACTIVATE,
        icon: ICON_PRIMENG_LIST.PI_POWER_OFF,
        title: "Activate",
        data: row,
        severity: ICON_BUTTON_SEVERITY.SUCCESS,
        onAction: (rowItem) => this.onActivate(row)
      });

    } else if (row.deleted == 0 && this.ACL_LIST?.DEFECT_TYPE_MASTER_DEACTIVATE) {
      iconsList.push({
        type: TABLE_ACTION_TYPES.DEACTIVATE,
        icon: ICON_PRIMENG_LIST.PI_POWER_OFF,
        title: "De-Activate", data: row,
        severity: ICON_BUTTON_SEVERITY.DANGER,
        onAction: (rowItem) => this.onDeActivate(row)
      });
    }

    // if (this.ACL_LIST?.AUDITOR_DELETE) {
    //   iconsList.push({
    //     type: TABLE_ACTION_TYPES.DELETE,
    //     icon: ICON_PRIMENG_LIST.PI_TRASH,
    //     title: "Delete",
    //     data: row,
    //     severity: ICON_BUTTON_SEVERITY.DANGER,
    //     onAction: (rowItem) => this.deleteDefectType(row)
    //   });
    // }

    return iconsList;
  }

  private getDefectTypes(): void {
    this._commonService.loadingTableData.next(true);
    this._invokeService.serviceInvocation(APP_UI_CONFIG.master.defectType.get)
      .pipe(
        takeUntil(this.$destroyed),
        finalize(() => {
          setTimeout(() => {
            this._commonService.loadingTableData.next(false);
          }, 500);
        })
      )
      .subscribe({
        next: response => {
          this.prepareAuditorData(this._commonService.sortByDateLatest(response, 'createdTime', 'createdTime'));
        },
        error: error => {
          this._commonService.handleError(error);
        }
      });
  }

  private prepareAuditorData(data: any[]): void {
    this.defectTypes = data.map(item => ({
      createdDateText: this._commonService.dateTimeFormat(item.createdTime),
      ...item
    }));

  }

  private onActivate(row: DefectType): void {
    this._commonService.confirm('Are you sure you want to activate this Defect Type?', row.name)
      .pipe(takeUntil(this.$destroyed))
      .subscribe(confirm => {
        if (confirm) {
          APP_UI_CONFIG.master.defectType.activate.paramList.defectUid = row.uuid;
          this._invokeService.serviceInvocation(APP_UI_CONFIG.master.defectType.activate)
            .pipe(takeUntil(this.$destroyed))
            .subscribe({
              next: response => {
                this._commonService.success('Defect Type activated successfully');
                this.onRefresh();
              },
              error: error => {
                this._commonService.handleError(error);
              }
            });
        }
      });
  }
  private onDeActivate(row: DefectType): void {
    this._commonService.confirm('Are you sure you want to delete this Defect Type?', row.name)
      .pipe(takeUntil(this.$destroyed))
      .subscribe(confirm => {
        if (confirm) {
          APP_UI_CONFIG.master.defectType.deactivate.paramList.defectUid = row.uuid;
          this._invokeService.serviceInvocation(APP_UI_CONFIG.master.defectType.deactivate)
            .pipe(takeUntil(this.$destroyed))
            .subscribe({
              next: response => {
                this._commonService.success('Defect Type deleted successfully');
                this.onRefresh();
              },
              error: error => {
                this._commonService.handleError(error);
              }
            });
        }
      });
  }

  private onOpenDefectTypeDialog(title, row, status): void {
    const ref: DynamicDialogRef = this._dialogService.open(CreateEditDefectTypeComponent, {
      data: { data: row, status: status },
      header: title,
      modal: true,
      width: this.isMobile ? '100%' : '60%',
      closeOnEscape: false,
      dismissableMask: false,
      transitionOptions: "300ms",
      baseZIndex: 10000,
      closable: true,
      focusOnShow: false,
      style: {
        'max-height': '90vh',
        'margin-top': this.isMobile ? '0' : '2rem'
      },
      breakpoints: {
        '768px': '100vw',
        '576px': '100vw'
      }
    });
    ref.onClose.subscribe((response: any) => {
      if (response) {
        this.onRefresh();
      }
    });
  }

  public ngOnDestroy(): void {
    this.$destroyed.next(null);
    this.$destroyed.complete();
  }
}
