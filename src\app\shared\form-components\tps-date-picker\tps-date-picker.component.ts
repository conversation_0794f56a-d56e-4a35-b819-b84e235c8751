import { CommonModule, NgF<PERSON>, NgIf } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormGroup, FormGroupDirective, ReactiveFormsModule } from '@angular/forms';
import { DatePickerModule } from 'primeng/datepicker';
import { TooltipModule } from 'primeng/tooltip';

import { FORM_CONTROL_TYPES } from '../../constants/tps-enum-constants';
import { TpsErrorComponent } from '../tps-error/tps-error.component';

class tpsDatePicker {
  type: string
  fieldName: string
  tooltip?: string
  hint?: string
  value: any
  label: string
  placeholder: string
  show: boolean;
  showTime?: boolean
  onChange?: (field) => void
  rules: {
    required?: boolean,
    minDate?: Date
    maxDate?: Date
  }
}
@Component({
  selector: 'tps-date-picker',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, NgIf, TpsErrorComponent, DatePickerModule, TooltipModule],
  templateUrl: './tps-date-picker.component.html',
})
export class TpsDatePickerComponent implements OnInit {
  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  @Input() field: tpsDatePicker;
  @Input() formName: FormGroup;
  @Input() layout: string = 'row';
  @Input() showLabel: boolean = true;
  @Output() onChange: EventEmitter<any> = new EventEmitter();
  yearRange: any;
  dateFormat: string = 'dd-MM-y';

  constructor(private formGroupDirective: FormGroupDirective) {
    this.formName = formGroupDirective.control;
  }

  ngOnInit(): void {
    if (this.field.showTime) {
      this.dateFormat = `dd-MM-y hh:mm a`
    }
  }
  public onChangeField(event, field): void {
    this.onChange.emit(event);
  }
}
