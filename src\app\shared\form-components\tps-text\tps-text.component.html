<form [formGroup]="formName" autocomplete="off">
    <div class="row mb-2" *ngIf="layout=='row'">
        <div class="col-md-5">
            <div class="flex flex-row items-center gap-2">
                <label for="{{field.label}}">{{field.label}}
                    <span *ngIf="field?.rules?.required && !formName.controls[field.fieldName].disabled"
                        class="requiredField">*</span>
                </label>
                <i *ngIf="field?.tooltip" [pTooltip]="field?.tooltip" tooltipPosition="bottom"
                    class="cursor-pointer pi pi-question-circle helping_text_icon"></i>
            </div>
        </div>
        <div class="col-md-7">
            <div class="w-full flex flex-column gap-1">
                <input class="w-full tps-input" pInputText type="text" variant="outlined" id="{{field.label}}"
                    [name]="field.fieldName" [placeholder]="field.placeholder" [formControlName]="field.fieldName"
                    [required]="field?.rules?.required" [minlength]="field?.rules?.minLength"
                    [maxlength]="maxLength" [pattern]="field?.rules?.pattern"
                    [class.ng-invalid]="formName.controls[field.fieldName].invalid"
                    [class.ng-dirty]="(formName.controls[field.fieldName].dirty || formName.controls[field.fieldName].touched)" />
                <small *ngIf="field?.hint">{{field?.hint}}</small>
                <tps-error [fieldName]="field.fieldName" [label]="field.label" [formName]="formName"></tps-error>
            </div>
        </div>
    </div>
    <div class="flex flex-col gap-1" *ngIf="layout=='column'">
        <div class="flex flex-row items-center gap-2" *ngIf="showLabel">
            <label for="{{field.label}}">{{field.label}}
                <span *ngIf="field?.rules?.required && !formName.controls[field.fieldName].disabled"
                    class="requiredField">*</span>
            </label>
            <i *ngIf="field?.tooltip" [pTooltip]="field?.tooltip" tooltipPosition="bottom"
                class="cursor-pointer pi pi-question-circle helping_text_icon"></i>
        </div>
        <div class="w-full flex flex-column gap-1">
            <input class="w-full tps-input" pInputText type="text" variant="outlined" id="{{field.label}}"
                [name]="field.fieldName" [placeholder]="field.placeholder" [formControlName]="field.fieldName"
                [required]="field?.rules?.required" [minlength]="field?.rules?.minLength"
                [maxlength]="field?.rules?.maxLength" [pattern]="field?.rules?.pattern"
                [class.ng-invalid]="formName.controls[field.fieldName].invalid"
                [class.ng-dirty]="(formName.controls[field.fieldName].dirty || formName.controls[field.fieldName].touched)" />
            <small *ngIf="field?.hint">{{field?.hint}}</small>
            <tps-error [fieldName]="field.fieldName" [label]="field.label" [formName]="formName"></tps-error>
        </div>
    </div>
</form>