import { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'environments/environment';
import { saveAs } from 'file-saver';
import { catchError, Observable, ReplaySubject, takeUntil, throwError } from 'rxjs';

import { API_TYPES } from '../constants/tps-enum-constants';
import { CommonService } from './common.service';


@Injectable({
  providedIn: 'root'
})
export class InvokeService {
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);
  constructor(
    private _httpClient: HttpClient,
    private _commonService: CommonService,) { }

  public serviceInvocation(context, params?, payload?): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid(), 'tenantUid': this._commonService.getTenantUid(), 'tuid': this._commonService.getTenantUid() }) //environment .tenantUid
      .set("Authorization", this._commonService.getBasicAuth());
    let apiResponse: Observable<any> = new Observable();
    let baseUrl = environment.apiUrl;
    if (context.baseUrl) {
      baseUrl = environment[context.baseUrl]
    }
    if (context.externalUrl) {
      baseUrl = context.externalUrl;
    }
    let param = params;
    let queryParams;
    if (param != null && param != undefined && param.length != 0) {
      queryParams = "?1=1";
      for (let i in param) {
        queryParams += i ? "&" : "";
        queryParams += param[i].key + "=" + param[i].value;
      }
    }
    if (context.type == API_TYPES.GET) {
      let url1 = this.rePlaceString(context);
      let url = baseUrl;
      if (url1) {
        //console.log("GET url1:", url1)
        url = baseUrl + url1;
      }
      if (queryParams) {
        url += queryParams;
      }
      apiResponse = this._httpClient.get(url, { headers: headers }).pipe(catchError(error => this.handleError(error)))
    }
    if (context.type == API_TYPES.POST) {
      let url1 = this.rePlaceString(context);
      let url = baseUrl;
      if (url1) {
        url = baseUrl + url1;
      }
      if (queryParams) {
        url += queryParams;
      }
      let payload1: any = {};
      payload1 = payload;
      apiResponse = this._httpClient.post(url, payload1, { headers: headers }).pipe(catchError(error => this.handleError(error)))
    }

    if (context.type == API_TYPES.DELETE) {
      let url1 = this.rePlaceString(context);
      let url = baseUrl;
      if (url1) {
        url = baseUrl + url1;
      }
      if (queryParams) {
        url += queryParams;
      }
      apiResponse = this._httpClient.delete(url, { headers: headers, body: payload }).pipe(catchError(error => this.handleError(error)))
    }
    if (context.type == API_TYPES.PUT) {
      let url1 = this.rePlaceString(context);
      let url = baseUrl;
      if (url1) {
        url = baseUrl + url1;
      }
      if (queryParams) {
        url += queryParams;
      }
      apiResponse = this._httpClient.put(url, payload, { headers: headers }).pipe(catchError(error => this.handleError(error)))
    }

    return apiResponse;
  }

  public rePlaceString(context: any): string {
    let res: string = context.url;
    let res1 = res;
    if (context?.paramList) {
      for (var key of Object.keys(context?.paramList)) {
        if (res.search(key) != -1) {
          res = res.replace(`{${key}}`, context.paramList[key]);
        } else {
          res1 = res;
        }
      }
      res1 = res;
    } else {
      res1 = res;
    }
    return res1;
  }

  public getImage(context): Observable<any> {
    let headers = new HttpHeaders({ 'Client': this._commonService.getTenantUid(), 'tenantUid': this._commonService.getTenantUid(), 'tuid': this._commonService.getTenantUid() }) //environment .tenantUid
      .set("Authorization", this._commonService.getBasicAuth());
    let url1 = this.rePlaceString(context);
    let url = environment.apiUrl;
    if (context.baseUrl) {
      url = environment[context.baseUrl]
    }
    url = url + url1;
    return this._httpClient.get(url, { headers: headers, responseType: 'blob' }).pipe(catchError(error => this.handleError(error)))
  }
  public getImageBase64(context: any): Observable<any> {
    let headers = new HttpHeaders({ 'tenantUid': this._commonService.getTenantUid(), 'Client': this._commonService.getTenantUid(), 'tuid': this._commonService.getTenantUid() }).set("Authorization", this._commonService.getBasicAuth());
    let url1 = this.rePlaceString(context);
    let url = environment.apiUrl + url1;
    return this._httpClient.get<any>(url, { headers: headers });
  }

  public getPreviewImageBase64(context: any): Observable<any> {
    let httpheaders = new HttpHeaders({ 'tenantUid': environment.tenantUid,'tuid': environment.tenantUid }).set("Authorization", this._commonService.getBasicAuth());
    let headers = { headers: httpheaders };
    let baseUrl = environment.apiUrl;
    if (context.externalUrl) {
      baseUrl = environment.apiUrl;
    }
    return this._httpClient.get<any>(baseUrl + context.url, headers)
  }

  public handleError(errorResponse: HttpErrorResponse) {
    let errorMessage: any = {};
    if (errorResponse.status == 0) {
      let errorMessage1 = 'Could not connect to network. please check and try again later.';
      this._commonService.error(errorMessage1);
    }
    else if (errorResponse.status == 500) {
      let errorMessage1 = 'Internal Server Error';
      this._commonService.error(errorMessage1);
    }
    else {
      if (errorResponse.error instanceof ErrorEvent) {
        errorMessage = `${errorResponse.error}`;
      } else {
        // server-side error
        errorMessage = `${errorResponse.error}`;
      }
    }
    return throwError(errorMessage);
  }

  public uploadAttachments(context, payload): Observable<any> {
    let headers = new HttpHeaders({ 'Client': this._commonService.getTenantUid(), 'tenantUid': this._commonService.getTenantUid(), 'tuid': this._commonService.getTenantUid() }) //environment .tenantUid
      .set("Authorization", this._commonService.getBasicAuth());
    let apiResponse: Observable<any> = new Observable();
    let baseUrl = environment.apiUrl;
    if (context.baseUrl) {
      baseUrl = environment[context.baseUrl]
    }
    if (context?.externalUrl) {
      baseUrl = context.externalUrl;
    }
    if (context.type == "PUT") {
      let url1 = this.rePlaceString(context);
      let url = baseUrl + url1;
      apiResponse = this._httpClient.put(url, payload, { headers: headers });
    }
    if (context.type == "POST") {
      let url1 = this.rePlaceString(context);
      let url = baseUrl + url1;
      apiResponse = this._httpClient.post(url, payload, { headers: headers });
    }
    return apiResponse;
  }


  downloadFile(context: any, params: any, fileName: String, payload?: any): any {
    let headers = new HttpHeaders({ 'Client': environment.tenantUid })
      .set("Authorization", this._commonService.getBasicAuth());
    let baseUrl = environment.apiUrl;
    if (context.externalUrl) {
      baseUrl = environment.apiUrl;
    }
    if (context.type == "GET") {
      let url1 = this.rePlaceString(context);
      let url = baseUrl + url1;

      let param = params;
      if (param != null && param != undefined && param.length != 0) {
        let p = "?";
        for (let i in param) {
          p += i ? "&" : "";
          p += param[i].key + "=" + param[i].value;
        }
        url += p;
      }
      this._httpClient.get(url, { headers: headers, responseType: 'blob' as 'json' })
        .pipe(takeUntil(this.$destroyed))
        .subscribe({
          next: response => {
            if (response) {
              this.saveToFileSystem(fileName, response);
            } else {
              this._commonService.error('Failed to download a file');
            }
          },
          error: error => {
            this._commonService.handleError(error);
          }
        })
    }
    else if (context.type == "POST") {
      let url1 = this.rePlaceString(context);
      let url = baseUrl + url1;
      this._httpClient.post(url, payload, { headers: headers, 'responseType': 'text' })
        .pipe(takeUntil(this.$destroyed))
        .subscribe({
          next: response => {
            if (response) {
              this.saveToFileSystem(fileName, response);
            } else {
              this._commonService.error('Failed to download a file');
            }
          },
          error: error => {
            this._commonService.handleError(error);
          }
        })
    }
  }

  uploadImage(context: any, params?, id?, payload?): Observable<any> {
    let headers = new HttpHeaders({ 'tenantUid': environment.tenantUid,'tuid': environment.tenantUid })
      .set("Authorization", this._commonService.getBasicAuth());
    let apiResponse: Observable<any> = new Observable();
    let baseUrl = environment.apiUrl;
    if (context.externalUrl) {
      baseUrl = environment.apiUrl;
    }
    if (context.type == "PUT") {
      let url1 = this.rePlaceString(context);
      let url = baseUrl + url1;
      apiResponse = this._httpClient.put(url, context.payload, { headers: headers, });
    }
    if (context.type == "POST") {
      let url1 = this.rePlaceString(context);
      let url = baseUrl + url1;
      apiResponse = this._httpClient.post(url, context.payload, { headers: headers });
    }
    return apiResponse;
  }  

  public getImageBlob(context): Observable<any>{
    let baseUrl = environment.apiUrl;
    if (context.externalUrl) {
      baseUrl = environment.apiUrl;
    }
    let url1 = this.rePlaceString(context);
    let url = baseUrl + url1;
    let httpheaders = new HttpHeaders({ 'tenantUid': environment.tenantUid,'tuid': environment.tenantUid }).set("Authorization", this._commonService.getBasicAuth());
    return this._httpClient.get(url, { headers: httpheaders, responseType: 'blob' as 'json' });
  }

  public saveToFileSystem(filename, response) {
    //const filename = "orderitems.csv";//parts[1].split('=')[1];
    const blob = new Blob([response], { type: 'text/plain' });
    saveAs(blob, filename);
  }


}
