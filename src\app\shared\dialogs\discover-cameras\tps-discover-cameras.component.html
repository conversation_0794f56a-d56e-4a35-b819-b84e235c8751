<div class="w-full flex flex-col gap-1">
    <!-- <div class="modal-header">
        <tps-header [headerName]="hdr" [showBackIcon]="false"></tps-header>
    </div> -->
    <div class="modal-content">
        <div class="flex flex-wrap gap-3 mb-4">
            <div class="flex align-items-center">
                <p-radioButton name="without" value="without" [(ngModel)]="discoverType"
                    (onClick)="onChangeDiscoverType()" inputId="Without UserName and Password" />
                <label for="Without UserName and Password" class="ml-2">
                    Without UserName and Password
                </label>
            </div>

            <div class="flex align-items-center">
                <p-radioButton name="with" value="with" [(ngModel)]="discoverType" (onClick)="onChangeDiscoverType()"
                    inputId="With Username and Password" />
                <label for="With Username and Password" class="ml-2">
                    With Username and Password.
                </label>
            </div>
        </div>
        
        <form [formGroup]="discoverForm" autocomplete="off" *ngIf="discoverType">
            <div class="w-full flex flex-col gap-4">
                <div class="required_fields_message mb-2">
                    Note: Fields marked with (*) are mandatory
                </div>
                <div class="dynamic-form-wrapper w-full min-w-0 form-row justify-between ">
                    <ng-container *ngFor="let field of fields" [ngSwitch]="field.type">
                        <div *ngIf="field?.topGroupTitle" class="w-full tps-dynamic-form-sub-title my-2 mb-4">
                            {{field.topGroupTitle}}
                        </div>
                        <ng-container *ngIf="field.show">
                            <tps-text class="col-md-12 col-sm-12 col-12" *ngSwitchCase="FORM_CONTROL_TYPES.TEXT"
                                [field]="field" [formName]="discoverForm"></tps-text>
                        </ng-container>
                        <ng-container *ngIf="field.show">
                            <tps-password class="col-md-12 col-sm-12 col-12" *ngSwitchCase="FORM_CONTROL_TYPES.PASSWORD"
                                [field]="field" [formName]="discoverForm"></tps-password>
                        </ng-container>
                        <ng-container >
                            <tps-single-select class="col-md-12 col-sm-12 col-12"
                                *ngSwitchCase="FORM_CONTROL_TYPES.SINGLE_SELECT" [field]="field"
                                [formName]="discoverForm"></tps-single-select>
                        </ng-container>
                    </ng-container>
                </div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <div class="w-full flex flex-row justify-end gap-6">
            <tps-secondary-button [buttonName]="'Cancel'" (onClick)="close()"></tps-secondary-button>
            <tps-primary-button *ngIf="discoverType=='with'" [buttonName]="'Submit'" [isDisabled]="discoverForm.invalid"
                (onClick)="onSubmit()"></tps-primary-button>
            <tps-primary-button *ngIf="discoverType=='without'" [buttonName]="'Submit'"
                (onClick)="onSubmit()"></tps-primary-button>
        </div>
    </div>
</div>