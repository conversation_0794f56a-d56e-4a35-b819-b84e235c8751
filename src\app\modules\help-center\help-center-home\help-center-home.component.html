<div class="flex min-w-0 flex-auto flex-col ">
    <div class=" relative overflow-hidden  px-4 pb-28 pt-8  sm:px-16 sm:pb-48 sm:pt-20"
        style="color:white;background-image: linear-gradient(90deg, #265DAB 12.65%, #2B75BC 30.36%, #3F8CC6 48.58%)">
        <!-- <svg viewBox="0 0 960 540" width="100%" height="100%" preserveAspectRatio="xMidYMax slice"
            xmlns="http://www.w3.org/2000/svg" class="absolute inset-0 pointer-events-none">
            <g fill="none" stroke="currentColor" stroke-width="100" class="text-gray-700 opacity-25">
                <circle r="234" cx="196" cy="23"></circle>
                <circle r="234" cx="790" cy="491"></circle>
            </g>
        </svg> -->
        <div class="relative z-10 flex flex-col items-center">
            <h2 class="text-xl font-semibold">HELP CENTER</h2>
            <div class="mt-1 text-center text-4xl font-extrabold leading-tight tracking-tight sm:text-7xl"> How can we
                help you today? </div>
        </div>
    </div>
    <div class="flex flex-col items-center px-6 pb-6 sm:px-10 sm:pb-10">
        <div
            class="-mt-16 grid w-full max-w-sm grid-cols-1 gap-y-8 sm:-mt-24 md:max-w-4xl md:grid-cols-1 md:gap-x-4 md:gap-y-0">
            <!-- <div class="bg-card relative flex flex-col overflow-hidden rounded-2xl shadow transition-shadow duration-150 ease-in-out hover:shadow-lg"
                style="box-shadow: rgba(0, 0, 0, 0.07) 0px 1px 2px, rgba(0, 0, 0, 0.07) 0px 2px 4px, rgba(0, 0, 0, 0.07) 0px 4px 8px, rgba(0, 0, 0, 0.07) 0px 8px 16px, rgba(0, 0, 0, 0.07) 0px 16px 32px, rgba(0, 0, 0, 0.07) 0px 32px 64px !important;">
                <div class="flex flex-auto flex-col items-center p-8 text-center">
                    <div class="text-2xl font-semibold">Guides</div>
                    <div class="text-secondary mt-1 md:max-w-40"> Articles and resources to guide you </div>
                </div>
                <div
                    class="flex items-center justify-center bg-gray-50 px-8 py-4 text-primary-500 dark:border-t dark:bg-transparent dark:text-primary-400">
                    <a class="flex items-center cursor-pointer" (click)="onNavigate('help-center/guide')"><span
                            class="absolute inset-0"></span><span class="font-medium">Check guides</span>
                    </a>
                </div>
            </div> -->
            <div class="bg-card relative flex flex-col overflow-hidden rounded-2xl shadow transition-shadow duration-150 ease-in-out hover:shadow-lg"
                style="box-shadow: rgba(0, 0, 0, 0.07) 0px 1px 2px, rgba(0, 0, 0, 0.07) 0px 2px 4px, rgba(0, 0, 0, 0.07) 0px 4px 8px, rgba(0, 0, 0, 0.07) 0px 8px 16px, rgba(0, 0, 0, 0.07) 0px 16px 32px, rgba(0, 0, 0, 0.07) 0px 32px 64px !important;">
                <div class="flex flex-auto flex-col items-center p-8 text-center">
                    <div class="text-2xl font-semibold">Support</div>
                    <div class="text-secondary mt-1 md:max-w-40"> Contact us for more detailed support </div>
                </div>
                <div
                    class="flex items-center justify-center bg-gray-50 px-8 py-4 text-primary-500 dark:border-t dark:bg-transparent dark:text-primary-400">
                    <a class="flex items-center cursor-pointer" (click)="onNavigate('help-center/support')"><span
                            class="absolute inset-0"></span><span class="font-medium">Contact us</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>