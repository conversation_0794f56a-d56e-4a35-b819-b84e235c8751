import { Routes } from '@angular/router';

import { GuideComponent } from './guide/guide.component';
import { HelpCenterHomeComponent } from './help-center-home/help-center-home.component';
import { SupportComponent } from './support/support.component';



export default [
    {
        path: '',
        component: HelpCenterHomeComponent
    },
    {
        path: 'support',
        component: SupportComponent
    },
    {
        path: 'guide',
        component: GuideComponent
    },
    { path: 'not-found', loadChildren: () => import('app/modules/page-not-found/page-not-found.routes') },
    { path: '', pathMatch: 'full', redirectTo: 'not-found' },

] as Routes;