<button type="button" [class.button_disabled]="isDisabled" class="tps-button-wrapper w-full" (click)="onButtonClick()"
    [disabled]="isDisabled">
    <img *ngIf="isSvg" style="width:18px;height:18px" src="assets/svg/{{icon}}.svg">

    <i *ngIf="icon && iconPos =='left' && !isSvg" class="pi {{icon}}" style="font-size: 1rem;color:white"></i>
    <span style="color:white !important;">
        {{buttonName}}
    </span>
    <i *ngIf="icon && iconPos =='right' && !isSvg" class="pi {{icon}}" style="font-size: 1rem;color:white"></i>

</button>