import { Ng<PERSON><PERSON>, NgIf } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'tps-primary-button',
  standalone: true,
  imports: [NgIf],
  templateUrl: './tps-primary-button.component.html',
  styleUrl: './tps-primary-button.component.css'
})
export class TpsPrimaryButtonComponent implements OnInit {
  @Input() isDisabled: boolean = false
  @Input() buttonName: string = '';
  @Input() icon: string = '';
  @Input() iconPos: string = 'left';
  @Input() isSvg: boolean = false;

  @Output() onClick: EventEmitter<any> = new EventEmitter();

  constructor() { }
  public ngOnInit(): void {

  }

  public onButtonClick(): void {
    this.onClick.emit(true);
  }
}