import { Routes } from '@angular/router';
import { AuthSignOutComponent } from 'app/modules/auth/sign-out/sign-out.component';

export default [
    {
        path     : '',
        component: AuthSignOutComponent,
    },
    { path: 'not-found', loadChildren: () => import('app/modules/page-not-found/page-not-found.routes') },
    { path: '', pathMatch: 'full', redirectTo: 'not-found' },
] as Routes;
