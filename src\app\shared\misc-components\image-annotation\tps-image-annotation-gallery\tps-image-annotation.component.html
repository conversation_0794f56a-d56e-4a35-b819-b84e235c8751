

<div class="card rounded-2xl w-full h-full flex flex-row justify-center items-center flex-wrap gap-2 p-2">
    <div class="gallery flex flex-col" *ngFor="let image of files">
        <p-image appendTo="body" styleClass="each-image" [src]="image.src" [alt]="image.name" [preview]="true"
            width="250" height="200" />
        <div class="desc flex flex-row justify-between">
            <div>{{image.name}}</div>
            <i class="pi pi-pen-to-square cursor-pointer" style="color: #517EBC;font-size: 1.25rem;"
                pTooltip="Image Annotation" tooltipPosition="bottom" (click)="onCreateAnnotateImage(image)"></i>
        </div>
    </div>
</div>