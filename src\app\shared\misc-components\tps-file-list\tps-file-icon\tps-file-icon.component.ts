import { Component } from '@angular/core';


@Component({
  selector: 'tps-file-icon',
  standalone: true,
  imports: [],
  template: "<img style='width:24px;height:24px' [alt]='' [src]=''/>",
})
export class TpsFileIconComponent {
  constructor() { }


  public fileTypeAndIcon(file): string {
    if (file) {
      let type = file?.split('.').pop();
      let icon: string;
      switch (type) {
        case 'pdf':
          icon = 'pdf';
          break;
        case 'jpg':
        case 'jpeg':
        case 'png':
          icon = 'image';
          break;
        default:
          icon = 'file';
          break;
      }
      return icon;
    } else {
      return 'file';
    }
  }
}
