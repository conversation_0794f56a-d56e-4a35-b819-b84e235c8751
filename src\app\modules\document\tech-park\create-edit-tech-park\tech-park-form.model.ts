import { FORM_CONTROL_TYPES } from 'app/shared/tapas-ui';

export const TECH_PARK_FORM_MODEL = {
    name: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Name",
        placeholder: 'Enter Tech Park Name',
        show: true,
        rules: {
            required: true,
            maxLength: 250,
        }
    },
    factory: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        options: [],
        value: "",
        label: "Factory",
        placeholder: 'Select Factory',
        show: true,
        rules: {
            required: true,
        }
    },
    vendor: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        options: [],
        value: "",
        label: "Vendor",
        placeholder: 'Select Vendor',
        show: true,
        rules: {
            required: true,
        }
    },
    goldSeal: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Gold Seal",
        placeholder: 'Enter Gold Seal',
        show: true,
        rules: {
            required: true,
            maxLength: 250,
        }
    },
}