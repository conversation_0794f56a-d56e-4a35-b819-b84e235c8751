import { CommonModule, NgF<PERSON>, NgIf } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormGroup, FormGroupDirective, ReactiveFormsModule } from '@angular/forms';
import { FORM_CONTROL_TYPES } from 'app/shared/tapas-ui';
import { SelectButtonModule } from 'primeng/selectbutton';
import { TooltipModule } from 'primeng/tooltip';

import { TpsErrorComponent } from '../tps-error/tps-error.component';



class tpsSelectedButton {
  type: string
  options: any[]
  fieldName: string
  tooltip?: string
  hint?: string
  value: any
  label: string
  placeholder: string
  show: boolean
  onChange: (any) => void
  rules: {
    required?: boolean,
    step: 1
  }
}

@Component({
  selector: 'tps-selected-button',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, NgIf, TpsErrorComponent, SelectButtonModule, TooltipModule],
  templateUrl: './tps-selected-button.component.html',
})
export class TpsSelectedButtonComponent implements OnInit {
  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  @Input() field: tpsSelectedButton;
  @Input() formName: FormGroup;

  constructor(private formGroupDirective: FormGroupDirective) {
    this.formName = formGroupDirective.control;
  }

  public ngOnInit(): void {
  }
}
