import { CommonModule, NgF<PERSON>, NgIf } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormGroup, FormGroupDirective, ReactiveFormsModule } from '@angular/forms';
import { CheckboxModule } from 'primeng/checkbox';
import { TooltipModule } from 'primeng/tooltip';

import { FORM_CONTROL_TYPES } from '../../constants/tps-enum-constants';
import { TpsErrorComponent } from '../tps-error/tps-error.component';

class tpsCheckbox {
  type: string
  fieldName: string
  tooltip?: string
  hint?: string
  value: any
  label: string
  placeholder: string
  show: boolean
  showLabel: boolean
  onChange: (any) => void
  rules: {
    required?: boolean,
  }
}
@Component({
  selector: 'tps-checkbox',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, NgIf, TpsErrorComponent, CheckboxModule, TooltipModule],
  templateUrl: './tps-checkbox.component.html'
})
export class TpsCheckboxComponent implements OnInit {
  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  @Input() field: tpsCheckbox;
  @Input() formName: FormGroup;
  @Output() onChange: EventEmitter<any> = new EventEmitter();

  constructor(private formGroupDirective: FormGroupDirective) {
    this.formName = formGroupDirective.control;
  }

  ngOnInit(): void {
  }
  public onChangeField(event, field): void {
    this.onChange.emit(event);
  }
}
