import { Component, OnDestroy, OnInit } from '@angular/core';
import { FormGroup, Validators } from '@angular/forms';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { CountryList } from 'app/core/common/country';
import { countryList } from 'app/core/common/countryCode';
import { Util } from 'app/core/common/util';
import { Vendor } from 'app/core/models/vendor.model';
import { SharedModule } from 'app/shared/shared.module';
import {
  CommonService,
  FORM_CONTROL_TYPES,
  FORM_FIELDS_CONSTANTS_VALUES,
  FormFactoryService,
  HTTP_STATUS,
  InvokeService,
  TABLE_ACTION_TYPES,
} from 'app/shared/tapas-ui';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { ReplaySubject, takeUntil } from 'rxjs';

import { VENDOR_FORM_MODEL } from './vendor-form.model';

@Component({
  selector: 'tps-create-edit-vendor',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './create-edit-vendor.component.html'
})
export class CreateEditVendorComponent implements OnInit, OnDestroy {
  public hdr: string = 'Create Vendor';
  public vendorForm: FormGroup;
  public fields: any[] = [];
  public rec: Vendor = new Vendor();
  isMobile: boolean = false;
  ctryList: CountryList;

  //constants
  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  public TABLE_ACTION_TYPES = TABLE_ACTION_TYPES;
  public status: string;

  //unsubscribe
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);

  constructor(
    private _commonService: CommonService,
    private _dialogConfig: DynamicDialogConfig,
    public _dialogConfigRef: DynamicDialogRef,
    private _formFactoryService: FormFactoryService,
    private _invokeService: InvokeService,
  ) { }

  ngOnInit(): void {
    // Monitor screen size changes
    this._commonService.isMobile().subscribe(result => {
      this.isMobile = result.matches;
    });

    this.status = this._dialogConfig.data?.status;

    // Load record data first if available
    if (this._dialogConfig.data?.data) {
      this.rec = this._dialogConfig.data.data;
      console.log('ngOnInit - Loaded record:', this.rec);
    }

    this.buildForm();
    this.configureForm();
  }

  //build form
  private buildForm() {
    // Initialize country list
    this.ctryList = new CountryList();

    // Clone the form model to avoid modifying the original
    const formModel = Util.clone(VENDOR_FORM_MODEL);

    // Set country options from CountryList (already sorted in constructor)
    formModel.country.options = this.ctryList.countries;

    // Add onChange handler for country field to update coordinates
    formModel.country.onChange = (event: any) => {
      this.onCountryChange(event.value);
    };

    const formGroupFields = this._formFactoryService.getFormControlsFields(formModel);
    this.fields = this._formFactoryService.getFieldsList(formModel);
    this.vendorForm = new FormGroup(formGroupFields);

    // Set default validation for contact number (international format)
    this.updateContactNumberValidation('Other');
  }

  //configure form
  private configureForm(): void {
    switch (this.status) {
      case TABLE_ACTION_TYPES.CREATE:
        break;
      case TABLE_ACTION_TYPES.EDIT:
        this.hdr = "Update Vendor";
        this.setDataToForm();
        this.vendorForm.controls['code'].disable();
        break;
      case TABLE_ACTION_TYPES.VIEW:
        this.hdr = "View Vendor";
        this.setDataToForm();
        this.vendorForm.disable();
        break;
      default:
        break;
    }
  }

  // Handle country selection change
  public onCountryChange(selectedCountry: string): void {
    this.ctryList.setCountry(selectedCountry);
    this.updateContactNumberValidation(selectedCountry);
  }

  // Get country dial code from countryList
  private getCountryDialCode(countryName: string): string {
    const country = countryList.find(c => c.name === countryName);
    return country ? country.dial_code : '+1';
  }

  // Check if selected country is India
  private isIndiaSelected(countryName: string): boolean {
    return countryName === 'India';
  }

  // Update contact number validation based on selected country
  private updateContactNumberValidation(selectedCountry: string): void {
    const contactControl = this.vendorForm.get('contactNo');
    if (!contactControl) return;

    // Store current value to preserve it if it's valid for the new country
    const currentValue = contactControl.value;

    // Clear validators first
    contactControl.clearValidators();

    if (this.isIndiaSelected(selectedCountry)) {
      // For India: 10 digits, starting with 6-9
      contactControl.setValidators([
        Validators.pattern(FORM_FIELDS_CONSTANTS_VALUES.INDIA_PHONE_PATTERN),
        Validators.minLength(10),
        Validators.maxLength(10)
      ]);

      // Update placeholder
      const contactField = this.fields.find(field => field.fieldName === 'contactNo');
      if (contactField) {
        contactField.placeholder = 'Enter 10-digit mobile number (e.g., 9876543210)';
      }

      // Clear value if it contains country code (starts with +)
      if (currentValue && currentValue.startsWith('+')) {
        contactControl.setValue('');
      }
    } else {
      // For other countries: country code + number
      const dialCode = this.getCountryDialCode(selectedCountry);
      contactControl.setValidators([
        Validators.pattern(FORM_FIELDS_CONSTANTS_VALUES.INTERNATIONAL_PHONE_PATTERN),
        Validators.minLength(8),
        Validators.maxLength(15)
      ]);

      // Update placeholder with country code
      const contactField = this.fields.find(field => field.fieldName === 'contactNo');
      if (contactField) {
        contactField.placeholder = `Enter number with country code (e.g., ${dialCode}1234567890)`;
      }

      // Pre-fill with country code if field is empty or doesn't start with +
      if (!currentValue || !currentValue.startsWith('+')) {
        contactControl.setValue(dialCode);
      } else if (currentValue.startsWith('+') && !currentValue.startsWith(dialCode)) {
        // If current value has a different country code, replace it
        contactControl.setValue(dialCode);
      }
    }

    // Update validators and trigger validation
    contactControl.updateValueAndValidity();
  }

  private setDataToForm(): void {
    this.rec = this._dialogConfig.data?.data;
    this.vendorForm = this._formFactoryService.setFormControlsValues(this.vendorForm, this.rec, this.fields);

    // Apply country-specific validation if country is already set
    if (this.rec.country) {
      this.updateContactNumberValidation(this.rec.country);
    }
  }

  //submit form
  public onSubmit() {
    if (this.isMobile) {
      // Add any mobile-specific handling here
    }
    this.rec = this._formFactoryService.mapFormFieldsDataToModel(this.vendorForm, this.rec, this.fields);

    if (this.rec.uuid) {
      this._commonService.confirm(`Are you sure you want to update a vendor`, this.rec.fullName || this.rec.firstName)
        .pipe(takeUntil(this.$destroyed))
        .subscribe(response => {
          if (response) {
            APP_UI_CONFIG.admin.vendors.update.paramList.vendorUid = this.rec.uuid;
            this._invokeService.serviceInvocation(APP_UI_CONFIG.admin.vendors.update, '', Util.clone(this.rec))
              .pipe(takeUntil(this.$destroyed))
              .subscribe({
                next: response => {
                  if (response.code == HTTP_STATUS.SUCCESS) {
                    this._commonService.success(`Vendor updated successfully`);
                    this.close(true);
                  } else if (response.code == 400) {
                    this._commonService.error(response.message);
                  }
                  else {
                    this._commonService.error('Failed to update the vendor details');
                  }
                }, error: error => {
                  this._commonService.handleError(error);
                }
              })
          }
        })
    } else {
      this._commonService.confirm(`Are you sure you want to create a vendor`, this.rec.fullName || this.rec.firstName)
        .pipe(takeUntil(this.$destroyed))
        .subscribe(response => {
          if (response) {
            this._invokeService.serviceInvocation(APP_UI_CONFIG.admin.vendors.create, '', Util.clone(this.rec))
              .pipe(takeUntil(this.$destroyed))
              .subscribe({
                next: response => {
                  if (response.code == HTTP_STATUS.SUCCESS) {
                    this._commonService.success(`New vendor created successfully`);
                    this.close(true);
                  } else if (response.code == 400) {
                    this._commonService.error(response.message);
                  }
                  else {
                    this._commonService.error('Failed to create the vendor details');
                  }
                }, error: error => {
                  this._commonService.handleError(error);
                }
              })
          }
        })
    }
  }

  //close modal
  public close(status) {
    this._dialogConfigRef.close(status);
  }

  public ngOnDestroy(): void {
    this.$destroyed.next(null);
    this.$destroyed.complete();
  }
}
