<div class="fuse-horizontal-navigation-wrapper">

    <ng-container *ngFor="let item of navigation">

        <!-- Skip the hidden items -->
        <ng-container>

            <!-- Basic -->
            <fuse-horizontal-navigation-basic-item [hidden]="item.hidden" class="fuse-horizontal-navigation-menu-item"
                [disabled]="item.disabled" *ngIf="item.type === 'basic'" [item]="item" [name]="name"
                (onClick)="onClickMenu($event)">
            </fuse-horizontal-navigation-basic-item>

            <!-- Branch: aside, collapsable, group -->
            <fuse-horizontal-navigation-branch-item [hidden]="item.hidden" class="fuse-horizontal-navigation-menu-item"
                [disabled]="item.disabled" (onClick)="onClickMenu($event)"
                *ngIf="item.type === 'aside' || item.type === 'collapsable' || item.type === 'group'" [item]="item"
                [name]="name"></fuse-horizontal-navigation-branch-item>

            <!-- Spacer -->
            <fuse-horizontal-navigation-spacer-item [hidden]="item.hidden" class="fuse-horizontal-navigation-menu-item"
                (onClick)="onClickMenu($event)" [disabled]="item.disabled" *ngIf="item.type === 'spacer'" [item]="item"
                [name]="name">
            </fuse-horizontal-navigation-spacer-item>

        </ng-container>

    </ng-container>

</div>