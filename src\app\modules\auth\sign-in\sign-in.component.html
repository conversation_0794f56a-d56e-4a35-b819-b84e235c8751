<div class="w-full h-full flex flex-row flex-wrap">
    <!-- Left side - Hide on small screens -->
    <div class="col-12 col-md-7 col-lg-7 h-full left_side_sign_in_wrapper d-none d-md-block" style="z-index: 0;">
        <img src="assets/svg/sign_in_circle_pattern.svg" alt="Circle Pattern" class="left_side_top_pattern">
        <img src="assets/svg/circle_pattern.svg" alt="Circles Pattern" class="right_middle_side_top_pattern">
        <div class="row  w-full h-full">
            <div class="z-10 relative w-full">
                <div class="sign_main_title">
                    <div class="welcome-text">
                        <div class="signIn-label">Welcome to</div>
                    </div>
                    <div class="ml-5 portal_title">
                        <div class="main-label">{{projectTile}}</div>
                    </div>
                    <div class="slogan_text">
                        {{projectSubTitle}}

                    </div>
                </div>
                <div class="hyperlinks flex flex-col gap-4 ml-4">
                    <ul class="flex flex-row  items-center contact-us">
                        <li class=" mr-14 ml-4 cursor-pointer" (click)="onOpenTerms('https://www.3frameslab.com/')">
                            Terms</li>
                        <li class=" mr-14 cursor-pointer" (click)="onOpenTerms('https://www.3frameslab.com/')">
                            Privacy</li>
                        <li class=" mr-9 cursor-pointer" (click)="onOpenTerms('https://www.3frameslab.com/contact')">
                            Contact Us</li>
                    </ul>
                    <div class="copy-right flex flex-row item-center gap-2">
                        <span class="copyright-symbol">&copy;</span> <span> {{currentYear}}
                        </span><span class="ml-1">{{copyrights}}</span>
                    </div>
                </div>
            </div>
        </div>
        <img src="assets/svg/sign_in_square_pattern.svg" alt="Square Pattern" class="right_side_bottom_pattern">
    </div>
    <!-- <div class="col-md-8 col-lg-8 d-none d-sm-block d-sm-none d-md-block h-full left_side_sign_in_wrapper">
        <div class="w-full flex flex-row flex-wrap justify-between mt-3">
            <img [src]="sign_partner_logo" alt="Partner Logo" class="left_side_top_partner">
            <img [src]="company_logo" alt="Company Logo" class="right_side_top_company">
        </div>
        <div class="row  w-full h-full">
            <div class="z-10 relative w-full">
            </div>
        </div>
    </div> -->
    <div class="col-12 col-md-5 col-lg-5 h-full sign-in-right-panel">
        <div class="w-full h-full flex flex-col justify-center items-center p-4">
            <!-- Mobile Project Title - Show only on small screens -->
            <div class="d-md-none w-full mt-8">
                <div class="flex flex-col items-center text-center">
                    <!-- <div class="welcome-text mb-2">
                        <div class="text-xl font-semibold text-primary-600">Welcome to</div>
                    </div> -->
                    <div class="project-title mb-2">
                        <h1 class="text-4xl font-bold text-primary-800">{{projectTile}}</h1>
                    </div>
                    <div class="project-subtitle">
                        <p class="text-xl text-primary-600 italic">{{projectSubTitle}}</p>
                    </div>
                </div>
            </div>

            <div class="w-full flex flex-col justify-center items-center sing_in_box">
                <!-- Logo - Responsive size -->
                <div class="flex justify-center w-full mb-4">
                    <img class="responsive-logo" alt="logo" [src]="headerLogo">
                </div>
                <div class="w-full flex flex-col justify-center items-center" *ngIf="!showOTP">
                    <!-- Welcome text -->
                    <div class="flex flex-col justify-center gap-1 mb-4">
                        <div class="flex justify-center signIn-text">Welcome</div>
                        <div class="flex justify-center signIn_text_sub_title"></div>
                    </div>

                    <!-- Alert -->
                    <p-message *ngIf="showAlert" [severity]="alert[0].severity"
                        class="w-full">{{alert[0].detail}}</p-message>
                    <!-- Sign in form -->

                    <form class="w-full mt-4" [formGroup]="signInForm">
                        <div class="flex flex-column gap-2 w-full mb-4">
                            <p-inputgroup>
                                <p-inputgroup-addon>
                                    <i class="pi pi-user"></i>
                                </p-inputgroup-addon>
                                <input pInputText id="username" placeholder="Enter username" class="w-full input-height"
                                    style="max-height:40px !important" aria-describedby="username-help"
                                    formControlName="username" required />
                            </p-inputgroup>

                            <!-- <p-inputGroup>
                                <p-inputgroup-addon>
                                    <i class="pi pi-user"></i>
                                </p-inputgroup-addon>
                                <input pInputText id="username" placeholder="Enter username" class="w-full input-height"
                                    style="max-height:40px !important" aria-describedby="username-help"
                                    formControlName="username" required />
                            </p-inputGroup> -->
                            <div
                                *ngIf="signInForm.controls['username'].invalid && (signInForm.controls['username'].dirty || signInForm.controls['username'].touched)">

                                <p-message *ngIf="signInForm.get('username').hasError('required')" severity="error"
                                    variant="simple">Username is
                                    required.</p-message>
                                <p-message *ngIf="signInForm.get('username').hasError('username')" severity="error"
                                    variant="simple">Please enter a valid email address.</p-message>
                            </div>
                        </div>
                        <div class="flex flex-column gap-2 w-full">
                            <p-inputgroup>
                                <p-inputgroup-addon>
                                    <i class="pi pi-lock"></i>
                                </p-inputgroup-addon>
                                <p-password formControlName="password" class="w-full input-height"
                                    placeholder="Enter password" [toggleMask]="signInForm.controls['password'].valid"
                                    [feedback]="false" required />
                            </p-inputgroup>
                            <p-message
                                *ngIf="signInForm.controls['password'].invalid && (signInForm.controls['password'].dirty || signInForm.controls['password'].touched)"
                                severity="error" variant="simple">Password is
                                required.</p-message>
                        </div>
                        <div class="mt-16">
                            <button class="tps-button-wrapper w-full" style="height: 40px;max-height: 40px;"
                                [disabled]="signInForm.disabled" (click)="getTenantByUser()">
                                <span *ngIf="!signInForm.disabled" style="font-weight: 400;font-size: 16px;">
                                    Sign In
                                </span>
                            </button>
                        </div>
                        <div class="flex flex-row justify-center items-center  mt-5 ">
                            <!-- <img style="width:3.5rem;height:4.5rem;" src="assets/images/logo/testlogo-small.png"
                                alt="logo"> -->
                            {{projectPoweredBy}}
                        </div>
                    </form>
                </div>

                <div class="w-full opt-wrapper" *ngIf="showOTP">
                    <div *ngIf="showRegister" class="flex flex-col gap-2">
                        <div class="otp-register_title">
                            Register Now
                        </div>
                        <div class="flex flex-col gap-1">
                            <div>1.Please install Authenticator App in your mobile(<span>Click <a target="_blank"
                                        href="https://play.google.com/store/apps/details?id=com.azure.authenticator&hl=en&gl=US">here</a>
                                    to install</span>)</div>
                            <div>2.Scan QR Code through Authenticator App</div>
                            <div>3.Enter MFA Code </div>
                            <div>4.Click on verify</div>
                        </div>
                        <div class="flex flex-row justify-center">
                            <img [src]="qrCodeImageUrl" alt="" style="width:150px;height:150px">
                        </div>
                    </div>
                    <div class="mb-2">
                        <i class="pi pi-mobile" style="font-size: 2rem"></i>

                    </div>
                    <div class="otp-title-wrapper">
                        <div class="otp-title">
                            MFA Verification
                        </div>
                        <div>
                            Please enter the verification code
                        </div>
                    </div>
                    <div class="opt-input-wrapper w-full">
                        <div class="flex flex-column gap-2 w-full">
                            <p-inputOtp class="input-height" [(ngModel)]="authenticationCode" [length]="6" />
                        </div>
                    </div>
                    <div class="w-full opt-action-wraper">
                        <button class="bg-primary-button w-full mrt-signin mt-3 login-btn-color"
                            [class.button_disabled]="(authenticationCode?.length !=6)"
                            [disabled]="(authenticationCode?.length !=6)"
                            style="height:32px;font-weight: 400; cursor: pointer;" (click)="onOtpVerification()">Verify
                            Code</button>
                        <div class="mt-2">
                            Open your Two-Factor Authenticator (TOTP) app to view your
                            Authentication Code.
                        </div>
                        <div *ngIf="!showRegister" style="font-weight: bold;">
                            Don't have an MFA code? <span style="color:#517EBC;cursor:pointer;align-items:center;"
                                (click)="registerMfaAuthentication()"> Register now.</span>
                        </div>
                    </div>
                </div>
                <!-- <div class="signin_footer w-full flex flex-row justify-between items-center ">
                    <div class="flex flex-row items-center gap-6">
                        <div style="color:#517EBC;cursor: pointer;"
                            (click)="onOpenTerms('https://www.3frameslab.com/')">
                            User Manual
                        </div>
                        <div style="color:#517EBC;cursor: pointer;"
                            (click)="onOpenTerms('https://www.3frameslab.com/')">
                            Privacy Policy
                        </div>
                    </div>

                    <div class="version-number flex flex-col justify-center items-center">
                        <div style="color:#000;font-weight: 6000;">
                            Version {{currentApplicationVersion}}
                        </div>
                        <div style="color:#517EBC;cursor: pointer;"
                            (click)="onOpenTerms('https://www.3frameslab.com/')">
                            Release Notes
                        </div>
                    </div>
                </div> -->

            </div>
        </div>
    </div>
</div>



<div class="overlay" *ngIf="loading">
    <div class="overlay__inner">
        <div class="overlay__content">
            <span class="spinner"></span>
            <div class="spinner-text mt-4">Loading...</div>
        </div>
    </div>
</div>