#videoPlayer{
  width: 100%;
  background: black;
  position: -webkit-sticky; /* Safari */
  position: sticky;
  top: 15px;
  margin-bottom: -6px;

}
.videoPlayer{
  width: 100%;
  background: black;
  position: -webkit-sticky; /* Safari */
  position: sticky;
  top: 15px;
  margin-bottom: -6px;

}
.btn-group.stream{
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
.btn-group.stream .btn{
  flex: 1 0 30%;
}
.btn-group.stream .input-group-prepend{
  flex: 1 0 30%;
}
.img-wrapper{
  width: 100%;
  height: 180px;
  background: black;
  cursor: pointer;
}
.img-wrapper img{
  width: 100%;
  height: 100%;
}
.text-wrapper{
  padding: 10px;
}

@media (min-width: 576px){
.card-columns {
    -webkit-column-count: 4;
    -moz-column-count: 4;
    column-count: 4;
    -webkit-column-gap: 1.25rem;
    -moz-column-gap: 1.25rem;
    column-gap: 1.25rem;
    orphans: 1;
    widows: 1;
}
}
@media (min-width: 576px){
  .card-column {
      column-count: 2;
    }
}
@media (min-width: 768px){
  .card-column {
      column-count: 4;
    }
}
@media (min-width: 1920px){
  .card-column {
      column-count: 6;
    }
}


.grid-wrapper{
  height: 80vh;
    margin: -1px;
    background: #000;
    display: flex;
    flex-wrap: wrap;
}
:-webkit-full-screen .grid-wrapper{/*WebKit, Opera 15+*/
  height: 100vh;
  width: 100vw;
}
:-moz-full-screen .grid-wrapper{/*FireFox*/
  height: 100vh;
  width: 100vw;
}
:full-screen .grid-wrapper{/*Opera 12.15-, Blink, w3c standard*/
  height: 100vh;
  width: 100vw;
}
.grid-wrapper .player{
  height: 50%;
  width: 50%;
  position: relative;
  padding: 2px;
}
.grid-wrapper .player.grid-1{
  width: 100%;
  height: 100%;
}
.grid-wrapper .player.grid-6{
  width: 33.33%;
}
.grid-wrapper .player.grid-9{
  width: 33.33%;
  height: 33.33%;
}
.grid-wrapper .player.grid-12{
  width: 25%;
  height: 33.33%;
}
.grid-wrapper .player.grid-16{
  width: 25%;
  height: 25%;
}
.grid-wrapper .player.grid-25{
  width: 20%;
  height: 20%;
}
.grid-wrapper .player.grid-36{
  width: 16.66%;
  height:  16.66%;
}
.grid-wrapper .player.grid-49{
  width: 14.285%;
  height:  14.29%;
}
.grid-wrapper .player video{
  height: 100%;
  width: 100%;
  background: #343a40;
  margin-bottom: -6px;
}
.grid-wrapper .player .play-info{
  position: absolute;
  padding: 10px;
  text-align: center;
  color: #fff;
  top:0;
  left: 0;
  width: 100%;
}
.grid-wrapper .player .control{
  width: 100%;
    position: absolute;
    bottom: 0;
    padding: 10px;
    text-align: center;
}
.grid-wrapper .player .control .btn{
  margin: 0 0 10px 10px;
}

.card .stream-name{
  position: absolute;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
}
.card .stream-name .card-title{
  color: #FFF;
    width: 100%;
    filter: drop-shadow(1px 0px 1px black);
    font-size: 30px;
}
.one-line-header{
  white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 95%;
}
.main-player-wrapper{
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgb(0 0 0 / 0.8);
  z-index: 1100;
  padding: 15px;
}
.main-player-wrapper a{
  color: #fff;
    position: absolute;
    right: 5px;
    top: 5px;
    font-size: 30px;
    padding: 0 12px;
    border-radius: 50px;
    box-shadow: 0px 0px 10px -1px #fff;
    cursor: pointer;
}
.main-player-wrapper a:hover{
  color: #dc3545;
  box-shadow: 0px 0px 10px -1px #dc3545;
}
.main-player-wrapper .main-player{
  width: 100%;
  height: 100%;
  position: relative;
}
.main-player-wrapper .main-player video{
  width: 100%;
  height: 100%;
  background: #000;
}
.main-player-wrapper .main-player .play-info{
  position: absolute;
    padding: 10px;
    text-align: center;
    color: #fff;
    top: 0;
    width: 100%;
}
.carousel-caption{
  pointer-events: none;
}
.fix-height{
  object-fit: fill;
  aspect-ratio: 16/9;
}
