$font-family: 'Source Sans Pro';
$font-style: normal;

.qinspect-font-family {
    font-family: 'Source Sans Pro' !important;
}

.qinspect-font-style {
    font-style: normal !important;
}

.font-weight-600 {
    font-weight: 600 !important;
}

.font-size-16 {
    font-size: 14px !important;
}

.font-size-14 {
    font-size: 14px !important;
}

.line-height-20 {
    line-height: 20px !important;
}

.text-primary-100 {
    color: #D6E4FF !important;
}

.text-primary-200 {
    color: #ADC8FF !important;
}

.text-primary-300 {
    color: #84A9FF !important;
}

.text-primary-400 {
    color: #6690FF !important;
}

.text-primary-500 {
    color: #3366FF !important;
}

.text-primary-600 {
    color: #254EDB !important;
}

.text-primary-700 {
    color: #1939B7 !important;
}

.text-primary-800 {
    color: #102693 !important;
}

.text-primary-900 {
    color: #091A7A !important;
}

.bg-primary-100 {
    background-color: #FEEDE2 !important;
}

.bg-primary-200 {
    background-color: #FDD6C5 !important;
}

.bg-primary-300 {
    background-color: #F9BAA7 !important;
}

.bg-primary-400 {
    background-color: #F49E8F !important;
}

.bg-primary-500 {
    background-color: #ED736B !important;
}

.bg-primary-600 {
    background-color: #CB4F51 !important;
}

.bg-primary-700 {
    background-color: #A93542 !important;
}

.bg-primary-800 {
    background-color: #892235 !important;
}

.bg-primary-900 {
    background-color: #71152E !important;
}



.text-neutral-100 {
    color: #FFFFFF !important;
}

.text-neutral-200 {
    color: #F8F9FD !important;
}

.text-neutral-300 {
    color: #D0D7E2 !important;
}

.text-neutral-400 {
    color: #A8AFBD !important;
}

.text-neutral-500 {
    color: #7B8699 !important;
}

.text-neutral-600 {
    color: #59667A !important;
}

.text-neutral-700 {
    color: #384353 !important;
}

.text-neutral-800 {
    color: #192638 !important;
}

.text-neutral-900 {
    color: #0F1925 !important;
}

.bg-neutral-100 {
    background-color: #FFFFFF !important;
}

.bg-neutral-200 {
    background-color: #F8F9FD !important;
}

.bg-neutral-300 {
    background-color: #D0D7E2 !important;
}

.bg-neutral-400 {
    background-color: #A8AFBD !important;
}

.bg-neutral-500 {
    background-color: #7B8699 !important;
}

.bg-neutral-600 {
    background-color: #59667A !important;
}

.bg-neutral-700 {
    background-color: #384353 !important;
}

.bg-neutral-800 {
    background-color: #192638 !important;
}

.bg-neutral-900 {
    background-color: #0F1925 !important;
}



.text-success-100 {
    color: #EFFEE1 !important;
}

.text-success-200 {
    color: #DCFAC4 !important;
}

.text-success-300 {
    color: #C0F3A4 !important;
}

.text-success-400 {
    color: #A6E789 !important;
}

.text-success-500 {
    color: #7DD863 !important;
}

.text-success-600 {
    color: #39AC28 !important;
}

.text-success-700 {
    color: #3A9B30 !important;
}

.text-success-800 {
    color: #1F7D1F !important;
}

.text-success-900 {
    color: #136719 !important;
}

.bg-success-100 {
    background-color: #EFFEE1 !important;
}

.bg-success-200 {
    background-color: #DCFAC4 !important;
}

.bg-success-300 {
    background-color: #C0F3A4 !important;
}

.bg-success-400 {
    background-color: #A6E789 !important;
}

.bg-success-500 {
    background-color: #7DD863 !important;
}

.bg-success-600 {
    background-color: #58B948 !important;
}

.bg-success-700 {
    background-color: #3A9B30 !important;
}

.bg-success-800 {
    background-color: #1F7D1F !important;
}

.bg-success-900 {
    background-color: #136719 !important;
}


.text-warning-100 {
    color: #FEF6E1 !important;
}

.text-warning-200 {
    color: #FFEAC3 !important;
}

.text-warning-300 {
    color: #FFDCA6 !important;
}

.text-warning-400 {
    color: #FFCD90 !important;
}

.text-warning-500 {
    color: #FFB56C !important;
}

.text-warning-600 {
    color: #DB8D4D !important;
}

.text-warning-700 {
    color: #B66935 !important;
}

.text-warning-800 {
    color: #934922 !important;
}

.text-warning-900 {
    color: #7A3315 !important;
}

.bg-warning-100 {
    background-color: #FEF6E1 !important;
}

.bg-warning-200 {
    background-color: #FFEAC3 !important;
}

.bg-warning-300 {
    background-color: #FFDCA6 !important;
}

.bg-warning-400 {
    background-color: #FFCD90 !important;
}

.bg-warning-500 {
    background-color: #FFB56C !important;
}

.bg-warning-600 {
    background-color: #DB8D4D !important;
}

.bg-warning-700 {
    background-color: #B66935 !important;
}

.bg-warning-800 {
    background-color: #934922 !important;
}

.bg-warning-900 {
    background-color: #7A3315 !important;
}



.text-danger-100 {
    color: #FFE9DE !important;
}

.text-danger-200 {
    color: #FECFBD !important;
}

.text-danger-300 {
    color: #FEAF9E !important;
}

.text-danger-400 {
    color: #FF9085 !important;
}

.text-danger-500 {
    color: #FE5E5E !important;
}

.text-danger-600 {
    color: #DB4551 !important;
}

.text-danger-700 {
    color: #B72F47 !important;
}

.text-danger-800 {
    color: #931D3D !important;
}

.text-danger-900 {
    color: #7A1235 !important;
}

.bg-danger-100 {
    background-color: #FFE9DE !important;
}

.bg-danger-200 {
    background-color: #FECFBD !important;
}

.bg-danger-300 {
    background-color: #FEAF9E !important;
}

.bg-danger-400 {
    background-color: #FF9085 !important;
}

.bg-danger-500 {
    background-color: #FE5E5E !important;
}

.bg-danger-600 {
    background-color: #DB4551 !important;
}

.bg-danger-700 {
    background-color: #B72F47 !important;
}

.bg-danger-800 {
    background-color: #931D3D !important;
}

.bg-danger-900 {
    background-color: #7A1235 !important;
}

.bg-primary-button {
    font-family: 'Source Sans Pro' !important;
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    line-height: 20px;
    text-align: center;
    background: #ED736B;
    border-radius: 6px;
    height: 40px;
    color: white;
}

.tfl-elements-gap-4 {
    gap: 4px;
}

.tfl-elements-gap-8 {
    gap: 4px;
}

.tfl-elements-gap {
    gap: 24px
}

.header-height {
    height: 72px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.qinspectform-label {
    font-family: 'Source Sans Pro';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #384353;
}

.p-select-label {
    font-size: 14px !important;
    font-family: 'Source Sans Pro' !important;
    margin-top: -3px;
}

.title_card {
    background: white;
    padding: 5px;
    align-items: center;
}

.submitted-text {
    color: rgba(245 158 11 /1);
}

.submitted-text {
    color: rgba(245 158 11 /1);
}