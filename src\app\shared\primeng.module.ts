import { <PERSON>dk<PERSON><PERSON>, CdkDropList, CdkDropListGroup } from '@angular/cdk/drag-drop';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { AccordionModule } from 'primeng/accordion';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { AvatarModule } from 'primeng/avatar';
import { AvatarGroupModule } from 'primeng/avatargroup';
import { BadgeModule } from 'primeng/badge';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { CarouselModule } from 'primeng/carousel';
import { CheckboxModule } from 'primeng/checkbox';
import { ContextMenuModule } from 'primeng/contextmenu';
import { DatePickerModule } from 'primeng/datepicker';
import { DialogModule } from 'primeng/dialog';
import { DividerModule } from 'primeng/divider';
import { DragDropModule } from 'primeng/dragdrop';
import { FieldsetModule } from 'primeng/fieldset';
import { FileUploadModule } from 'primeng/fileupload';
import { IconFieldModule } from 'primeng/iconfield';
import { ImageModule } from 'primeng/image';
import { InputGroupModule } from 'primeng/inputgroup';
import { InputGroupAddonModule } from 'primeng/inputgroupaddon';
import { InputIconModule } from 'primeng/inputicon';
import { InputNumberModule } from 'primeng/inputnumber';
import { InputSwitchModule } from 'primeng/inputswitch';
import { InputTextModule } from 'primeng/inputtext';
import { ListboxModule } from 'primeng/listbox';
import { MenuModule } from 'primeng/menu';
import { MultiSelectModule } from 'primeng/multiselect';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { PanelModule } from 'primeng/panel';
import { PasswordModule } from 'primeng/password';
import { ProgressBarModule } from 'primeng/progressbar';
import { RadioButtonModule } from 'primeng/radiobutton';
import { ScrollTopModule } from 'primeng/scrolltop';
import { SelectModule } from 'primeng/select';
import { SelectButtonModule } from 'primeng/selectbutton';
import { SkeletonModule } from 'primeng/skeleton';
import { SliderModule } from 'primeng/slider';
import { SplitterModule } from 'primeng/splitter';
import { StepperModule } from 'primeng/stepper';
import { TableModule } from 'primeng/table';
import { TabViewModule } from 'primeng/tabview';
import { TagModule } from 'primeng/tag';
import { ToastModule } from 'primeng/toast';
import { TooltipModule } from 'primeng/tooltip';

@NgModule({
    declarations: [],
    imports: [
        CommonModule,
        SelectModule,
        ButtonModule,
        SelectButtonModule,
        MultiSelectModule,
        AutoCompleteModule,
        ListboxModule,
        AccordionModule,
        InputNumberModule,
        DividerModule,
        InputTextModule,
        DatePickerModule,
        RadioButtonModule,
        TabViewModule,
        CheckboxModule,
        TabViewModule,
        TooltipModule,
        TableModule,
        MenuModule,
        SliderModule,
        ContextMenuModule,
        DialogModule,
        SplitterModule,
        FieldsetModule,
        PasswordModule,
        InputSwitchModule,
        PanelModule,
        BadgeModule,
        AvatarModule,
        AvatarGroupModule,
        ImageModule,
        SkeletonModule,
        CarouselModule,
        StepperModule,
        CardModule,
        OverlayPanelModule,
        DragDropModule,
        CdkDropListGroup,
        CdkDropList,
        CdkDrag,
        FileUploadModule,
        InputGroupAddonModule,
        InputGroupModule,
        ProgressBarModule,
        ToastModule,
        IconFieldModule,
        InputIconModule,
        TagModule,
        SelectButtonModule,
        ScrollTopModule

    ],
    exports: [
        SelectModule,
        ButtonModule,
        SelectButtonModule,
        MultiSelectModule,
        AutoCompleteModule,
        ListboxModule,
        AccordionModule,
        InputNumberModule,
        DividerModule,
        InputTextModule,
        DatePickerModule,
        RadioButtonModule,
        TabViewModule,
        CheckboxModule,
        TabViewModule,
        TooltipModule,
        TableModule,
        MenuModule,
        SliderModule,
        ContextMenuModule,
        DialogModule,
        SplitterModule,
        FieldsetModule,
        PasswordModule,
        InputSwitchModule,
        PanelModule,
        BadgeModule,
        AvatarModule,
        ImageModule,
        SkeletonModule,
        AvatarGroupModule,
        CarouselModule,
        StepperModule,
        OverlayPanelModule,
        DragDropModule,
        CdkDropListGroup,
        CdkDropList,
        CdkDrag,
        FileUploadModule,
        InputGroupAddonModule,
        InputGroupModule,
        ProgressBarModule,
        ToastModule,
        IconFieldModule,
        InputIconModule,
        CardModule,
        TagModule,
        SelectButtonModule,
        ScrollTopModule

    ]
})
export class PrimeNgModule { }
