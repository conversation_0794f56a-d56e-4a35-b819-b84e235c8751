export class Auditor {
  addressLine1?: string;
  addressLine2?: string;
  altemail?: string;
  city?: string;
  code?: string;
  contactNo?: string;
  country?: string;
  createdBy?: string;
  createdTime?: number;
  deleted?: number;
  designation?: string;
  deviceImeiNumber?: string;
  email?: string;
  firstName?: string;
  fullName?: string;
  id?: number;
  inchargeUid?: string;
  lastName?: string;
  loggedOn?: string;
  mobileRegId?: string;
  modifiedBy?: string;
  modifiedTime?: number;
  parentTenantUid?: string;
  password?: string;
  pincode?: string;
  role?: string;
  state?: string;
  status?: number;
  tenantId?: number;
  tenantUid?: string;
  userName?: string;
  uuid?: string;
  setPasswordAsUserName?: boolean;
  resetPassword?: boolean;
  newPassword?: string
  inspectionAgencyUid?: string
}

