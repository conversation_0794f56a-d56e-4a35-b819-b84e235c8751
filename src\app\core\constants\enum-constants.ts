//General
export const FORM_REQUIRED_MESSAGE = "Note: Fields marked with (*) are mandatory"


export enum ROLE {
    VENDOR = "VENDOR",
    FACTORY = "FACTORY",
    AUDITOR = "AUDITOR",
    AUDITORINCHARGE = "AUDITO<PERSON><PERSON><PERSON>R<PERSON>",
    AGENCY = "AGENCY",
    SELLER = "SELLER",
    LAB = "LAB",
    SOURCING = "SOURCING",
    ADMIN = "ADMIN",
    PRODUCT_MANAGER = "PRODUCT MANAGER",
    REVIEWER = "REVIEWER",
    SUPPLY_CHAIN = "SUPPLY_CHAIN",
    PRODUCT_COMPLIANCE = "PRODUCT COMPLIANCE",
    OPERATIONS = "OPERATIONS",
    DQA = "DQA",
    SQM_OPS = "SQM_OPS",
    TEST_USER = "TEST_USER",
    SME = "SME"
}

export const ROLE_LIST = [
    { "label": ROLE.AUDITOR, value: ROLE.AUDITOR },
    { "label": ROLE.VENDOR, value: ROLE.VENDOR },
    { "label": ROLE.FACTORY, value: ROLE.FACTORY },
    { "label": ROLE.AUDITORINCHARGE, value: ROLE.AUDITORINCHARGE },
    { "label": ROLE.AGENCY, value: ROLE.AGENCY },
    { "label": ROLE.SELLER, value: ROLE.SELLER },
    { "label": ROLE.LAB, value: ROLE.LAB },
    { "label": ROLE.SOURCING, value: ROLE.SOURCING },
    { "label": ROLE.ADMIN, value: ROLE.ADMIN },
    { "label": ROLE.PRODUCT_MANAGER, value: ROLE.PRODUCT_MANAGER },
    { "label": ROLE.REVIEWER, value: ROLE.REVIEWER },
    { "label": ROLE.SUPPLY_CHAIN, value: ROLE.SUPPLY_CHAIN },
    { "label": ROLE.PRODUCT_COMPLIANCE, value: ROLE.PRODUCT_COMPLIANCE },
    { "label": ROLE.OPERATIONS, value: ROLE.OPERATIONS },
    { "label": ROLE.SQM_OPS, value: ROLE.SQM_OPS },
    { "label": ROLE.TEST_USER, value: ROLE.TEST_USER },
]


export enum HTTP_STATUS {
    SUCCESS = 200,
    UNAUTHORIZED = 401,
    NOT_CONTENT = 204,
    BAD_REQUEST = 400,
}
export enum FORM_CONTROL_TYPES {
    TEXT = "text",
    NUMBER = "number",
    SINGLE_SELECT = "single-select",
    MULTI_SELECT = "multi-select",
    RADIO = "radio",
    CHECKBOX = "checkbox",
    DATE = "date",
    VIEW = "view",
    INPUT = "input",
    TEXTAREA = "text-area",
    TEXT_SEARCH = "text-search",
    TIME_DURATION = "time-duration"
}
export enum FORM_VALIDATION_TYPES {
    REQUIRED = 'required',
    MIN = 'min',
    MAX = 'max',
    MINLENGTH = 'minLength',
    MAXLENGTH = 'maxLength',
    PATTERN = 'pattern',
}
export enum FORM_FIELDS_CONSTANTS_VALUES {
    TEXT_AREA_LENGTH = 250, // Changed from 500 to 250
    PHONE_NUMBER_LENGTH = 10,
    AADHAR_NUMBER_PATTERN = 12,
    EMAIL_PATTERN = "[a-zA-Z0-9.-_]{1,}@[a-zA-Z0-9.-]{2,}[.]{1}[a-zA-Z]{2,4}",
    NUMERIC_PATTERN = "^[0-9]+$",
    FLOAT_NUMBER_PATTERN = "^[0-9.]+$",
    ALPHA_PATTERN = "^[a-zA-Z](?:[a-zA-Z ]*[a-zA-Z])?$",
    ALPHA_NUMERIC_PATTERN_NO_SPACE = "^[a-zA-Z0-9]+$",
    ALPHA_NUMERIC_PATTERN_WITH_SPACE = "^[a-zA-Z0-9 ]+$",
    BLOOD_GROUP_PATTERN = "^[ aboveABOVE+-]+$"
}

export enum TABLE_ACTION_TYPES {
    CREATE = 'CREATE',
    VIEW = 'VIEW',
    EDIT = 'EDIT',
    DELETE = 'DELETE',
    ACTIVATE = 'ACTIVATE',
    DEACTIVATE = 'DEACTIVATE',
    APPROVE = 'APPROVE',
    ANALYTICS = 'ANALYTICS',
    PIN_DROP = 'PIN_DROP'
}
export enum API_TYPES {
    GET = "GET",
    POST = "POST",
    PUT = "PUT",
    DELETE = "DELETE"
}

export enum ACTION_ICON_COLOR {
    BLUE = "#517EBC",
    GRAY = "#7B8699",
    GREEN = "#55C938",
    RED = "#ED736B"
}

export const MQTT_TOPIC_NAMES = {
    DEDUP_FRAME: 'DEDUP_FRAME',
    THREAT_DETECT: 'monitor_test',
    FIRE_DETECT: 'FIRE_DETECT',
    ANPR_NEW_VEHICLE: 'mqtt-topic',
    INBOX: 'INBOX',
    HUMAN_ACTIVITY_DASHBOARD: 'HUMAN_ACTIVITY_DASHBOARD',
    THREAT_DETECT_HISTORICAL: 'THREAT_DETECT_HISTORICAL'
}

export const SEVERITY_LIST = [
    { label: 'Urgent', value: "1" },
    { label: 'Critical', value: "2" },
    { label: 'Major', value: "3" },
    { label: 'Minor', value: "4" },
];

export const countryList = [
    { label: 'Bangladesh', value: 'Bangladesh' },
    { label: 'China', value: 'China' },
    { label: 'Hong Kong', value: 'Hong Kong' },
    { label: 'India', value: 'India' },
    { label: 'Indonesia', value: 'Indonesia' },
    { label: 'Italy', value: 'Italy' },
    { label: 'Mauritius', value: 'Mauritius' },
    { label: 'Nepal', value: 'Nepal' },
    { label: 'Sri Lanka', value: 'Sri Lanka' },
    { label: 'Thailand', value: 'Thailand' },
    { label: 'UAE', value: 'UAE' },
    { label: 'United Kingdom', value: 'United Kingdom' },
    { label: 'Vietnam', value: 'Vietnam' },
]

