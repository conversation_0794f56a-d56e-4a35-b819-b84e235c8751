:root {
  font-family: "Source Sans Pro" !important;
  font-feature-settings: "cv02", "cv03", "cv04", "cv11" !important;
  font-variation-settings: normal !important;
  --font-family: "Source Sans Pro" !important;
  --font-feature-settings: "cv02", "cv03", "cv04", "cv11" !important;
  --text-color: #334155 !important;
  --text-color-secondary: #64748b !important;
  --primary-color: #3B82F6 !important;
  --primary-color-text: #ffffff !important;
  --surface-0: #ffffff !important;
  --surface-50: #f8fafc !important;
  --surface-100: #f1f5f9 !important;
  --surface-200: #e2e8f0 !important;
  --surface-300: #cbd5e1 !important;
  --surface-400: #94a3b8 !important;
  --surface-500: #64748b !important;
  --surface-600: #475569 !important;
  --surface-700: #334155 !important;
  --surface-800: #1e293b !important;
  --surface-900: #0f172a !important;
  --surface-950: #020617 !important;
  --gray-0: #ffffff !important;
  --gray-50: #f8fafc !important;
  --gray-100: #f1f5f9 !important;
  --gray-200: #e2e8f0 !important;
  --gray-300: #cbd5e1 !important;
  --gray-400: #94a3b8 !important;
  --gray-500: #64748b !important;
  --gray-600: #475569 !important;
  --gray-700: #334155 !important;
  --gray-800: #1e293b !important;
  --gray-900: #0f172a !important;
  --gray-950: #020617 !important;
  --content-padding: 1.125rem !important;
  --inline-spacing: 0.5rem !important;
  --border-radius: 6px !important;
  --surface-ground: #f8fafc !important;
  --surface-section: #ffffff !important;
  --surface-card: #ffffff !important;
  --surface-overlay: #ffffff !important;
  --surface-border: #e2e8f0 !important;
  --surface-hover: #f1f5f9 !important;
  --focus-ring: none !important;
  --maskbg: rgba(0, 0, 0, 0.4) !important;
  --highlight-bg: #EFF6FF !important;
  --highlight-text-color: #1D4ED8 !important;
  --p-anchor-gutter: 2px !important;
  color-scheme: light !important;
}

:root {
  --p-focus-ring-color: var(--primary-color) !important;
}

:root {
  --blue-50:#f5f9ff !important;
  --blue-100:#d0e1fd !important;
  --blue-200:#abc9fb !important;
  --blue-300:#85b2f9 !important;
  --blue-400:#609af8 !important;
  --blue-500:#3b82f6 !important;
  --blue-600:#326fd1 !important;
  --blue-700:#295bac !important;
  --blue-800:#204887 !important;
  --blue-900:#183462 !important;
  --green-50:#f4fcf7 !important;
  --green-100:#caf1d8 !important;
  --green-200:#a0e6ba !important;
  --green-300:#76db9b !important;
  --green-400:#4cd07d !important;
  --green-500:#22c55e !important;
  --green-600:#1da750 !important;
  --green-700:#188a42 !important;
  --green-800:#136c34 !important;
  --green-900:#0e4f26 !important;
  --yellow-50:#fefbf3 !important;
  --yellow-100:#faedc4 !important;
  --yellow-200:#f6de95 !important;
  --yellow-300:#f2d066 !important;
  --yellow-400:#eec137 !important;
  --yellow-500:#eab308 !important;
  --yellow-600:#c79807 !important;
  --yellow-700:#a47d06 !important;
  --yellow-800:#816204 !important;
  --yellow-900:#5e4803 !important;
  --cyan-50:#f3fbfd !important;
  --cyan-100:#c3edf5 !important;
  --cyan-200:#94e0ed !important;
  --cyan-300:#65d2e4 !important;
  --cyan-400:#35c4dc !important;
  --cyan-500:#06b6d4 !important;
  --cyan-600:#059bb4 !important;
  --cyan-700:#047f94 !important;
  --cyan-800:#036475 !important;
  --cyan-900:#024955 !important;
  --pink-50:#fef6fa !important;
  --pink-100:#fad3e7 !important;
  --pink-200:#f7b0d3 !important;
  --pink-300:#f38ec0 !important;
  --pink-400:#f06bac !important;
  --pink-500:#ec4899 !important;
  --pink-600:#c93d82 !important;
  --pink-700:#a5326b !important;
  --pink-800:#822854 !important;
  --pink-900:#5e1d3d !important;
  --indigo-50:#f7f7fe !important;
  --indigo-100:#dadafc !important;
  --indigo-200:#bcbdf9 !important;
  --indigo-300:#9ea0f6 !important;
  --indigo-400:#8183f4 !important;
  --indigo-500:#6366f1 !important;
  --indigo-600:#5457cd !important;
  --indigo-700:#4547a9 !important;
  --indigo-800:#363885 !important;
  --indigo-900:#282960 !important;
  --teal-50:#f3fbfb !important;
  --teal-100:#c7eeea !important;
  --teal-200:#9ae0d9 !important;
  --teal-300:#6dd3c8 !important;
  --teal-400:#41c5b7 !important;
  --teal-500:#14b8a6 !important;
  --teal-600:#119c8d !important;
  --teal-700:#0e8174 !important;
  --teal-800:#0b655b !important;
  --teal-900:#084a42 !important;
  --orange-50:#fff8f3 !important;
  --orange-100:#feddc7 !important;
  --orange-200:#fcc39b !important;
  --orange-300:#fba86f !important;
  --orange-400:#fa8e42 !important;
  --orange-500:#f97316 !important;
  --orange-600:#d46213 !important;
  --orange-700:#ae510f !important;
  --orange-800:#893f0c !important;
  --orange-900:#642e09 !important;
  --bluegray-50:#f7f8f9 !important;
  --bluegray-100:#dadee3 !important;
  --bluegray-200:#bcc3cd !important;
  --bluegray-300:#9fa9b7 !important;
  --bluegray-400:#818ea1 !important;
  --bluegray-500:#64748b !important;
  --bluegray-600:#556376 !important;
  --bluegray-700:#465161 !important;
  --bluegray-800:#37404c !important;
  --bluegray-900:#282e38 !important;
  --purple-50:#fbf7ff !important;
  --purple-100:#ead6fd !important;
  --purple-200:#dab6fc !important;
  --purple-300:#c996fa !important;
  --purple-400:#b975f9 !important;
  --purple-500:#a855f7 !important;
  --purple-600:#8f48d2 !important;
  --purple-700:#763cad !important;
  --purple-800:#5c2f88 !important;
  --purple-900:#432263 !important;
  --red-50:#fff5f5 !important;
  --red-100:#ffd0ce !important;
  --red-200:#ffaca7 !important;
  --red-300:#ff8780 !important;
  --red-400:#ff6259 !important;
  --red-500:#ff3d32 !important;
  --red-600:#d9342b !important;
  --red-700:#b32b23 !important;
  --red-800:#8c221c !important;
  --red-900:#661814 !important;
  --primary-50:#f5f9ff !important;
  --primary-100:#d0e1fd !important;
  --primary-200:#abc9fb !important;
  --primary-300:#85b2f9 !important;
  --primary-400:#609af8 !important;
  --primary-500:#3b82f6 !important;
  --primary-600:#326fd1 !important;
  --primary-700:#295bac !important;
  --primary-800:#204887 !important;
  --primary-900:#183462 !important;
}

.p-editor-container .p-editor-toolbar {
  background: #ffffff !important;
  border-top-right-radius: 6px !important;
  border-top-left-radius: 6px !important;
}
.p-editor-container .p-editor-toolbar.ql-snow {
  border: 1px solid #e2e8f0 !important;
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-stroke {
  stroke: #64748b !important;
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-fill {
  fill: #64748b !important;
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-picker .ql-picker-label {
  border: 0 none !important;
  color: #64748b !important;
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-picker .ql-picker-label:hover {
  color: #334155 !important;
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-picker .ql-picker-label:hover .ql-stroke {
  stroke: #334155 !important;
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-picker .ql-picker-label:hover .ql-fill {
  fill: #334155 !important;
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label {
  color: #334155 !important;
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-stroke {
  stroke: #334155 !important;
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-fill {
  fill: #334155 !important;
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options {
  background: #ffffff !important;
  border: 1px solid #e2e8f0 !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1) !important;
  border-radius: 6px !important;
  padding: 0.25rem 0.25rem !important;
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options .ql-picker-item {
  color: #334155 !important;
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options .ql-picker-item:hover {
  color: #1e293b !important;
  background: #f1f5f9 !important;
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-picker.ql-expanded:not(.ql-icon-picker) .ql-picker-item {
  padding: 0.5rem 0.75rem !important;
}
.p-editor-container .p-editor-content {
  border-bottom-right-radius: 6px !important;
  border-bottom-left-radius: 6px !important;
}
.p-editor-container .p-editor-content.ql-snow {
  border: 1px solid #e2e8f0 !important;
}
.p-editor-container .p-editor-content .ql-editor {
  background: #ffffff !important;
  color: #334155 !important;
  border-bottom-right-radius: 6px !important;
  border-bottom-left-radius: 6px !important;
}
.p-editor-container .ql-snow.ql-toolbar button:hover,
.p-editor-container .ql-snow.ql-toolbar button:focus {
  color: #334155 !important;
}
.p-editor-container .ql-snow.ql-toolbar button:hover .ql-stroke,
.p-editor-container .ql-snow.ql-toolbar button:focus .ql-stroke {
  stroke: #334155 !important;
}
.p-editor-container .ql-snow.ql-toolbar button:hover .ql-fill,
.p-editor-container .ql-snow.ql-toolbar button:focus .ql-fill {
  fill: #334155 !important;
}
.p-editor-container .ql-snow.ql-toolbar button.ql-active,
.p-editor-container .ql-snow.ql-toolbar .ql-picker-label.ql-active,
.p-editor-container .ql-snow.ql-toolbar .ql-picker-item.ql-selected {
  color: #3B82F6 !important;
}
.p-editor-container .ql-snow.ql-toolbar button.ql-active .ql-stroke,
.p-editor-container .ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke,
.p-editor-container .ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke {
  stroke: #3B82F6 !important;
}
.p-editor-container .ql-snow.ql-toolbar button.ql-active .ql-fill,
.p-editor-container .ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-fill,
.p-editor-container .ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-fill {
  fill: #3B82F6 !important;
}
.p-editor-container .ql-snow.ql-toolbar button.ql-active .ql-picker-label,
.p-editor-container .ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-picker-label,
.p-editor-container .ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-picker-label {
  color: #3B82F6 !important;
}

@layer primeng {
  * {
    box-sizing: border-box !important;
  }

  .p-component {
    font-family: var(--font-family) !important;
    font-feature-settings: var(--font-feature-settings, normal) !important;
    font-size: 1rem !important;
    font-weight: normal !important;
  }

  .p-component-overlay {
    background-color: rgba(0, 0, 0, 0.4) !important;
    transition-duration: 0.2s !important;
  }

  .p-disabled, .p-component:disabled {
    opacity: 0.6 !important;
  }

  .p-error {
    color: #f87171 !important;
  }

  .p-text-secondary {
    color: #64748b !important;
  }

  .pi {
    font-size: 1rem;
  }

  .p-icon {
    width: 1rem !important;
    height: 1rem !important;
  }

  .p-link {
    font-family: var(--font-family) !important;
    font-feature-settings: var(--font-feature-settings, normal) !important;
    font-size: 1rem !important;
    border-radius: 6px !important;
  }
  .p-link:focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }

  .p-component-overlay-enter {
    animation: p-component-overlay-enter-animation 150ms forwards !important;
  }

  .p-component-overlay-leave {
    animation: p-component-overlay-leave-animation 150ms forwards !important;
  }

  @keyframes p-component-overlay-enter-animation {
    from {
      background-color: transparent !important;
    }
    to {
      background-color: var(--maskbg) !important;
    }
  }
  @keyframes p-component-overlay-leave-animation {
    from {
      background-color: var(--maskbg) !important;
    }
    to {
      background-color: transparent !important;
    }
  }

  .p-autocomplete .p-autocomplete-loader {
    right: 0.75rem !important;
  }
  .p-autocomplete.p-autocomplete-dd .p-autocomplete-loader {
    right: 3.25rem !important;
  }
  .p-autocomplete:not(.p-disabled):hover .p-autocomplete-multiple-container {
    border-color: #94a3b8 !important;
  }
  .p-autocomplete:not(.p-disabled).p-focus .p-autocomplete-multiple-container {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: -1px !important;
    box-shadow: none !important;
    border-color: #94a3b8 !important;
  }
  .p-autocomplete .p-autocomplete-multiple-container {
    padding: 0.25rem 0.75rem !important;
    gap: 0.5rem !important;
  }
  .p-autocomplete .p-autocomplete-multiple-container .p-autocomplete-input-token {
    padding: 0.25rem 0 !important;
  }
  .p-autocomplete .p-autocomplete-multiple-container .p-autocomplete-input-token input {
    font-family: var(--font-family) !important;
    font-feature-settings: var(--font-feature-settings, normal) !important;
    font-size: 1rem !important;
    color: #334155 !important;
    padding: 0 !important;
    margin: 0 !important;
  }
  .p-autocomplete .p-autocomplete-multiple-container .p-autocomplete-token {
    padding: 0.25rem 0.75rem !important;
    background: #f1f5f9 !important;
    color: #1e293b !important;
    border-radius: 16px !important;
  }
  .p-autocomplete .p-autocomplete-multiple-container .p-autocomplete-token .p-autocomplete-token-icon {
    margin-left: 0.5rem !important;
  }
  .p-autocomplete .p-autocomplete-multiple-container .p-autocomplete-token.p-focus {
    background: #e2e8f0 !important;
    color: #0f172a !important;
  }
  .p-autocomplete.p-invalid.p-component > .p-inputtext {
    border-color: #f87171 !important;
  }

  .p-autocomplete-panel {
    background: #ffffff !important;
    color: #334155 !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 6px !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1) !important;
  }
  .p-autocomplete-panel .p-autocomplete-items {
    padding: 0.25rem 0.25rem !important;
  }
  .p-autocomplete-panel .p-autocomplete-items .p-autocomplete-item {
    margin: 2px 0 !important;
    padding: 0.5rem 0.75rem !important;
    border: 0 none !important;
    color: #334155 !important;
    background: transparent !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border-radius: 4px !important;
  }
  .p-autocomplete-panel .p-autocomplete-items .p-autocomplete-item:first-child {
    margin-top: 0 !important;
  }
  .p-autocomplete-panel .p-autocomplete-items .p-autocomplete-item.p-highlight {
    color: #1D4ED8 !important;
    background: #EFF6FF !important;
  }
  .p-autocomplete-panel .p-autocomplete-items .p-autocomplete-item.p-highlight.p-focus {
    background: rgba(59, 130, 246, 0.24) !important;
  }
  .p-autocomplete-panel .p-autocomplete-items .p-autocomplete-item:not(.p-highlight):not(.p-disabled).p-focus {
    color: #0f172a !important;
    background: #e2e8f0 !important;
  }
  .p-autocomplete-panel .p-autocomplete-items .p-autocomplete-item:not(.p-highlight):not(.p-disabled):hover {
    color: #1e293b !important;
    background: #f1f5f9 !important;
  }
  .p-autocomplete-panel .p-autocomplete-items .p-autocomplete-item-group {
    margin: 0 !important;
    padding: 0.5rem 0.75rem !important;
    color: #94a3b8 !important;
    background: #ffffff !important;
    font-weight: 600 !important;
  }
  .p-autocomplete-panel .p-autocomplete-items .p-autocomplete-empty-message {
    padding: 0.5rem 0.75rem !important;
    color: #334155 !important;
    background: transparent !important;
  }

  p-autocomplete.ng-dirty.ng-invalid > .p-autocomplete > .p-inputtext {
    border-color: #f87171 !important;
  }

  p-autocomplete.p-autocomplete-clearable .p-inputtext {
    padding-right: 2.5rem !important;
  }
  p-autocomplete.p-autocomplete-clearable .p-autocomplete-clear-icon {
    color: #94a3b8 !important;
    right: 0.75rem !important;
  }

  p-autocomplete.p-autocomplete-clearable .p-autocomplete-dd .p-autocomplete-clear-icon {
    color: #94a3b8 !important;
    right: 3.25rem !important;
  }

  p-calendar.ng-dirty.ng-invalid > .p-datepicker > .p-inputtext {
    border-color: #f87171 !important;
  }

  .p-datepicker:not(.p-datepicker-disabled).p-focus > .p-inputtext {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: -1px !important;
    box-shadow: none !important;
    border-color: #94a3b8 !important;
  }

  .p-datepicker {
    padding: 0.75rem !important;
    background: #ffffff !important;
    color: #334155 !important;
    border: 1px solid #cbd5e1 !important;
    border-radius: 6px !important;
  }
  .p-datepicker:not(.p-datepicker-inline) {
    background: #ffffff !important;
    border: 1px solid #e2e8f0 !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1) !important;
  }
  .p-datepicker:not(.p-datepicker-inline) .p-datepicker-header {
    background: #ffffff !important;
  }
  .p-datepicker .p-datepicker-header {
    padding: 0 0 0.5rem 0 !important;
    color: #334155 !important;
    background: #ffffff !important;
    font-weight: 500 !important;
    margin: 0 0 0 0 !important;
    border-bottom: 1px solid #e2e8f0 !important;
    border-top-right-radius: 6px !important;
    border-top-left-radius: 6px !important;
  }
  .p-datepicker .p-datepicker-header .p-datepicker-prev,
.p-datepicker .p-datepicker-header .p-datepicker-next {
    width: 1.75rem !important;
    height: 1.75rem !important;
    color: #64748b !important;
    border: 0 none !important;
    background: transparent !important;
    border-radius: 50% !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-datepicker .p-datepicker-header .p-datepicker-prev:enabled:hover,
.p-datepicker .p-datepicker-header .p-datepicker-next:enabled:hover {
    color: #475569 !important;
    border-color: transparent !important;
    background: #f1f5f9 !important;
  }
  .p-datepicker .p-datepicker-header .p-datepicker-prev:focus-visible,
.p-datepicker .p-datepicker-header .p-datepicker-next:focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }
  .p-datepicker .p-datepicker-header .p-datepicker-title {
    line-height: 1.75rem !important;
  }
  .p-datepicker .p-datepicker-header .p-datepicker-title .p-datepicker-year,
.p-datepicker .p-datepicker-header .p-datepicker-title .p-datepicker-month {
    color: #334155 !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    font-weight: 500 !important;
    padding: 0.25rem !important;
  }
  .p-datepicker .p-datepicker-header .p-datepicker-title .p-datepicker-year:enabled:hover,
.p-datepicker .p-datepicker-header .p-datepicker-title .p-datepicker-month:enabled:hover {
    color: #3B82F6 !important;
  }
  .p-datepicker .p-datepicker-header .p-datepicker-title .p-datepicker-month {
    margin-right: 0.5rem !important;
  }
  .p-datepicker table {
    font-size: 1rem !important;
    margin: 0.5rem 0 0 0 !important;
  }
  .p-datepicker table th {
    padding: 0.25rem !important;
  }
  .p-datepicker table th > span {
    width: 2rem !important;
    height: 2rem !important;
  }
  .p-datepicker table td {
    padding: 0.25rem !important;
  }
  .p-datepicker table td > span {
    width: 2rem !important;
    height: 2rem !important;
    border-radius: 50% !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border: 1px solid transparent !important;
  }
  .p-datepicker table td > span.p-highlight {
    color: #1D4ED8 !important;
    background: #EFF6FF !important;
  }
  .p-datepicker table td > span:focus {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }
  .p-datepicker table td.p-datepicker-today > span {
    background: #e2e8f0 !important;
    color: #0f172a !important;
    border-color: transparent !important;
  }
  .p-datepicker table td.p-datepicker-today > span.p-highlight {
    color: #1D4ED8 !important;
    background: #EFF6FF !important;
  }
  .p-datepicker .p-datepicker-buttonbar {
    padding: 0.5rem 0 0 0 !important;
    border-top: 1px solid #e2e8f0 !important;
  }
  .p-datepicker .p-datepicker-buttonbar .p-button {
    width: auto !important;
  }
  .p-datepicker .p-timepicker {
    border-top: 1px solid #e2e8f0 !important;
    padding: 0 !important;
  }
  .p-datepicker .p-timepicker button {
    width: 1.75rem !important;
    height: 1.75rem !important;
    color: #64748b !important;
    border: 0 none !important;
    background: transparent !important;
    border-radius: 50% !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-datepicker .p-timepicker button:enabled:hover {
    color: #475569 !important;
    border-color: transparent !important;
    background: #f1f5f9 !important;
  }
  .p-datepicker .p-timepicker button:focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }
  .p-datepicker .p-timepicker button:last-child {
    margin-top: 0.2em !important;
  }
  .p-datepicker .p-timepicker span {
    font-size: 1rem !important;
  }
  .p-datepicker .p-timepicker > div {
    padding: 0 0.5rem !important;
  }
  .p-datepicker.p-datepicker-timeonly .p-timepicker {
    border-top: 0 none !important;
  }
  .p-datepicker .p-monthpicker {
    margin: 0.5rem 0 0 0 !important;
  }
  .p-datepicker .p-monthpicker .p-monthpicker-month {
    padding: 0.25rem !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border-radius: 6px !important;
  }
  .p-datepicker .p-monthpicker .p-monthpicker-month.p-highlight {
    color: #1D4ED8 !important;
    background: #EFF6FF !important;
  }
  .p-datepicker .p-yearpicker {
    margin: 0.5rem 0 0 0 !important;
  }
  .p-datepicker .p-yearpicker .p-yearpicker-year {
    padding: 0.25rem !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border-radius: 6px !important;
  }
  .p-datepicker .p-yearpicker .p-yearpicker-year.p-highlight {
    color: #1D4ED8 !important;
    background: #EFF6FF !important;
  }
  .p-datepicker.p-datepicker-multiple-month .p-datepicker-group {
    border-left: 1px solid #e2e8f0 !important;
    padding-right: 0.75rem !important;
    padding-left: 0.75rem !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .p-datepicker.p-datepicker-multiple-month .p-datepicker-group:first-child {
    padding-left: 0 !important;
    border-left: 0 none !important;
  }
  .p-datepicker.p-datepicker-multiple-month .p-datepicker-group:last-child {
    padding-right: 0 !important;
  }
  .p-datepicker:not(.p-disabled) table td span:not(.p-highlight):not(.p-disabled):hover {
    background: #f1f5f9 !important;
  }
  .p-datepicker:not(.p-disabled) table td span:not(.p-highlight):not(.p-disabled):focus {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }
  .p-datepicker:not(.p-disabled) .p-monthpicker .p-monthpicker-month:not(.p-disabled):not(.p-highlight):hover {
    background: #f1f5f9 !important;
  }
  .p-datepicker:not(.p-disabled) .p-monthpicker .p-monthpicker-month:not(.p-disabled):focus {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }
  .p-datepicker:not(.p-disabled) .p-yearpicker .p-yearpicker-year:not(.p-disabled):not(.p-highlight):hover {
    background: #f1f5f9 !important;
  }
  .p-datepicker:not(.p-disabled) .p-yearpicker .p-yearpicker-year:not(.p-disabled):focus {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }

  p-calendar.p-datepicker-clearable .p-inputtext {
    padding-right: 2.5rem !important;
  }
  p-calendar.p-datepicker-clearable .p-datepicker-clear-icon {
    color: #94a3b8 !important;
    right: 0.75rem !important;
  }

  p-calendar.p-datepicker-clearable .p-datepicker-w-btn .p-datepicker-clear-icon {
    color: #94a3b8 !important;
    right: 3.25rem !important;
  }

  @media screen and (max-width: 769px) {
    .p-datepicker table th, .p-datepicker table td {
      padding: 0.25rem !important;
    }
  }
  .p-cascadeselect {
    background: #ffffff !important;
    border: 1px solid #cbd5e1 !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border-radius: 6px !important;
  }
  .p-cascadeselect:not(.p-disabled):hover {
    border-color: #94a3b8 !important;
  }
  .p-cascadeselect:not(.p-disabled).p-focus {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: -1px !important;
    box-shadow: none !important;
    border-color: #94a3b8 !important;
  }
  .p-cascadeselect .p-cascadeselect-label {
    background: transparent !important;
    border: 0 none !important;
    padding: 0.5rem 0.75rem !important;
  }
  .p-cascadeselect .p-cascadeselect-label.p-placeholder {
    color: #64748b !important;
  }
  .p-cascadeselect .p-cascadeselect-label:enabled:focus {
    outline: 0 none !important;
    box-shadow: none !important;
  }
  .p-cascadeselect .p-cascadeselect-trigger {
    background: transparent !important;
    color: #94a3b8 !important;
    width: 2.5rem !important;
    border-top-right-radius: 6px !important;
    border-bottom-right-radius: 6px !important;
  }
  .p-cascadeselect.p-invalid.p-component {
    border-color: #f87171 !important;
  }
  .p-cascadeselect.p-variant-filled {
    background-color: #f8fafc !important;
  }
  .p-cascadeselect.p-variant-filled:enabled:hover {
    background-color: #f8fafc !important;
  }
  .p-cascadeselect.p-variant-filled:enabled:focus {
    background-color: #ffffff !important;
  }

  .p-cascadeselect-panel {
    background: #ffffff !important;
    color: #334155 !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 6px !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1) !important;
  }
  .p-cascadeselect-panel .p-cascadeselect-items {
    padding: 0.25rem 0.25rem !important;
  }
  .p-cascadeselect-panel .p-cascadeselect-items .p-cascadeselect-item {
    margin: 2px 0 !important;
    border: 0 none !important;
    color: #334155 !important;
    background: transparent !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border-radius: 4px !important;
  }
  .p-cascadeselect-panel .p-cascadeselect-items .p-cascadeselect-item:first-child {
    margin-top: 0 !important;
  }
  .p-cascadeselect-panel .p-cascadeselect-items .p-cascadeselect-item.p-highlight {
    color: #1D4ED8 !important;
    background: #EFF6FF !important;
  }
  .p-cascadeselect-panel .p-cascadeselect-items .p-cascadeselect-item.p-highlight.p-focus {
    background: rgba(59, 130, 246, 0.24) !important;
  }
  .p-cascadeselect-panel .p-cascadeselect-items .p-cascadeselect-item:not(.p-highlight):not(.p-disabled).p-focus {
    color: #0f172a !important;
    background: #e2e8f0 !important;
  }
  .p-cascadeselect-panel .p-cascadeselect-items .p-cascadeselect-item:not(.p-highlight):not(.p-disabled):hover {
    color: #1e293b !important;
    background: #f1f5f9 !important;
  }
  .p-cascadeselect-panel .p-cascadeselect-items .p-cascadeselect-item .p-cascadeselect-item-content {
    padding: 0.5rem 0.75rem !important;
  }
  .p-cascadeselect-panel .p-cascadeselect-items .p-cascadeselect-item .p-cascadeselect-group-icon {
    font-size: 0.875rem !important;
  }

  .p-input-filled .p-cascadeselect {
    background: #f8fafc !important;
  }
  .p-input-filled .p-cascadeselect:not(.p-disabled):hover {
    background-color: #f8fafc !important;
  }
  .p-input-filled .p-cascadeselect:not(.p-disabled).p-focus {
    background-color: #ffffff !important;
  }

  p-cascadeselect.ng-dirty.ng-invalid > .p-cascadeselect {
    border-color: #f87171 !important;
  }

  p-cascadeselect.p-cascadeselect-clearable .p-cascadeselect-label {
    padding-right: 0.75rem !important;
  }
  p-cascadeselect.p-cascadeselect-clearable .p-cascadeselect-clear-icon {
    color: #94a3b8 !important;
    right: 2.5rem !important;
  }

  .p-overlay-modal .p-cascadeselect-sublist .p-cascadeselect-panel {
    box-shadow: none !important;
    border-radius: 0 !important;
    padding: 0.25rem 0 0.25rem 0.5rem !important;
  }
  .p-overlay-modal .p-cascadeselect-item-active > .p-cascadeselect-item-content .p-cascadeselect-group-icon {
    transform: rotate(90deg) !important;
  }

  .p-checkbox {
    width: 1.25rem !important;
    height: 1.25rem !important;
  }
  .p-checkbox .p-checkbox-box {
    border: 1px solid #cbd5e1 !important;
    background: #ffffff !important;
    width: 1.25rem !important;
    height: 1.25rem !important;
    color: #334155 !important;
    border-radius: 6px !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    outline-color: transparent !important;
  }
  .p-checkbox .p-checkbox-box .p-checkbox-icon {
    transition-duration: 0.2s !important;
    color: #ffffff !important;
    font-size: 0.875rem !important;
  }
  .p-checkbox .p-checkbox-box .p-icon {
    width: 0.875rem !important;
    height: 0.875rem !important;
  }
  .p-checkbox .p-checkbox-box.p-highlight {
    border-color: #3B82F6 !important;
    background: #3B82F6 !important;
  }
  .p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box:hover {
    border-color: #94a3b8 !important;
  }
  .p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box.p-focus {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: -1px !important;
    box-shadow: none !important;
    border-color: #94a3b8 !important;
  }
  .p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box.p-highlight:hover {
    border-color: #2563eb !important;
    background: #2563eb !important;
    color: #ffffff !important;
  }
  .p-checkbox.p-variant-filled .p-checkbox-box {
    background-color: #f8fafc !important;
  }
  .p-checkbox.p-variant-filled .p-checkbox-box.p-highlight {
    background: #3B82F6 !important;
  }
  .p-checkbox.p-variant-filled:not(.p-checkbox-disabled) .p-checkbox-box:hover {
    background-color: #f8fafc !important;
  }
  .p-checkbox.p-variant-filled:not(.p-checkbox-disabled) .p-checkbox-box.p-highlight:hover {
    background: #2563eb !important;
  }

  p-checkbox.ng-dirty.ng-invalid > .p-checkbox > .p-checkbox-box {
    border-color: #f87171 !important;
  }

  .p-input-filled .p-checkbox .p-checkbox-box {
    background-color: #f8fafc !important;
  }
  .p-input-filled .p-checkbox .p-checkbox-box.p-highlight {
    background: #3B82F6 !important;
  }
  .p-input-filled .p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box:hover {
    background-color: #f8fafc !important;
  }
  .p-input-filled .p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box.p-highlight:hover {
    background: #2563eb !important;
  }

  .p-checkbox-label {
    margin-left: 0.5rem !important;
  }

  p-tristatecheckbox.ng-dirty.ng-invalid > .p-checkbox > .p-checkbox-box {
    border-color: #f87171 !important;
  }

  .p-chips:not(.p-disabled):hover .p-chips-multiple-container {
    border-color: #94a3b8 !important;
  }
  .p-chips:not(.p-disabled).p-focus .p-chips-multiple-container {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: -1px !important;
    box-shadow: none !important;
    border-color: #94a3b8 !important;
  }
  .p-chips .p-chips-multiple-container {
    padding: 0.25rem 0.75rem !important;
    gap: 0.5rem !important;
  }
  .p-chips .p-chips-multiple-container .p-chips-token {
    padding: 0.25rem 0.75rem !important;
    margin-right: 0.5rem !important;
    background: #f1f5f9 !important;
    color: #1e293b !important;
    border-radius: 16px !important;
  }
  .p-chips .p-chips-multiple-container .p-chips-token.p-focus {
    background: #e2e8f0 !important;
    color: #0f172a !important;
  }
  .p-chips .p-chips-multiple-container .p-chips-token .p-chips-token-icon {
    margin-left: 0.5rem !important;
  }
  .p-chips .p-chips-multiple-container .p-chips-input-token {
    padding: 0.25rem 0 !important;
  }
  .p-chips .p-chips-multiple-container .p-chips-input-token input {
    font-family: var(--font-family) !important;
    font-feature-settings: var(--font-feature-settings, normal) !important;
    font-size: 1rem !important;
    color: #334155 !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  p-chips.ng-dirty.ng-invalid > .p-chips > .p-inputtext {
    border-color: #f87171 !important;
  }

  p-chips.p-chips-clearable .p-inputtext {
    padding-right: 1.75rem !important;
  }
  p-chips.p-chips-clearable .p-chips-clear-icon {
    color: #94a3b8 !important;
    right: 0.75rem !important;
  }

  .p-colorpicker-preview,
.p-fluid .p-colorpicker-preview.p-inputtext {
    width: 1.5rem !important;
    height: 1.5rem !important;
  }

  .p-colorpicker-panel {
    background: #323232 !important;
    border: 1px solid #191919 !important;
  }
  .p-colorpicker-panel .p-colorpicker-color-handle,
.p-colorpicker-panel .p-colorpicker-hue-handle {
    border-color: #ffffff !important;
  }

  .p-colorpicker-overlay-panel {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1) !important;
  }

  .p-select {
    background: #ffffff !important;
    border: 1px solid #cbd5e1 !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border-radius: 6px !important;
  }
  .p-select:not(.p-disabled):hover {
    border-color: #94a3b8 !important;
  }
  .p-select:not(.p-disabled).p-focus {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: -1px !important;
    box-shadow: none !important;
    border-color: #94a3b8 !important;
  }
  .p-select.p-select-clearable .p-select-label {
    padding-right: 1.75rem !important;
  }
  .p-select .p-select-label {
    background: transparent !important;
    border: 0 none !important;
  }
  .p-select .p-select-label.p-placeholder {
    color: #64748b !important;
  }
  .p-select .p-select-label:focus, .p-select .p-select-label:enabled:focus {
    outline: 0 none !important;
    box-shadow: none !important;
  }
  .p-select .p-select-trigger {
    background: transparent !important;
    color: #94a3b8 !important;
    width: 2.5rem !important;
    border-top-right-radius: 6px !important;
    border-bottom-right-radius: 6px !important;
  }
  .p-select .p-select-clear-icon {
    color: #94a3b8 !important;
    right: 2.5rem !important;
  }
  .p-select.p-invalid.p-component {
    border-color: #f87171 !important;
  }
  .p-select.p-variant-filled {
    background-color: #f8fafc !important;
  }
  .p-select.p-variant-filled:enabled:hover {
    background-color: #f8fafc !important;
  }
  .p-select.p-variant-filled:enabled:focus {
    background-color: #ffffff !important;
  }

  .p-select-panel {
    background: #ffffff !important;
    color: #334155 !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 6px !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1) !important;
  }
  .p-select-panel .p-select-header {
    padding: 0.5rem 0.5rem 0 0.5rem !important;
    border-bottom: 0 none !important;
    color: #334155 !important;
    background: #ffffff !important;
    margin: 0 0 0 0 !important;
    border-top-right-radius: 6px !important;
    border-top-left-radius: 6px !important;
  }
  .p-select-panel .p-select-header .p-select-filter {
    padding-right: 1.75rem !important;
    margin-right: -1.75rem !important;
  }
  .p-select-panel .p-select-header .p-select-filter-icon {
    right: 0.75rem !important;
    color: #94a3b8 !important;
  }
  .p-select-panel .p-select-items {
    padding: 0.25rem 0.25rem !important;
  }
  .p-select-panel .p-select-items .p-select-item {
    margin: 2px 0 !important;
    padding: 0.5rem 0.75rem !important;
    border: 0 none !important;
    color: #334155 !important;
    background: transparent !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border-radius: 4px !important;
  }
  .p-select-panel .p-select-items .p-select-item:first-child {
    margin-top: 0 !important;
  }
  .p-select-panel .p-select-items .p-select-item.p-highlight {
    color: #1D4ED8 !important;
    background: #EFF6FF !important;
  }
  .p-select-panel .p-select-items .p-select-item.p-highlight.p-focus {
    background: rgba(59, 130, 246, 0.24) !important;
  }
  .p-select-panel .p-select-items .p-select-item:not(.p-highlight):not(.p-disabled).p-focus {
    color: #0f172a !important;
    background: #e2e8f0 !important;
  }
  .p-select-panel .p-select-items .p-select-item:not(.p-highlight):not(.p-disabled):hover {
    color: #1e293b !important;
    background: #f1f5f9 !important;
  }
  .p-select-panel .p-select-items .p-select-item-group {
    margin: 0 !important;
    padding: 0.5rem 0.75rem !important;
    color: #94a3b8 !important;
    background: #ffffff !important;
    font-weight: 600 !important;
  }
  .p-select-panel .p-select-items .p-select-empty-message {
    padding: 0.5rem 0.75rem !important;
    color: #334155 !important;
    background: transparent !important;
  }

  .p-input-filled .p-select {
    background: #f8fafc !important;
  }
  .p-input-filled .p-select:not(.p-disabled):hover {
    background-color: #f8fafc !important;
  }
  .p-input-filled .p-select:not(.p-disabled).p-focus {
    background-color: #ffffff !important;
  }
  .p-input-filled .p-select:not(.p-disabled).p-focus .p-inputtext {
    background-color: transparent !important;
  }

  p-dropdown.ng-dirty.ng-invalid > .p-select {
    border-color: #f87171 !important;
  }

  .p-icon-field .p-input-icon {
    position: absolute !important;
    top: 50% !important;
    margin-top: -0.5rem !important;
  }

  .p-inputgroup-addon {
    background: #ffffff !important;
    color: #64748b !important;
    border-top: 1px solid #cbd5e1 !important;
    border-left: 1px solid #cbd5e1 !important;
    border-bottom: 1px solid #cbd5e1 !important;
    padding: 0.5rem 0.75rem !important;
    min-width: 2.5rem !important;
  }
  .p-inputgroup-addon:last-child {
    border-right: 1px solid #cbd5e1 !important;
  }

  .p-inputgroup > .p-component,
.p-inputgroup > .p-inputwrapper > .p-inputtext,
.p-inputgroup > .p-float-label > .p-component {
    border-radius: 0 !important;
    margin: 0 !important;
  }
  .p-inputgroup > .p-component + .p-inputgroup-addon,
.p-inputgroup > .p-inputwrapper > .p-inputtext + .p-inputgroup-addon,
.p-inputgroup > .p-float-label > .p-component + .p-inputgroup-addon {
    border-left: 0 none !important;
  }
  .p-inputgroup > .p-component:focus,
.p-inputgroup > .p-inputwrapper > .p-inputtext:focus,
.p-inputgroup > .p-float-label > .p-component:focus {
    z-index: 1 !important;
  }
  .p-inputgroup > .p-component:focus ~ label,
.p-inputgroup > .p-inputwrapper > .p-inputtext:focus ~ label,
.p-inputgroup > .p-float-label > .p-component:focus ~ label {
    z-index: 1 !important;
  }

  .p-inputgroup-addon:first-child,
.p-inputgroup button:first-child,
.p-inputgroup input:first-child,
.p-inputgroup > .p-inputwrapper:first-child > .p-component,
.p-inputgroup > .p-inputwrapper:first-child > .p-component > .p-inputtext {
    border-top-left-radius: 6px !important;
    border-bottom-left-radius: 6px !important;
  }

  .p-inputgroup .p-float-label:first-child input {
    border-top-left-radius: 6px !important;
    border-bottom-left-radius: 6px !important;
  }

  .p-inputgroup-addon:last-child,
.p-inputgroup button:last-child,
.p-inputgroup input:last-child,
.p-inputgroup > .p-inputwrapper:last-child > .p-component,
.p-inputgroup > .p-inputwrapper:last-child > .p-component > .p-inputtext {
    border-top-right-radius: 6px !important;
    border-bottom-right-radius: 6px !important;
  }

  .p-inputgroup .p-float-label:last-child input {
    border-top-right-radius: 6px !important;
    border-bottom-right-radius: 6px !important;
  }

  .p-fluid .p-inputgroup .p-button {
    width: auto !important;
  }
  .p-fluid .p-inputgroup .p-button.p-button-icon-only {
    width: 2.5rem ;
  }

  .p-icon-field-left .p-input-icon:first-of-type {
    left: 0.75rem !important;
    color: #94a3b8 !important;
  }

  .p-icon-field-right .p-input-icon:last-of-type {
    right: 0.75rem !important;
    color: #94a3b8 !important;
  }

  p-inputmask.ng-dirty.ng-invalid > .p-inputtext {
    border-color: #f87171 !important;
  }

  p-inputmask.p-inputmask-clearable .p-inputtext {
    padding-right: 2.5rem !important;
  }
  p-inputmask.p-inputmask-clearable .p-inputmask-clear-icon {
    color: #94a3b8 !important;
    right: 0.75rem !important;
  }

  .p-inputmask.p-variant-filled {
    background-color: #f8fafc !important;
  }
  .p-inputmask.p-variant-filled:enabled:hover {
    background-color: #f8fafc !important;
  }
  .p-inputmask.p-variant-filled:enabled:focus {
    background-color: #ffffff !important;
  }

  p-inputnumber.ng-dirty.ng-invalid > .p-inputnumber > .p-inputtext {
    border-color: #f87171 !important;
  }

  p-inputnumber.p-inputnumber-clearable .p-inputnumber-input {
    padding-right: 2.5rem !important;
  }
  p-inputnumber.p-inputnumber-clearable .p-inputnumber-clear-icon {
    color: #94a3b8 !important;
    right: 0.75rem !important;
  }

  p-inputnumber.p-inputnumber-clearable .p-inputnumber-buttons-stacked .p-inputnumber-clear-icon {
    right: 3.25rem !important;
  }
  p-inputnumber.p-inputnumber-clearable .p-inputnumber-buttons-horizontal .p-inputnumber-clear-icon {
    right: 3.25rem !important;
  }

  p-inputnumber.p-inputnumber.p-variant-filled > .p-inputnumber-input {
    background-color: #f8fafc !important;
  }
  p-inputnumber.p-inputnumber.p-variant-filled > .p-inputnumber-input:enabled:hover {
    background-color: #f8fafc !important;
  }
  p-inputnumber.p-inputnumber.p-variant-filled > .p-inputnumber-input:enabled:focus {
    background-color: #ffffff !important;
  }

  .p-inputotp {
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
  }

  .p-inputotp-input {
    text-align: center !important;
    width: 2.5rem !important;
  }

  .p-inputswitch {
    width: 2.5rem !important;
    height: 1.5rem !important;
  }
  .p-inputswitch .p-inputswitch-slider {
    background: #cbd5e1 !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border-radius: 30px !important;
  }
  .p-inputswitch .p-inputswitch-slider:before {
    background: #ffffff !important;
    width: 1rem !important;
    height: 1rem !important;
    left: 0.25rem !important;
    margin-top: -0.5rem !important;
    border-radius: 50% !important;
    transition-duration: 0.2s !important;
  }
  .p-inputswitch.p-inputswitch-checked .p-inputswitch-slider:before {
    transform: translateX(1rem) !important;
  }
  .p-inputswitch.p-focus .p-inputswitch-slider {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }
  .p-inputswitch:not(.p-disabled):hover .p-inputswitch-slider {
    background: #94a3b8 !important;
  }
  .p-inputswitch.p-inputswitch-checked .p-inputswitch-slider {
    background: #3B82F6 !important;
  }
  .p-inputswitch.p-inputswitch-checked .p-inputswitch-slider:before {
    background: #ffffff !important;
  }
  .p-inputswitch.p-inputswitch-checked:not(.p-disabled):hover .p-inputswitch-slider {
    background: #2563eb !important;
  }

  p-inputswitch.ng-dirty.ng-invalid > .p-inputswitch > .p-inputswitch-slider {
    border-color: #f87171 !important;
  }

  .p-inputtext {
    font-family: var(--font-family) !important;
    font-feature-settings: var(--font-feature-settings, normal) !important;
    font-size: 1rem !important;
    color: #334155 !important;
    background: #ffffff !important;
    padding: 0.5rem 0.75rem !important;
    border: 1px solid #cbd5e1 !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    appearance: none !important;
    border-radius: 6px !important;
  }
  .p-inputtext:enabled:hover {
    border-color: #94a3b8 !important;
  }
  .p-inputtext:enabled:focus {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: -1px !important;
    box-shadow: none !important;
    border-color: #94a3b8 !important;
  }
  .p-inputtext.ng-dirty.ng-invalid {
    border-color: #f87171 !important;
  }
  .p-inputtext.p-variant-filled {
    background-color: #f8fafc !important;
  }
  .p-inputtext.p-variant-filled:enabled:hover {
    background-color: #f8fafc !important;
  }
  .p-inputtext.p-variant-filled:enabled:focus {
    background-color: #ffffff !important;
  }
  .p-inputtext.p-inputtext-sm {
    font-size: 0.875rem !important;
    padding: 0.4375rem 0.65625rem !important;
  }
  .p-inputtext.p-inputtext-lg {
    font-size: 1.25rem !important;
    padding: 0.625rem 0.9375rem !important;
  }

  .p-float-label > label {
    left: 0.75rem !important;
    color: #64748b !important;
    transition-duration: 0.2s !important;
  }

  .p-float-label > .ng-invalid.ng-dirty + label {
    color: #f87171 !important;
  }

  .p-input-icon-left > .p-icon-wrapper.p-icon,
.p-input-icon-left > i:first-of-type {
    left: 0.75rem !important;
    color: #94a3b8 !important;
  }

  .p-input-icon-left > .p-inputtext {
    padding-left: 2.5rem !important;
  }

  .p-input-icon-left.p-float-label > label {
    left: 2.5rem !important;
  }

  .p-input-icon-right > .p-icon-wrapper,
.p-input-icon-right > i:last-of-type {
    right: 0.75rem !important;
    color: #94a3b8 !important;
  }

  .p-input-icon-right > .p-inputtext {
    padding-right: 2.5rem !important;
  }

  .p-icon-field-left > .p-inputtext {
    padding-left: 2.5rem !important;
  }

  .p-icon-field-left.p-float-label > label {
    left: 2.5rem !important;
  }

  .p-icon-field-right > .p-inputtext {
    padding-right: 2.5rem !important;
  }

  ::-webkit-input-placeholder {
    color: #64748b !important;
  }

  :-moz-placeholder {
    color: #64748b !important;
  }

  ::-moz-placeholder {
    color: #64748b !important;
  }

  :-ms-input-placeholder {
    color: #64748b !important;
  }

  .p-input-filled .p-inputtext {
    background-color: #f8fafc !important;
  }
  .p-input-filled .p-inputtext:enabled:hover {
    background-color: #f8fafc !important;
  }
  .p-input-filled .p-inputtext:enabled:focus {
    background-color: #ffffff !important;
  }

  .p-inputtext-sm .p-inputtext {
    font-size: 0.875rem !important;
    padding: 0.4375rem 0.65625rem !important;
  }

  .p-inputtext-lg .p-inputtext {
    font-size: 1.25rem !important;
    padding: 0.625rem 0.9375rem !important;
  }

  .p-listbox {
    background: #ffffff !important;
    color: #334155 !important;
    border: 1px solid #cbd5e1 !important;
    border-radius: 6px !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-listbox .p-listbox-header {
    padding: 0.5rem 0.5rem 0 0.5rem !important;
    border-bottom: 0 none !important;
    color: #334155 !important;
    background: #ffffff !important;
    margin: 0 0 0 0 !important;
    border-top-right-radius: 6px !important;
    border-top-left-radius: 6px !important;
  }
  .p-listbox .p-listbox-header .p-listbox-filter {
    padding-right: 1.75rem !important;
  }
  .p-listbox .p-listbox-header .p-listbox-filter-icon {
    right: 0.75rem !important;
    color: #94a3b8 !important;
  }
  .p-listbox .p-listbox-header .p-checkbox {
    margin-right: 0.5rem !important;
  }
  .p-listbox .p-listbox-list {
    padding: 0.25rem 0.25rem !important;
    outline: 0 none !important;
  }
  .p-listbox .p-listbox-list .p-listbox-item {
    margin: 2px 0 !important;
    padding: 0.5rem 0.75rem !important;
    border: 0 none !important;
    color: #334155 !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border-radius: 4px !important;
  }
  .p-listbox .p-listbox-list .p-listbox-item:first-child {
    margin-top: 0 !important;
  }
  .p-listbox .p-listbox-list .p-listbox-item.p-highlight {
    color: #1D4ED8 !important;
    background: #EFF6FF !important;
  }
  .p-listbox .p-listbox-list .p-listbox-item .p-checkbox {
    margin-right: 0.5rem !important;
  }
  .p-listbox .p-listbox-list .p-listbox-item-group {
    margin: 0 !important;
    padding: 0.5rem 0.75rem !important;
    color: #94a3b8 !important;
    background: #ffffff !important;
    font-weight: 600 !important;
  }
  .p-listbox .p-listbox-list .p-listbox-empty-message {
    padding: 0.5rem 0.75rem !important;
    color: #334155 !important;
    background: transparent !important;
  }
  .p-listbox:not(.p-disabled) .p-listbox-item.p-highlight.p-focus {
    background: rgba(59, 130, 246, 0.24) !important;
  }
  .p-listbox:not(.p-disabled) .p-listbox-item:not(.p-highlight):not(.p-disabled).p-focus {
    color: #1e293b !important;
    background: #f1f5f9 !important;
  }
  .p-listbox:not(.p-disabled) .p-listbox-item:not(.p-highlight):not(.p-disabled):hover {
    color: #1e293b !important;
    background: #f1f5f9 !important;
  }
  .p-listbox:not(.p-disabled) .p-listbox-item:not(.p-highlight):not(.p-disabled):hover.p-focus {
    color: #1e293b !important;
    background: #f1f5f9 !important;
  }
  .p-listbox.p-focus {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: -1px !important;
    box-shadow: none !important;
    border-color: #94a3b8 !important;
  }

  p-listbox.ng-dirty.ng-invalid > .p-listbox {
    border-color: #f87171 !important;
  }

  .p-multiselect {
    background: #ffffff !important;
    border: 1px solid #cbd5e1 !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border-radius: 6px !important;
  }
  .p-multiselect:not(.p-disabled):hover {
    border-color: #94a3b8 !important;
  }
  .p-multiselect:not(.p-disabled).p-focus {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: -1px !important;
    box-shadow: none !important;
    border-color: #94a3b8 !important;
  }
  .p-multiselect .p-multiselect-label {
    padding: 0.5rem 0.75rem !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-multiselect .p-multiselect-label.p-placeholder {
    color: #64748b !important;
  }
  .p-multiselect.p-multiselect-chip .p-multiselect-token {
    padding: 0.25rem 0.75rem !important;
    margin-right: 0.5rem !important;
    background: #f1f5f9 !important;
    color: #1e293b !important;
    border-radius: 16px !important;
  }
  .p-multiselect.p-multiselect-chip .p-multiselect-token .p-multiselect-token-icon {
    margin-left: 0.5rem !important;
  }
  .p-multiselect .p-multiselect-trigger {
    background: transparent !important;
    color: #94a3b8 !important;
    width: 2.5rem !important;
    border-top-right-radius: 6px !important;
    border-bottom-right-radius: 6px !important;
  }
  .p-multiselect.p-variant-filled {
    background: #f8fafc !important;
  }
  .p-multiselect.p-variant-filled:not(.p-disabled):hover {
    background-color: #f8fafc !important;
  }
  .p-multiselect.p-variant-filled:not(.p-disabled).p-focus {
    background-color: #ffffff !important;
  }

  .p-inputwrapper-filled .p-multiselect.p-multiselect-chip .p-multiselect-label {
    padding: 0.25rem 0.75rem !important;
  }

  .p-multiselect-clearable .p-multiselect-label-container {
    padding-right: 1.75rem !important;
  }
  .p-multiselect-clearable .p-multiselect-clear-icon {
    color: #94a3b8 !important;
    right: 2.5rem !important;
  }

  .p-multiselect-panel {
    background: #ffffff !important;
    color: #334155 !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 6px !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1) !important;
  }
  .p-multiselect-panel .p-multiselect-header {
    padding: 0.5rem 0.5rem 0 0.5rem !important;
    border-bottom: 0 none !important;
    color: #334155 !important;
    background: #ffffff !important;
    margin: 0 0 0 0 !important;
    border-top-right-radius: 6px !important;
    border-top-left-radius: 6px !important;
  }
  .p-multiselect-panel .p-multiselect-header .p-multiselect-filter-container .p-inputtext {
    padding-right: 1.75rem !important;
  }
  .p-multiselect-panel .p-multiselect-header .p-multiselect-filter-container .p-multiselect-filter-icon {
    right: 0.75rem !important;
    color: #94a3b8 !important;
  }
  .p-multiselect-panel .p-multiselect-header .p-checkbox {
    margin-right: 0.5rem !important;
  }
  .p-multiselect-panel .p-multiselect-header .p-multiselect-close {
    margin-left: 0.5rem !important;
    width: 1.75rem !important;
    height: 1.75rem !important;
    color: #64748b !important;
    border: 0 none !important;
    background: transparent !important;
    border-radius: 50% !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-multiselect-panel .p-multiselect-header .p-multiselect-close:enabled:hover {
    color: #475569 !important;
    border-color: transparent !important;
    background: #f1f5f9 !important;
  }
  .p-multiselect-panel .p-multiselect-header .p-multiselect-close:focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }
  .p-multiselect-panel .p-multiselect-items {
    padding: 0.25rem 0.25rem !important;
  }
  .p-multiselect-panel .p-multiselect-items .p-multiselect-item {
    margin: 2px 0 !important;
    padding: 0.5rem 0.75rem !important;
    border: 0 none !important;
    color: #334155 !important;
    background: transparent !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border-radius: 4px !important;
  }
  .p-multiselect-panel .p-multiselect-items .p-multiselect-item:first-child {
    margin-top: 0 !important;
  }
  .p-multiselect-panel .p-multiselect-items .p-multiselect-item.p-highlight {
    color: #1D4ED8 !important;
    background: #EFF6FF !important;
  }
  .p-multiselect-panel .p-multiselect-items .p-multiselect-item.p-highlight.p-focus {
    background: rgba(59, 130, 246, 0.24) !important;
  }
  .p-multiselect-panel .p-multiselect-items .p-multiselect-item:not(.p-highlight):not(.p-disabled).p-focus {
    color: #0f172a !important;
    background: #f1f5f9 !important;
  }
  .p-multiselect-panel .p-multiselect-items .p-multiselect-item:not(.p-highlight):not(.p-disabled):hover {
    color: #1e293b !important;
    background: #f1f5f9 !important;
  }
  .p-multiselect-panel .p-multiselect-items .p-multiselect-item .p-checkbox {
    margin-right: 0.5rem !important;
  }
  .p-multiselect-panel .p-multiselect-items .p-multiselect-item-group {
    margin: 0 !important;
    padding: 0.5rem 0.75rem !important;
    color: #94a3b8 !important;
    background: #ffffff !important;
    font-weight: 600 !important;
  }
  .p-multiselect-panel .p-multiselect-items .p-multiselect-empty-message {
    padding: 0.5rem 0.75rem !important;
    color: #334155 !important;
    background: transparent !important;
  }

  .p-input-filled .p-multiselect {
    background: #f8fafc !important;
  }
  .p-input-filled .p-multiselect:not(.p-disabled):hover {
    background-color: #f8fafc !important;
  }
  .p-input-filled .p-multiselect:not(.p-disabled).p-focus {
    background-color: #ffffff !important;
  }

  p-multiselect.ng-dirty.ng-invalid > .p-multiselect {
    border-color: #f87171 !important;
  }

  p-password.ng-invalid.ng-dirty > .p-password > .p-inputtext {
    border-color: #f87171 !important;
  }

  .p-password-panel {
    padding: 0 1.125rem 1.125rem 1.125rem !important;
    background: #ffffff !important;
    color: #334155 !important;
    border: 1px solid #e2e8f0 !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1) !important;
    border-radius: 6px !important;
  }
  .p-password-panel .p-password-meter {
    margin-bottom: 0.5rem !important;
    background: #e2e8f0 !important;
  }
  .p-password-panel .p-password-meter .p-password-strength.weak {
    background: #ef4444 !important;
  }
  .p-password-panel .p-password-meter .p-password-strength.medium {
    background: #f59e0b !important;
  }
  .p-password-panel .p-password-meter .p-password-strength.strong {
    background: #22c55e !important;
  }

  p-password.p-password-clearable .p-password-input {
    padding-right: 2.5rem !important;
  }
  p-password.p-password-clearable .p-password-clear-icon {
    color: #94a3b8 !important;
    right: 0.75rem !important;
  }

  p-password.p-password-clearable.p-password-mask .p-password-input {
    padding-right: 4.25rem !important;
  }
  p-password.p-password-clearable.p-password-mask .p-password-clear-icon {
    color: #94a3b8 !important;
    right: 2.5rem !important;
  }

  .p-radiobutton {
    width: 1.25rem !important;
    height: 1.25rem !important;
  }
  .p-radiobutton .p-radiobutton-box {
    border: 1px solid #cbd5e1 !important;
    background: #ffffff !important;
    width: 1.25rem !important;
    height: 1.25rem !important;
    color: #334155 !important;
    border-radius: 50% !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    outline-color: transparent !important;
  }
  .p-radiobutton .p-radiobutton-box:not(.p-disabled):not(.p-highlight):hover {
    border-color: #94a3b8 !important;
  }
  .p-radiobutton .p-radiobutton-box:not(.p-disabled).p-focus {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: -1px !important;
    box-shadow: none !important;
    border-color: #94a3b8 !important;
  }
  .p-radiobutton .p-radiobutton-box .p-radiobutton-icon {
    width: 0.75rem !important;
    height: 0.75rem !important;
    transition-duration: 0.2s !important;
    background-color: #3B82F6 !important;
  }
  .p-radiobutton .p-radiobutton-box.p-highlight {
    border-color: #3B82F6 !important;
    background: #ffffff !important;
  }
  .p-radiobutton .p-radiobutton-box.p-highlight:not(.p-disabled):hover {
    border-color: #2563eb !important;
    background: #ffffff !important;
    color: #2563eb !important;
  }
  .p-radiobutton.p-variant-filled .p-radiobutton-box {
    background-color: #f8fafc !important;
  }
  .p-radiobutton.p-variant-filled .p-radiobutton-box:not(.p-disabled):hover {
    background-color: #f8fafc !important;
  }
  .p-radiobutton.p-variant-filled .p-radiobutton-box.p-highlight {
    background: #ffffff !important;
  }
  .p-radiobutton.p-variant-filled .p-radiobutton-box.p-highlight:not(.p-disabled):hover {
    background: #ffffff !important;
  }

  p-radiobutton.ng-dirty.ng-invalid > .p-radiobutton > .p-radiobutton-box {
    border-color: #f87171 !important;
  }

  .p-input-filled .p-radiobutton .p-radiobutton-box {
    background-color: #f8fafc !important;
  }
  .p-input-filled .p-radiobutton .p-radiobutton-box:not(.p-disabled):hover {
    background-color: #f8fafc !important;
  }
  .p-input-filled .p-radiobutton .p-radiobutton-box.p-highlight {
    background: #ffffff !important;
  }
  .p-input-filled .p-radiobutton .p-radiobutton-box.p-highlight:not(.p-disabled):hover {
    background: #ffffff !important;
  }

  .p-radiobutton-label {
    margin-left: 0.5rem !important;
  }

  .p-rating {
    gap: 0.5rem !important;
  }
  .p-rating .p-rating-item {
    border-radius: 50% !important;
    outline-color: transparent !important;
    display: inline-flex !important;
    justify-content: center !important;
    align-items: center !important;
  }
  .p-rating .p-rating-item .p-rating-icon {
    color: #64748b !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    font-size: 1rem !important;
  }
  .p-rating .p-rating-item .p-rating-icon.p-icon {
    width: 1rem !important;
    height: 1rem !important;
  }
  .p-rating .p-rating-item .p-rating-icon.p-rating-cancel {
    color: #f87171 !important;
  }
  .p-rating .p-rating-item.p-focus {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }
  .p-rating .p-rating-item.p-rating-item-active .p-rating-icon {
    color: #3B82F6 !important;
  }
  .p-rating:not(.p-disabled):not(.p-readonly) .p-rating-item:hover .p-rating-icon {
    color: #3B82F6 !important;
  }
  .p-rating:not(.p-disabled):not(.p-readonly) .p-rating-item:hover .p-rating-icon.p-rating-cancel {
    color: #ef4444 !important;
  }

  .p-selectbutton .p-button {
    max-height:32px !important;
    background: #f1f5f9 !important;
    border: 1px solid #f1f5f9 !important;
    color: #64748b !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-selectbutton .p-button .p-button-icon-left,
.p-selectbutton .p-button .p-button-icon-right {
    color: #64748b !important;
  }
  .p-selectbutton .p-button:not(.p-disabled):not(.p-highlight):hover {
    background: #f1f5f9 !important;
    border-color: #f1f5f9 !important;
    color: #334155 !important;
  }
  .p-selectbutton .p-button:not(.p-disabled):not(.p-highlight):hover .p-button-icon-left,
.p-selectbutton .p-button:not(.p-disabled):not(.p-highlight):hover .p-button-icon-right {
    color: #334155 !important;
  }
  .p-selectbutton .p-button.p-highlight {
    background: #f1f5f9 !important;
    border-color: #f1f5f9 !important;
    color: #0f172a !important;
  }
  .p-selectbutton .p-button.p-highlight .p-button-icon-left,
.p-selectbutton .p-button.p-highlight .p-button-icon-right {
    color: #0f172a !important;
  }
  .p-selectbutton .p-button.p-highlight:hover {
    background: #f1f5f9 !important;
    border-color: #f1f5f9 !important;
    color: #0f172a !important;
  }
  .p-selectbutton .p-button.p-highlight:hover .p-button-icon-left,
.p-selectbutton .p-button.p-highlight:hover .p-button-icon-right {
    color: #0f172a !important;
  }

  p-selectbutton.ng-dirty.ng-invalid > .p-selectbutton > .p-button {
    border-color: #f87171 !important;
  }

  .p-slider {
    background: #e2e8f0 !important;
    border: 0 none !important;
    border-radius: 6px !important;
  }
  .p-slider.p-slider-horizontal {
    height: 3px !important;
  }
  .p-slider.p-slider-horizontal .p-slider-handle {
    margin-top: -10px !important;
    margin-left: -10px !important;
  }
  .p-slider.p-slider-vertical {
    height: 100% !important;
    width: 3px !important;
  }
  .p-slider.p-slider-vertical .p-slider-handle {
    height: 20px !important;
    width: 20px !important;
    margin-left: -10px !important;
    margin-bottom: -10px !important;
  }
  .p-slider .p-slider-handle {
    height: 20px !important;
    width: 20px !important;
    background: #e2e8f0 !important;
    border: 0 none !important;
    border-radius: 50% !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-slider .p-slider-handle:focus {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }
  .p-slider .p-slider-range {
    background: #3B82F6 !important;
  }
  .p-slider:not(.p-disabled) .p-slider-handle:hover {
    background: #e2e8f0 !important;
    border-color: transparent !important;
  }
  .p-slider.p-slider-animate.p-slider-horizontal .p-slider-handle {
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s, left 0.2s !important;
  }
  .p-slider.p-slider-animate.p-slider-horizontal .p-slider-range {
    transition: width 0.2s !important;
  }
  .p-slider.p-slider-animate.p-slider-vertical .p-slider-handle {
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s, bottom 0.2s !important;
  }
  .p-slider.p-slider-animate.p-slider-vertical .p-slider-range {
    transition: height 0.2s !important;
  }

  .p-togglebutton.p-button {
    background: #f1f5f9 !important;
    border: 1px solid #f1f5f9 !important;
    color: #64748b !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-togglebutton.p-button .p-button-icon-left,
.p-togglebutton.p-button .p-button-icon-right {
    color: #64748b !important;
  }
  .p-togglebutton.p-button:not(.p-disabled):not(.p-highlight):hover {
    background: #f1f5f9 !important;
    border-color: #f1f5f9 !important;
    color: #334155 !important;
  }
  .p-togglebutton.p-button:not(.p-disabled):not(.p-highlight):hover .p-button-icon-left,
.p-togglebutton.p-button:not(.p-disabled):not(.p-highlight):hover .p-button-icon-right {
    color: #334155 !important;
  }
  .p-togglebutton.p-button.p-highlight {
    background: #f1f5f9 !important;
    border-color: #f1f5f9 !important;
    color: #0f172a !important;
  }
  .p-togglebutton.p-button.p-highlight .p-button-icon-left,
.p-togglebutton.p-button.p-highlight .p-button-icon-right {
    color: #0f172a !important;
  }
  .p-togglebutton.p-button.p-highlight:hover {
    background: #f1f5f9 !important;
    border-color: #f1f5f9 !important;
    color: #0f172a !important;
  }
  .p-togglebutton.p-button.p-highlight:hover .p-button-icon-left,
.p-togglebutton.p-button.p-highlight:hover .p-button-icon-right {
    color: #0f172a !important;
  }

  p-togglebutton.ng-dirty.ng-invalid > .p-togglebutton.p-button {
    border-color: #f87171 !important;
  }

  .p-treeselect {
    background: #ffffff !important;
    border: 1px solid #cbd5e1 !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border-radius: 6px !important;
  }
  .p-treeselect:not(.p-disabled):hover {
    border-color: #94a3b8 !important;
  }
  .p-treeselect:not(.p-disabled).p-focus {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: -1px !important;
    box-shadow: none !important;
    border-color: #94a3b8 !important;
  }
  .p-treeselect .p-treeselect-label {
    padding: 0.5rem 0.75rem !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-treeselect .p-treeselect-label.p-placeholder {
    color: #64748b !important;
  }
  .p-treeselect.p-treeselect-chip .p-treeselect-token {
    padding: 0.25rem 0.75rem !important;
    margin-right: 0.5rem !important;
    background: #f1f5f9 !important;
    color: #1e293b !important;
    border-radius: 16px !important;
  }
  .p-treeselect .p-treeselect-trigger {
    background: transparent !important;
    color: #94a3b8 !important;
    width: 2.5rem !important;
    border-top-right-radius: 6px !important;
    border-bottom-right-radius: 6px !important;
  }
  .p-treeselect.p-variant-filled {
    background-color: #f8fafc !important;
  }
  .p-treeselect.p-variant-filled:enabled:hover {
    background-color: #f8fafc !important;
  }
  .p-treeselect.p-variant-filled:enabled:focus {
    background-color: #ffffff !important;
  }

  p-treeselect.ng-invalid.ng-dirty > .p-treeselect {
    border-color: #f87171 !important;
  }

  .p-inputwrapper-filled .p-treeselect.p-treeselect-chip .p-treeselect-label {
    padding: 0.25rem 0.75rem !important;
  }

  .p-treeselect-panel {
    background: #ffffff !important;
    color: #334155 !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 6px !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1) !important;
  }
  .p-treeselect-panel .p-treeselect-header {
    padding: 0.5rem 0.5rem 0 0.5rem !important;
    border-bottom: 0 none !important;
    color: #334155 !important;
    background: #ffffff !important;
    margin: 0 0 0 0 !important;
    border-top-right-radius: 6px !important;
    border-top-left-radius: 6px !important;
  }
  .p-treeselect-panel .p-treeselect-header .p-treeselect-filter-container {
    margin-right: 0.5rem !important;
  }
  .p-treeselect-panel .p-treeselect-header .p-treeselect-filter-container .p-treeselect-filter {
    padding-right: 1.75rem !important;
  }
  .p-treeselect-panel .p-treeselect-header .p-treeselect-filter-container .p-treeselect-filter-icon {
    right: 0.75rem !important;
    color: #94a3b8 !important;
  }
  .p-treeselect-panel .p-treeselect-header .p-treeselect-filter-container.p-treeselect-clearable-filter .p-treeselect-filter {
    padding-right: 3.5rem !important;
  }
  .p-treeselect-panel .p-treeselect-header .p-treeselect-filter-container.p-treeselect-clearable-filter .p-treeselect-filter-clear-icon {
    right: 2.5rem !important;
  }
  .p-treeselect-panel .p-treeselect-header .p-treeselect-close {
    width: 1.75rem !important;
    height: 1.75rem !important;
    color: #64748b !important;
    border: 0 none !important;
    background: transparent !important;
    border-radius: 50% !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-treeselect-panel .p-treeselect-header .p-treeselect-close:enabled:hover {
    color: #475569 !important;
    border-color: transparent !important;
    background: #f1f5f9 !important;
  }
  .p-treeselect-panel .p-treeselect-header .p-treeselect-close:focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }
  .p-treeselect-panel .p-treeselect-items-wrapper .p-tree {
    border: 0 none !important;
  }
  .p-treeselect-panel .p-treeselect-items-wrapper .p-treeselect-empty-message {
    padding: 0.5rem 0.75rem !important;
    color: #334155 !important;
    background: transparent !important;
  }

  .p-input-filled .p-treeselect {
    background: #f8fafc !important;
  }
  .p-input-filled .p-treeselect:not(.p-disabled):hover {
    background-color: #f8fafc !important;
  }
  .p-input-filled .p-treeselect:not(.p-disabled).p-focus {
    background-color: #ffffff !important;
  }

  p-treeselect.p-treeselect-clearable .p-treeselect-label-container {
    padding-right: 1.75rem !important;
  }
  p-treeselect.p-treeselect-clearable .p-treeselect-clear-icon {
    color: #94a3b8 !important;
    right: 2.5rem !important;
  }

  .p-button {
    color: #ffffff !important;
    background: #3B82F6 !important;
    border: 1px solid #3B82F6 !important;
    padding: 0.5rem 1rem !important;
    font-size: 1rem !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border-radius: 6px !important;
    outline-color: transparent !important;
  }
  .p-button:not(:disabled):hover {
    background: #2563eb !important;
    color: #ffffff !important;
    border-color: #2563eb !important;
  }
  .p-button:not(:disabled):active {
    background: #1D4ED8 !important;
    color: #ffffff !important;
    border-color:transparent;
  }
  .p-button.p-button-outlined {
    background-color: transparent !important;
    color: #3B82F6 !important;
    border: 2px solid !important;
  }
  .p-button.p-button-outlined:not(:disabled):hover {
    background: rgba(59, 130, 246, 0.04) !important;
    color: #3B82F6 !important;
    border: 2px solid !important;
  }
  .p-button.p-button-outlined:not(:disabled):active {
    background: rgba(59, 130, 246, 0.16) !important;
    color: #3B82F6 !important;
    border: 2px solid !important;
  }
  .p-button.p-button-outlined.p-button-plain {
    color: #334155 !important;
    border-color: #334155 !important;
  }
  .p-button.p-button-outlined.p-button-plain:not(:disabled):hover {
    background: #f1f5f9 !important;
    color: #334155 !important;
  }
  .p-button.p-button-outlined.p-button-plain:not(:disabled):active {
    background: #e2e8f0 !important;
    color: #334155 !important;
  }
  .p-button.p-button-text {
    background-color: transparent !important;
    color: #3B82F6 !important;
    border-color: transparent !important;
  }
  .p-button.p-button-text:not(:disabled):hover {
    background: rgba(59, 130, 246, 0.04) !important;
    color: #3B82F6 !important;
    border-color: transparent !important;
  }
  .p-button.p-button-text:not(:disabled):active {
    background: rgba(59, 130, 246, 0.16) !important;
    color: #3B82F6 !important;
    border-color: transparent !important;
  }
  .p-button.p-button-text.p-button-plain {
    color: #334155 !important;
  }
  .p-button.p-button-text.p-button-plain:not(:disabled):hover {
    background: #f1f5f9 !important;
    color: #334155 !important;
  }
  .p-button.p-button-text.p-button-plain:not(:disabled):active {
    background: #e2e8f0 !important;
    color: #334155 !important;
  }
  .p-button:focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }
  .p-button .p-button-label {
    transition-duration: 0.2s !important;
  }
  .p-button .p-button-icon-left {
    margin-right: 0.5rem !important;
  }
  .p-button .p-button-icon-right {
    margin-left: 0.5rem !important;
  }
  .p-button .p-button-icon-bottom {
    margin-top: 0.5rem !important;
  }
  .p-button .p-button-icon-top {
    margin-bottom: 0.5rem !important;
  }
  .p-button .p-badge {
    margin-left: 0.5rem !important;
    min-width: 1rem !important;
    height: 1rem !important;
    line-height: 1rem !important;
    color: #3B82F6 !important;
    background-color: #ffffff !important;
  }
  .p-button.p-button-raised {
    box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12) !important;
  }
  .p-button.p-button-rounded {
    border-radius: 2rem !important;
  }
  .p-button.p-button-icon-only {
    width: 2rem !important;
    padding: 0.5rem 0 !important;
  }
  .p-button.p-button-icon-only .p-button-icon-left,
.p-button.p-button-icon-only .p-button-icon-right {
    margin: 0 !important;
  }
  .p-button.p-button-icon-only.p-button-rounded {
    border-radius: 50% !important;
    height: 2rem !important;
  }
  .p-button.p-button-sm {
    font-size: 0.875rem !important;
    padding: 0.4375rem 0.875rem !important;
  }
  .p-button.p-button-sm .p-button-icon {
    font-size: 0.875rem !important;
  }
  .p-button.p-button-lg {
    font-size: 1.25rem !important;
    padding: 0.625rem 1.25rem !important;
  }
  .p-button.p-button-lg .p-button-icon {
    font-size: 1.25rem !important;
  }
  .p-button.p-button-loading-label-only .p-button-label {
    margin-left: 0.5rem !important;
  }
  .p-button.p-button-loading-label-only .p-button-loading-icon {
    margin-right: 0 !important;
  }

  .p-fluid .p-button {
    width: 100% !important;
  }
  .p-fluid .p-button-icon-only {
    width: 2.5rem !important;
  }
  .p-fluid .p-button-group {
    display: flex !important;
  }
  .p-fluid .p-button-group .p-button {
    flex: 1 !important;
  }

  .p-button.p-button-secondary, .p-button-group.p-button-secondary > .p-button, .p-splitbutton.p-button-secondary > .p-button {
    color: #475569 !important;
    background: #f1f5f9 !important;
    border: 1px solid #f1f5f9 !important;
  }
  .p-button.p-button-secondary:not(:disabled):hover, .p-button-group.p-button-secondary > .p-button:not(:disabled):hover, .p-splitbutton.p-button-secondary > .p-button:not(:disabled):hover {
    background: #e2e8f0 !important;
    color: #334155 !important;
    border-color: #e2e8f0 !important;
  }
  .p-button.p-button-secondary:not(:disabled):focus, .p-button-group.p-button-secondary > .p-button:not(:disabled):focus, .p-splitbutton.p-button-secondary > .p-button:not(:disabled):focus {
    box-shadow: none !important;
  }
  .p-button.p-button-secondary:not(:disabled):active, .p-button-group.p-button-secondary > .p-button:not(:disabled):active, .p-splitbutton.p-button-secondary > .p-button:not(:disabled):active {
    background: #cbd5e1 !important;
    color: #1e293b !important;
    border-color: #cbd5e1 !important;
  }
  .p-button.p-button-secondary.p-button-outlined, .p-button-group.p-button-secondary > .p-button.p-button-outlined, .p-splitbutton.p-button-secondary > .p-button.p-button-outlined {
    background-color: transparent !important;
    color: #f1f5f9 !important;
    border: 2px solid !important;
  }
  .p-button.p-button-secondary.p-button-outlined:not(:disabled):hover, .p-button-group.p-button-secondary > .p-button.p-button-outlined:not(:disabled):hover, .p-splitbutton.p-button-secondary > .p-button.p-button-outlined:not(:disabled):hover {
    background: rgba(241, 245, 249, 0.04) !important;
    color: #f1f5f9 !important;
    border: 2px solid !important;
  }
  .p-button.p-button-secondary.p-button-outlined:not(:disabled):active, .p-button-group.p-button-secondary > .p-button.p-button-outlined:not(:disabled):active, .p-splitbutton.p-button-secondary > .p-button.p-button-outlined:not(:disabled):active {
    background: rgba(241, 245, 249, 0.16) !important;
    color: #f1f5f9 !important;
    border: 2px solid !important;
  }
  .p-button.p-button-secondary.p-button-text, .p-button-group.p-button-secondary > .p-button.p-button-text, .p-splitbutton.p-button-secondary > .p-button.p-button-text {
    background-color: transparent !important;
    color: #f1f5f9 !important;
    border-color: transparent !important;
  }
  .p-button.p-button-secondary.p-button-text:not(:disabled):hover, .p-button-group.p-button-secondary > .p-button.p-button-text:not(:disabled):hover, .p-splitbutton.p-button-secondary > .p-button.p-button-text:not(:disabled):hover {
    background: rgba(241, 245, 249, 0.04) !important;
    border-color: transparent !important;
    color: #f1f5f9 !important;
  }
  .p-button.p-button-secondary.p-button-text:not(:disabled):active, .p-button-group.p-button-secondary > .p-button.p-button-text:not(:disabled):active, .p-splitbutton.p-button-secondary > .p-button.p-button-text:not(:disabled):active {
    background: rgba(241, 245, 249, 0.16) !important;
    border-color: transparent !important;
    color: #f1f5f9 !important;
  }

  .p-button.p-button-info, .p-button-group.p-button-info > .p-button, .p-splitbutton.p-button-info > .p-button {
    color: #ffffff !important;
    background: #0ea5e9 !important;
    border: 1px solid #0ea5e9 !important;
  }
  .p-button.p-button-info:not(:disabled):hover, .p-button-group.p-button-info > .p-button:not(:disabled):hover, .p-splitbutton.p-button-info > .p-button:not(:disabled):hover {
    background: #0284c7 !important;
    color: #ffffff !important;
    border-color: #0284c7 !important;
  }
  .p-button.p-button-info:not(:disabled):focus, .p-button-group.p-button-info > .p-button:not(:disabled):focus, .p-splitbutton.p-button-info > .p-button:not(:disabled):focus {
    box-shadow: none !important;
  }
  .p-button.p-button-info:not(:disabled):active, .p-button-group.p-button-info > .p-button:not(:disabled):active, .p-splitbutton.p-button-info > .p-button:not(:disabled):active {
    background: #0369a1 !important;
    color: #ffffff !important;
    border-color: #0369a1 !important;
  }
  .p-button.p-button-info.p-button-outlined, .p-button-group.p-button-info > .p-button.p-button-outlined, .p-splitbutton.p-button-info > .p-button.p-button-outlined {
    background-color: transparent !important;
    color: #0ea5e9 !important;
    border: 2px solid !important;
  }
  .p-button.p-button-info.p-button-outlined:not(:disabled):hover, .p-button-group.p-button-info > .p-button.p-button-outlined:not(:disabled):hover, .p-splitbutton.p-button-info > .p-button.p-button-outlined:not(:disabled):hover {
    background: rgba(14, 165, 233, 0.04) !important;
    color: #0ea5e9 !important;
    border: 2px solid !important;
  }
  .p-button.p-button-info.p-button-outlined:not(:disabled):active, .p-button-group.p-button-info > .p-button.p-button-outlined:not(:disabled):active, .p-splitbutton.p-button-info > .p-button.p-button-outlined:not(:disabled):active {
    background: rgba(14, 165, 233, 0.16) !important;
    color: #0ea5e9 !important;
    border: 2px solid !important;
  }
  .p-button.p-button-info.p-button-text, .p-button-group.p-button-info > .p-button.p-button-text, .p-splitbutton.p-button-info > .p-button.p-button-text {
    background-color: transparent !important;
    color: #0ea5e9 !important;
    border-color: transparent !important;
  }
  .p-button.p-button-info.p-button-text:not(:disabled):hover, .p-button-group.p-button-info > .p-button.p-button-text:not(:disabled):hover, .p-splitbutton.p-button-info > .p-button.p-button-text:not(:disabled):hover {
    background: rgba(14, 165, 233, 0.04) !important;
    border-color: transparent !important;
    color: #0ea5e9 !important;
  }
  .p-button.p-button-info.p-button-text:not(:disabled):active, .p-button-group.p-button-info > .p-button.p-button-text:not(:disabled):active, .p-splitbutton.p-button-info > .p-button.p-button-text:not(:disabled):active {
    background: rgba(14, 165, 233, 0.16) !important;
    border-color: transparent !important;
    color: #0ea5e9 !important;
  }

  .p-button.p-button-success, .p-button-group.p-button-success > .p-button, .p-splitbutton.p-button-success > .p-button {
    color: #ffffff !important;
    background: #22c55e !important;
    border: 1px solid #22c55e !important;
  }
  .p-button.p-button-success:not(:disabled):hover, .p-button-group.p-button-success > .p-button:not(:disabled):hover, .p-splitbutton.p-button-success > .p-button:not(:disabled):hover {
    background: #16a34a !important;
    color: #ffffff !important;
    border-color: #16a34a !important;
  }
  .p-button.p-button-success:not(:disabled):focus, .p-button-group.p-button-success > .p-button:not(:disabled):focus, .p-splitbutton.p-button-success > .p-button:not(:disabled):focus {
    box-shadow: none !important;
  }
  .p-button.p-button-success:not(:disabled):active, .p-button-group.p-button-success > .p-button:not(:disabled):active, .p-splitbutton.p-button-success > .p-button:not(:disabled):active {
    background: #15803d !important;
    color: #ffffff !important;
    border-color: #15803d !important;
  }
  .p-button.p-button-success.p-button-outlined, .p-button-group.p-button-success > .p-button.p-button-outlined, .p-splitbutton.p-button-success > .p-button.p-button-outlined {
    background-color: transparent !important;
    color: #22c55e !important;
    border: 2px solid !important;
  }
  .p-button.p-button-success.p-button-outlined:not(:disabled):hover, .p-button-group.p-button-success > .p-button.p-button-outlined:not(:disabled):hover, .p-splitbutton.p-button-success > .p-button.p-button-outlined:not(:disabled):hover {
    background: rgba(34, 197, 94, 0.04) !important;
    color: #22c55e !important;
    border: 2px solid !important;
  }
  .p-button.p-button-success.p-button-outlined:not(:disabled):active, .p-button-group.p-button-success > .p-button.p-button-outlined:not(:disabled):active, .p-splitbutton.p-button-success > .p-button.p-button-outlined:not(:disabled):active {
    background: rgba(34, 197, 94, 0.16) !important;
    color: #22c55e !important;
    border: 2px solid !important;
  }
  .p-button.p-button-success.p-button-text, .p-button-group.p-button-success > .p-button.p-button-text, .p-splitbutton.p-button-success > .p-button.p-button-text {
    background-color: transparent !important;
    color: #22c55e !important;
    border-color: transparent !important;
  }
  .p-button.p-button-success.p-button-text:not(:disabled):hover, .p-button-group.p-button-success > .p-button.p-button-text:not(:disabled):hover, .p-splitbutton.p-button-success > .p-button.p-button-text:not(:disabled):hover {
    background: rgba(34, 197, 94, 0.04) !important;
    border-color: transparent !important;
    color: #22c55e !important;
  }
  .p-button.p-button-success.p-button-text:not(:disabled):active, .p-button-group.p-button-success > .p-button.p-button-text:not(:disabled):active, .p-splitbutton.p-button-success > .p-button.p-button-text:not(:disabled):active {
    background: rgba(34, 197, 94, 0.16) !important;
    border-color: transparent !important;
    color: #22c55e !important;
  }

  .p-button.p-button-warning, .p-button-group.p-button-warning > .p-button, .p-splitbutton.p-button-warning > .p-button {
    color: #ffffff !important;
    background: #f97316 !important;
    border: 1px solid #f97316 !important;
  }
  .p-button.p-button-warning:not(:disabled):hover, .p-button-group.p-button-warning > .p-button:not(:disabled):hover, .p-splitbutton.p-button-warning > .p-button:not(:disabled):hover {
    background: #ea580c !important;
    color: #ffffff !important;
    border-color: #ea580c !important;
  }
  .p-button.p-button-warning:not(:disabled):focus, .p-button-group.p-button-warning > .p-button:not(:disabled):focus, .p-splitbutton.p-button-warning > .p-button:not(:disabled):focus {
    box-shadow: none !important;
  }
  .p-button.p-button-warning:not(:disabled):active, .p-button-group.p-button-warning > .p-button:not(:disabled):active, .p-splitbutton.p-button-warning > .p-button:not(:disabled):active {
    background: #c2410c !important;
    color: #ffffff !important;
    border-color: #c2410c !important;
  }
  .p-button.p-button-warning.p-button-outlined, .p-button-group.p-button-warning > .p-button.p-button-outlined, .p-splitbutton.p-button-warning > .p-button.p-button-outlined {
    background-color: transparent !important;
    color: #f97316 !important;
    border: 2px solid !important;
  }
  .p-button.p-button-warning.p-button-outlined:not(:disabled):hover, .p-button-group.p-button-warning > .p-button.p-button-outlined:not(:disabled):hover, .p-splitbutton.p-button-warning > .p-button.p-button-outlined:not(:disabled):hover {
    background: rgba(249, 115, 22, 0.04) !important;
    color: #f97316 !important;
    border: 2px solid !important;
  }
  .p-button.p-button-warning.p-button-outlined:not(:disabled):active, .p-button-group.p-button-warning > .p-button.p-button-outlined:not(:disabled):active, .p-splitbutton.p-button-warning > .p-button.p-button-outlined:not(:disabled):active {
    background: rgba(249, 115, 22, 0.16) !important;
    color: #f97316 !important;
    border: 2px solid !important;
  }
  .p-button.p-button-warning.p-button-text, .p-button-group.p-button-warning > .p-button.p-button-text, .p-splitbutton.p-button-warning > .p-button.p-button-text {
    background-color: transparent !important;
    color: #f97316 !important;
    border-color: transparent !important;
  }
  .p-button.p-button-warning.p-button-text:not(:disabled):hover, .p-button-group.p-button-warning > .p-button.p-button-text:not(:disabled):hover, .p-splitbutton.p-button-warning > .p-button.p-button-text:not(:disabled):hover {
    background: rgba(249, 115, 22, 0.04) !important;
    border-color: transparent !important;
    color: #f97316 !important;
  }
  .p-button.p-button-warning.p-button-text:not(:disabled):active, .p-button-group.p-button-warning > .p-button.p-button-text:not(:disabled):active, .p-splitbutton.p-button-warning > .p-button.p-button-text:not(:disabled):active {
    background: rgba(249, 115, 22, 0.16) !important;
    border-color: transparent !important;
    color: #f97316 !important;
  }

  .p-button.p-button-help, .p-button-group.p-button-help > .p-button, .p-splitbutton.p-button-help > .p-button {
    color: #ffffff !important;
    background: #a855f7 !important;
    border: 1px solid #a855f7 !important;
  }
  .p-button.p-button-help:not(:disabled):hover, .p-button-group.p-button-help > .p-button:not(:disabled):hover, .p-splitbutton.p-button-help > .p-button:not(:disabled):hover {
    background: #9333ea !important;
    color: #ffffff !important;
    border-color: #9333ea !important;
  }
  .p-button.p-button-help:not(:disabled):focus, .p-button-group.p-button-help > .p-button:not(:disabled):focus, .p-splitbutton.p-button-help > .p-button:not(:disabled):focus {
    box-shadow: none !important;
  }
  .p-button.p-button-help:not(:disabled):active, .p-button-group.p-button-help > .p-button:not(:disabled):active, .p-splitbutton.p-button-help > .p-button:not(:disabled):active {
    background: #7e22ce !important;
    color: #ffffff !important;
    border-color: #7e22ce !important;
  }
  .p-button.p-button-help.p-button-outlined, .p-button-group.p-button-help > .p-button.p-button-outlined, .p-splitbutton.p-button-help > .p-button.p-button-outlined {
    background-color: transparent !important;
    color: #a855f7 !important;
    border: 2px solid !important;
  }
  .p-button.p-button-help.p-button-outlined:not(:disabled):hover, .p-button-group.p-button-help > .p-button.p-button-outlined:not(:disabled):hover, .p-splitbutton.p-button-help > .p-button.p-button-outlined:not(:disabled):hover {
    background: rgba(168, 85, 247, 0.04) !important;
    color: #a855f7 !important;
    border: 2px solid !important;
  }
  .p-button.p-button-help.p-button-outlined:not(:disabled):active, .p-button-group.p-button-help > .p-button.p-button-outlined:not(:disabled):active, .p-splitbutton.p-button-help > .p-button.p-button-outlined:not(:disabled):active {
    background: rgba(168, 85, 247, 0.16) !important;
    color: #a855f7 !important;
    border: 2px solid !important;
  }
  .p-button.p-button-help.p-button-text, .p-button-group.p-button-help > .p-button.p-button-text, .p-splitbutton.p-button-help > .p-button.p-button-text {
    background-color: transparent !important;
    color: #a855f7 !important;
    border-color: transparent !important;
  }
  .p-button.p-button-help.p-button-text:not(:disabled):hover, .p-button-group.p-button-help > .p-button.p-button-text:not(:disabled):hover, .p-splitbutton.p-button-help > .p-button.p-button-text:not(:disabled):hover {
    background: rgba(168, 85, 247, 0.04) !important;
    border-color: transparent !important;
    color: #a855f7 !important;
  }
  .p-button.p-button-help.p-button-text:not(:disabled):active, .p-button-group.p-button-help > .p-button.p-button-text:not(:disabled):active, .p-splitbutton.p-button-help > .p-button.p-button-text:not(:disabled):active {
    background: rgba(168, 85, 247, 0.16) !important;
    border-color: transparent !important;
    color: #a855f7 !important;
  }

  .p-button.p-button-danger, .p-button-group.p-button-danger > .p-button, .p-splitbutton.p-button-danger > .p-button {
    color: #ffffff !important;
    background: #ef4444 !important;
    border: 1px solid #ef4444 !important;
  }
  .p-button.p-button-danger:not(:disabled):hover, .p-button-group.p-button-danger > .p-button:not(:disabled):hover, .p-splitbutton.p-button-danger > .p-button:not(:disabled):hover {
    background: #dc2626 !important;
    color: #ffffff !important;
    border-color: #dc2626 !important;
  }
  .p-button.p-button-danger:not(:disabled):focus, .p-button-group.p-button-danger > .p-button:not(:disabled):focus, .p-splitbutton.p-button-danger > .p-button:not(:disabled):focus {
    box-shadow: none !important;
  }
  .p-button.p-button-danger:not(:disabled):active, .p-button-group.p-button-danger > .p-button:not(:disabled):active, .p-splitbutton.p-button-danger > .p-button:not(:disabled):active {
    background: #b91c1c !important;
    color: #ffffff !important;
    border-color: #b91c1c !important;
  }
  .p-button.p-button-danger.p-button-outlined, .p-button-group.p-button-danger > .p-button.p-button-outlined, .p-splitbutton.p-button-danger > .p-button.p-button-outlined {
    background-color: transparent !important;
    color: #ef4444 !important;
    border: 2px solid !important;
  }
  .p-button.p-button-danger.p-button-outlined:not(:disabled):hover, .p-button-group.p-button-danger > .p-button.p-button-outlined:not(:disabled):hover, .p-splitbutton.p-button-danger > .p-button.p-button-outlined:not(:disabled):hover {
    background: rgba(239, 68, 68, 0.04) !important;
    color: #ef4444 !important;
    border: 2px solid !important;
  }
  .p-button.p-button-danger.p-button-outlined:not(:disabled):active, .p-button-group.p-button-danger > .p-button.p-button-outlined:not(:disabled):active, .p-splitbutton.p-button-danger > .p-button.p-button-outlined:not(:disabled):active {
    background: rgba(239, 68, 68, 0.16) !important;
    color: #ef4444 !important;
    border: 2px solid !important;
  }
  .p-button.p-button-danger.p-button-text, .p-button-group.p-button-danger > .p-button.p-button-text, .p-splitbutton.p-button-danger > .p-button.p-button-text {
    background-color: transparent !important;
    color: #ef4444 !important;
    border-color: transparent !important;
  }
  .p-button.p-button-danger.p-button-text:not(:disabled):hover, .p-button-group.p-button-danger > .p-button.p-button-text:not(:disabled):hover, .p-splitbutton.p-button-danger > .p-button.p-button-text:not(:disabled):hover {
    background: rgba(239, 68, 68, 0.04) !important;
    border-color: transparent !important;
    color: #ef4444 !important;
  }
  .p-button.p-button-danger.p-button-text:not(:disabled):active, .p-button-group.p-button-danger > .p-button.p-button-text:not(:disabled):active, .p-splitbutton.p-button-danger > .p-button.p-button-text:not(:disabled):active {
    background: rgba(239, 68, 68, 0.16) !important;
    border-color: transparent !important;
    color: #ef4444 !important;
  }

  .p-button.p-button-contrast, .p-button-group.p-button-contrast > .p-button, .p-splitbutton.p-button-contrast > .p-button {
    color: #ffffff !important;
    background: #020617 !important;
    border: 1px solid #020617 !important;
  }
  .p-button.p-button-contrast:not(:disabled):hover, .p-button-group.p-button-contrast > .p-button:not(:disabled):hover, .p-splitbutton.p-button-contrast > .p-button:not(:disabled):hover {
    background: #1e293b !important;
    color: #ffffff !important;
    border-color: #1e293b !important;
  }
  .p-button.p-button-contrast:not(:disabled):focus, .p-button-group.p-button-contrast > .p-button:not(:disabled):focus, .p-splitbutton.p-button-contrast > .p-button:not(:disabled):focus {
    box-shadow: none !important;
  }
  .p-button.p-button-contrast:not(:disabled):active, .p-button-group.p-button-contrast > .p-button:not(:disabled):active, .p-splitbutton.p-button-contrast > .p-button:not(:disabled):active {
    background: #334155 !important;
    color: #ffffff !important;
    border-color: #334155 !important;
  }
  .p-button.p-button-contrast.p-button-outlined, .p-button-group.p-button-contrast > .p-button.p-button-outlined, .p-splitbutton.p-button-contrast > .p-button.p-button-outlined {
    background-color: transparent !important;
    color: #020617 !important;
    border: 2px solid !important;
  }
  .p-button.p-button-contrast.p-button-outlined:not(:disabled):hover, .p-button-group.p-button-contrast > .p-button.p-button-outlined:not(:disabled):hover, .p-splitbutton.p-button-contrast > .p-button.p-button-outlined:not(:disabled):hover {
    background: rgba(2, 6, 23, 0.04) !important;
    color: #020617 !important;
    border: 2px solid !important;
  }
  .p-button.p-button-contrast.p-button-outlined:not(:disabled):active, .p-button-group.p-button-contrast > .p-button.p-button-outlined:not(:disabled):active, .p-splitbutton.p-button-contrast > .p-button.p-button-outlined:not(:disabled):active {
    background: rgba(2, 6, 23, 0.16) !important;
    color: #020617 !important;
    border: 2px solid !important;
  }
  .p-button.p-button-contrast.p-button-text, .p-button-group.p-button-contrast > .p-button.p-button-text, .p-splitbutton.p-button-contrast > .p-button.p-button-text {
    background-color: transparent !important;
    color: #020617 !important;
    border-color: transparent !important;
  }
  .p-button.p-button-contrast.p-button-text:not(:disabled):hover, .p-button-group.p-button-contrast > .p-button.p-button-text:not(:disabled):hover, .p-splitbutton.p-button-contrast > .p-button.p-button-text:not(:disabled):hover {
    background: rgba(2, 6, 23, 0.04) !important;
    border-color: transparent !important;
    color: #020617 !important;
  }
  .p-button.p-button-contrast.p-button-text:not(:disabled):active, .p-button-group.p-button-contrast > .p-button.p-button-text:not(:disabled):active, .p-splitbutton.p-button-contrast > .p-button.p-button-text:not(:disabled):active {
    background: rgba(2, 6, 23, 0.16) !important;
    border-color: transparent !important;
    color: #020617 !important;
  }

  .p-button.p-button-link {
    color: #2563eb !important;
    background: transparent !important;
    border: transparent !important;
  }
  .p-button.p-button-link:not(:disabled):hover {
    background: transparent !important;
    color: #2563eb !important;
    border-color: transparent !important;
  }
  .p-button.p-button-link:not(:disabled):hover .p-button-label {
    text-decoration: underline !important;
  }
  .p-button.p-button-link:not(:disabled):focus {
    background: transparent !important;
    box-shadow: none !important;
    border-color: transparent !important;
  }
  .p-button.p-button-link:not(:disabled):active {
    background: transparent !important;
    color: #2563eb !important;
    border-color: transparent !important;
  }

  .p-speeddial-button.p-button.p-button-icon-only {
    width: 2.5rem !important;
    height: 2.5rem !important;
  }
  .p-speeddial-button.p-button.p-button-icon-only .p-button-icon {
    font-size: 1rem !important;
  }
  .p-speeddial-button.p-button.p-button-icon-only .p-icon {
    width: 1rem !important;
    height: 1rem !important;
  }

  .p-speeddial-list {
    outline: 0 none !important;
  }

  .p-speeddial-item.p-focus > .p-speeddial-action {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }

  .p-speeddial-action {
    width: 2.5rem !important;
    height: 2.5rem !important;
    background: #f1f5f9 !important;
    color: #475569 !important;
  }
  .p-speeddial-action:hover {
    background: #e2e8f0 !important;
    color: #334155 !important;
  }

  .p-speeddial-direction-up .p-speeddial-item {
    margin: 0.25rem 0 !important;
  }
  .p-speeddial-direction-up .p-speeddial-item:first-child {
    margin-bottom: 0.5rem !important;
  }

  .p-speeddial-direction-down .p-speeddial-item {
    margin: 0.25rem 0 !important;
  }
  .p-speeddial-direction-down .p-speeddial-item:first-child {
    margin-top: 0.5rem !important;
  }

  .p-speeddial-direction-left .p-speeddial-item {
    margin: 0 0.25rem !important;
  }
  .p-speeddial-direction-left .p-speeddial-item:first-child {
    margin-right: 0.5rem !important;
  }

  .p-speeddial-direction-right .p-speeddial-item {
    margin: 0 0.25rem !important;
  }
  .p-speeddial-direction-right .p-speeddial-item:first-child {
    margin-left: 0.5rem !important;
  }

  .p-speeddial-circle .p-speeddial-item,
.p-speeddial-semi-circle .p-speeddial-item,
.p-speeddial-quarter-circle .p-speeddial-item {
    margin: 0 !important;
  }
  .p-speeddial-circle .p-speeddial-item:first-child, .p-speeddial-circle .p-speeddial-item:last-child,
.p-speeddial-semi-circle .p-speeddial-item:first-child,
.p-speeddial-semi-circle .p-speeddial-item:last-child,
.p-speeddial-quarter-circle .p-speeddial-item:first-child,
.p-speeddial-quarter-circle .p-speeddial-item:last-child {
    margin: 0 !important;
  }

  .p-speeddial-mask {
    background-color: rgba(0, 0, 0, 0.4) !important;
  }

  .p-splitbutton {
    border-radius: 6px !important;
  }
  .p-splitbutton.p-button-rounded {
    border-radius: 2rem !important;
  }
  .p-splitbutton.p-button-rounded > .p-button {
    border-radius: 2rem !important;
  }
  .p-splitbutton.p-button-raised {
    box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12) !important;
  }

  .p-carousel .p-carousel-content .p-carousel-prev,
.p-carousel .p-carousel-content .p-carousel-next {
    width: 1.75rem !important;
    height: 1.75rem !important;
    color: #64748b !important;
    border: 0 none !important;
    background: transparent !important;
    border-radius: 50% !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    margin: 0.5rem !important;
  }
  .p-carousel .p-carousel-content .p-carousel-prev:enabled:hover,
.p-carousel .p-carousel-content .p-carousel-next:enabled:hover {
    color: #475569 !important;
    border-color: transparent !important;
    background: #f1f5f9 !important;
  }
  .p-carousel .p-carousel-content .p-carousel-prev:focus-visible,
.p-carousel .p-carousel-content .p-carousel-next:focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }
  .p-carousel .p-carousel-indicators {
    padding: 1rem !important;
  }
  .p-carousel .p-carousel-indicators .p-carousel-indicator {
    margin-right: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  .p-carousel .p-carousel-indicators .p-carousel-indicator button {
    background-color: #e2e8f0 !important;
    width: 2rem !important;
    height: 0.5rem !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border-radius: 6px !important;
  }
  .p-carousel .p-carousel-indicators .p-carousel-indicator button:hover {
    background: #cbd5e1 !important;
  }
  .p-carousel .p-carousel-indicators .p-carousel-indicator.p-highlight button {
    background: #EFF6FF !important;
    color: #1D4ED8 !important;
  }

  .p-datatable .p-paginator-top {
    border-width: 0 0 1px 0 !important;
    border-radius: 0 !important;
  }
  .p-datatable .p-paginator-bottom {
    border-width: 0 0 1px 0 !important;
    border-radius: 0 !important;
  }
  .p-datatable .p-datatable-header {
    background: #ffffff !important;
    color: #334155 !important;
    border: 1px solid #e2e8f0 !important;
    border-width: 0 0 1px 0 !important;
    padding: 0.75rem 1rem !important;
    font-weight: 600 !important;
  }
  .p-datatable .p-datatable-footer {
    background: #ffffff !important;
    color: #334155 !important;
    border: 1px solid #e2e8f0 !important;
    border-width: 0 0 1px 0 !important;
    padding: 0.75rem 1rem !important;
    font-weight: 600 !important;
  }
  .p-datatable .p-datatable-thead > tr > th {
    text-align: left !important;
    padding: 0.75rem 1rem !important;
    border: 1px solid #e2e8f0 !important;
    border-width: 0 0 1px 0 !important;
    font-weight: 600 !important;
    color: #334155 !important;
    background: #ffffff !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-datatable .p-datatable-tfoot > tr > td {
    text-align: left !important;
    padding: 0.75rem 1rem !important;
    border: 1px solid #e2e8f0 !important;
    border-width: 0 0 1px 0 !important;
    font-weight: 600 !important;
    color: #334155 !important;
    background: #ffffff !important;
  }
  .p-datatable .p-sortable-column .p-sortable-column-icon {
    color: #64748b !important;
    margin-left: 0.5rem !important;
  }
  .p-datatable .p-sortable-column .p-sortable-column-badge {
    border-radius: 50% !important;
    height: 1rem !important;
    min-width: 1rem !important;
    line-height: 1rem !important;
    color: #1D4ED8 !important;
    background: #EFF6FF !important;
    margin-left: 0.5rem !important;
  }
  .p-datatable .p-sortable-column:not(.p-highlight):hover {
    background: #f1f5f9 !important;
    color: #1e293b !important;
  }
  .p-datatable .p-sortable-column:not(.p-highlight):hover .p-sortable-column-icon {
    color: #475569 !important;
  }
  .p-datatable .p-sortable-column.p-highlight {
    background: #EFF6FF !important;
    color: #1D4ED8 !important;
  }
  .p-datatable .p-sortable-column.p-highlight .p-sortable-column-icon {
    color: #1D4ED8 !important;
  }
  .p-datatable .p-sortable-column.p-highlight:hover {
    background: #EFF6FF !important;
    color: #1D4ED8 !important;
  }
  .p-datatable .p-sortable-column.p-highlight:hover .p-sortable-column-icon {
    color: #1D4ED8 !important;
  }
  .p-datatable .p-sortable-column:focus-visible {
    box-shadow: 0 none !important;
    outline: 0 none !important;
  }
  .p-datatable .p-datatable-tbody > tr {
    background: #ffffff !important;
    color: #334155 !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-datatable .p-datatable-tbody > tr > td {
    text-align: left !important;
    border: 1px solid #e2e8f0 !important;
    border-width: 0 0 1px 0 !important;
    padding: 0.75rem 1rem !important;
  }
  .p-datatable .p-datatable-tbody > tr > td .p-row-toggler,
.p-datatable .p-datatable-tbody > tr > td .p-row-editor-init,
.p-datatable .p-datatable-tbody > tr > td .p-row-editor-save,
.p-datatable .p-datatable-tbody > tr > td .p-row-editor-cancel {
    width: 1.75rem !important;
    height: 1.75rem !important;
    color: #64748b !important;
    border: 0 none !important;
    background: transparent !important;
    border-radius: 50% !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-datatable .p-datatable-tbody > tr > td .p-row-toggler:enabled:hover,
.p-datatable .p-datatable-tbody > tr > td .p-row-editor-init:enabled:hover,
.p-datatable .p-datatable-tbody > tr > td .p-row-editor-save:enabled:hover,
.p-datatable .p-datatable-tbody > tr > td .p-row-editor-cancel:enabled:hover {
    color: #475569 !important;
    border-color: transparent !important;
    background: #f1f5f9 !important;
  }
  .p-datatable .p-datatable-tbody > tr > td .p-row-toggler:focus-visible,
.p-datatable .p-datatable-tbody > tr > td .p-row-editor-init:focus-visible,
.p-datatable .p-datatable-tbody > tr > td .p-row-editor-save:focus-visible,
.p-datatable .p-datatable-tbody > tr > td .p-row-editor-cancel:focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }
  .p-datatable .p-datatable-tbody > tr > td .p-row-editor-save {
    margin-right: 0.5rem !important;
  }
  .p-datatable .p-datatable-tbody > tr:focus-visible {
    outline: 0.15rem solid var(--p-focus-ring-color) !important;
    outline-offset: -0.15rem !important;
  }
  .p-datatable .p-datatable-tbody > tr.p-highlight {
    background: #EFF6FF !important;
    color: #1D4ED8 !important;
  }
  .p-datatable .p-datatable-tbody > tr.p-datatable-dragpoint-top > td {
    box-shadow: inset 0 2px 0 0 #EFF6FF !important;
  }
  .p-datatable .p-datatable-tbody > tr.p-datatable-dragpoint-bottom > td {
    box-shadow: inset 0 -2px 0 0 #EFF6FF !important;
  }
  .p-datatable.p-datatable-hoverable-rows .p-datatable-tbody > tr:not(.p-highlight):hover {
    background: #f1f5f9 !important;
    color: #1e293b !important;
  }
  .p-datatable .p-column-resizer-helper {
    background: #3B82F6 !important;
  }
  .p-datatable .p-datatable-scrollable-header,
.p-datatable .p-datatable-scrollable-footer {
    background: #ffffff !important;
  }
  .p-datatable.p-datatable-scrollable > .p-datatable-wrapper > .p-datatable-table > .p-datatable-thead,
.p-datatable.p-datatable-scrollable > .p-datatable-wrapper > .p-datatable-table > .p-datatable-tfoot, .p-datatable.p-datatable-scrollable > .p-datatable-wrapper > .p-scroller-viewport > .p-scroller > .p-datatable-table > .p-datatable-thead,
.p-datatable.p-datatable-scrollable > .p-datatable-wrapper > .p-scroller-viewport > .p-scroller > .p-datatable-table > .p-datatable-tfoot {
    background-color: #ffffff !important;
  }
  .p-datatable .p-datatable-loading-icon {
    font-size: 2rem !important;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-header {
    border-width: 1px 1px 0 1px !important;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-footer {
    border-width: 0 1px 1px 1px !important;
  }
  .p-datatable.p-datatable-gridlines .p-paginator-top {
    border-width: 0 1px 0 1px !important;
  }
  .p-datatable.p-datatable-gridlines .p-paginator-bottom {
    border-width: 0 1px 1px 1px !important;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-thead > tr > th {
    border-width: 1px 0 1px 1px !important;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-thead > tr > th:last-child {
    border-width: 1px !important;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-tbody > tr > td {
    border-width: 1px 0 0 1px !important;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-tbody > tr > td:last-child {
    border-width: 1px 1px 0 1px !important;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-tbody > tr:last-child > td {
    border-width: 1px 0 1px 1px !important;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-tbody > tr:last-child > td:last-child {
    border-width: 1px !important;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-tfoot > tr > td {
    border-width: 1px 0 1px 1px !important;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-tfoot > tr > td:last-child {
    border-width: 1px 1px 1px 1px !important;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-thead + .p-datatable-tfoot > tr > td {
    border-width: 0 0 1px 1px !important;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-thead + .p-datatable-tfoot > tr > td:last-child {
    border-width: 0 1px 1px 1px !important;
  }
  .p-datatable.p-datatable-gridlines:has(.p-datatable-thead):has(.p-datatable-tbody) .p-datatable-tbody > tr > td {
    border-width: 0 0 1px 1px !important;
  }
  .p-datatable.p-datatable-gridlines:has(.p-datatable-thead):has(.p-datatable-tbody) .p-datatable-tbody > tr > td:last-child {
    border-width: 0 1px 1px 1px !important;
  }
  .p-datatable.p-datatable-gridlines:has(.p-datatable-tbody):has(.p-datatable-tfoot) .p-datatable-tbody > tr:last-child > td {
    border-width: 0 0 0 1px !important;
  }
  .p-datatable.p-datatable-gridlines:has(.p-datatable-tbody):has(.p-datatable-tfoot) .p-datatable-tbody > tr:last-child > td:last-child {
    border-width: 0 1px 0 1px !important;
  }
  .p-datatable.p-datatable-striped .p-datatable-tbody > tr:nth-child(even) {
    background: #f8fafc !important;
  }
  .p-datatable.p-datatable-striped .p-datatable-tbody > tr:nth-child(even).p-highlight {
    background: #EFF6FF !important;
    color: #1D4ED8 !important;
  }
  .p-datatable.p-datatable-striped .p-datatable-tbody > tr:nth-child(even).p-highlight .p-row-toggler {
    color: #1D4ED8 !important;
  }
  .p-datatable.p-datatable-striped .p-datatable-tbody > tr:nth-child(even).p-highlight .p-row-toggler:hover {
    color: #1D4ED8 !important;
  }
  .p-datatable.p-datatable-sm .p-datatable-header {
    padding: 0.375rem 0.5rem !important;
  }
  .p-datatable.p-datatable-sm .p-datatable-thead > tr > th {
    padding: 0.375rem 0.5rem !important;
  }
  .p-datatable.p-datatable-sm .p-datatable-tbody > tr > td {
    padding: 0.375rem 0.5rem !important;
  }
  .p-datatable.p-datatable-sm .p-datatable-tfoot > tr > td {
    padding: 0.375rem 0.5rem !important;
  }
  .p-datatable.p-datatable-sm .p-datatable-footer {
    padding: 0.375rem 0.5rem !important;
  }
  .p-datatable.p-datatable-lg .p-datatable-header {
    padding: 0.9375rem 1.25rem !important;
  }
  .p-datatable.p-datatable-lg .p-datatable-thead > tr > th {
    padding: 0.9375rem 1.25rem !important;
  }
  .p-datatable.p-datatable-lg .p-datatable-tbody > tr > td {
    padding: 0.9375rem 1.25rem !important;
  }
  .p-datatable.p-datatable-lg .p-datatable-tfoot > tr > td {
    padding: 0.9375rem 1.25rem !important;
  }
  .p-datatable.p-datatable-lg .p-datatable-footer {
    padding: 0.9375rem 1.25rem !important;
  }

  .p-dataview .p-paginator-top {
    border-width: 0 0 1px 0 !important;
    border-radius: 0 !important;
  }
  .p-dataview .p-paginator-bottom {
    border-width: 0 0 1px 0 !important;
    border-radius: 0 !important;
  }
  .p-dataview .p-dataview-header {
    background: #ffffff !important;
    color: #334155 !important;
    border: 1px solid #e2e8f0 !important;
    border-width: 0 0 1px 0 !important;
    padding: 0.75rem 1rem !important;
    font-weight: 600 !important;
  }
  .p-dataview .p-dataview-content {
    background: #ffffff !important;
    color: #334155 !important;
    border: 0 none !important;
    padding: 0 !important;
  }
  .p-dataview .p-dataview-footer {
    background: #ffffff !important;
    color: #334155 !important;
    border: 1px solid #e2e8f0 !important;
    border-width: 0 0 1px 0 !important;
    padding: 0.75rem 1rem !important;
    font-weight: 600 !important;
    border-bottom-left-radius: 6px !important;
    border-bottom-right-radius: 6px !important;
  }
  .p-dataview .p-dataview-loading-icon {
    font-size: 2rem !important;
  }
  .p-dataview .p-dataview-emptymessage {
    padding: 0 1.125rem 1.125rem 1.125rem !important;
  }

  .p-column-filter-row .p-column-filter-menu-button,
.p-column-filter-row .p-column-filter-clear-button {
    margin-left: 0.5rem !important;
  }

  .p-column-filter-menu-button {
    width: 1.75rem !important;
    height: 1.75rem !important;
    color: #64748b !important;
    border: 0 none !important;
    background: transparent !important;
    border-radius: 50% !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-column-filter-menu-button:hover {
    color: #475569 !important;
    border-color: transparent !important;
    background: #f1f5f9 !important;
  }
  .p-column-filter-menu-button.p-column-filter-menu-button-open, .p-column-filter-menu-button.p-column-filter-menu-button-open:hover {
    background: #f1f5f9 !important;
    color: #475569 !important;
  }
  .p-column-filter-menu-button.p-column-filter-menu-button-active, .p-column-filter-menu-button.p-column-filter-menu-button-active:hover {
    background: #EFF6FF !important;
    color: #1D4ED8 !important;
  }
  .p-column-filter-menu-button:focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }

  .p-column-filter-clear-button {
    width: 1.75rem !important;
    height: 1.75rem !important;
    color: #64748b !important;
    border: 0 none !important;
    background: transparent !important;
    border-radius: 50% !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-column-filter-clear-button:hover {
    color: #475569 !important;
    border-color: transparent !important;
    background: #f1f5f9 !important;
  }
  .p-column-filter-clear-button:focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }

  .p-column-filter-overlay {
    background: #ffffff !important;
    color: #334155 !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 6px !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1) !important;
    min-width: 12.5rem !important;
  }
  .p-column-filter-overlay .p-column-filter-row-items {
    padding: 0.25rem 0.25rem !important;
  }
  .p-column-filter-overlay .p-column-filter-row-items .p-column-filter-row-item {
    margin: 2px 0 !important;
    padding: 0.5rem 0.75rem !important;
    border: 0 none !important;
    color: #334155 !important;
    background: transparent !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border-radius: 4px !important;
  }
  .p-column-filter-overlay .p-column-filter-row-items .p-column-filter-row-item:first-child {
    margin-top: 0 !important;
  }
  .p-column-filter-overlay .p-column-filter-row-items .p-column-filter-row-item.p-highlight {
    color: #1D4ED8 !important;
    background: #EFF6FF !important;
  }
  .p-column-filter-overlay .p-column-filter-row-items .p-column-filter-row-item:not(.p-highlight):not(.p-disabled):hover {
    color: #1e293b !important;
    background: #f1f5f9 !important;
  }
  .p-column-filter-overlay .p-column-filter-row-items .p-column-filter-row-item:focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: 0 none !important;
  }
  .p-column-filter-overlay .p-column-filter-row-items .p-column-filter-separator {
    border-top: 1px solid #e2e8f0 !important;
    margin: 2px 0 !important;
  }

  .p-column-filter-overlay-menu .p-column-filter-operator {
    padding: 0.5rem 0.5rem 0 0.5rem !important;
    border-bottom: 0 none !important;
    color: #334155 !important;
    background: #ffffff !important;
    margin: 0 0 0 0 !important;
    border-top-right-radius: 6px !important;
    border-top-left-radius: 6px !important;
  }
  .p-column-filter-overlay-menu .p-column-filter-constraint {
    padding: 0 1.125rem 1.125rem 1.125rem !important;
    border-bottom: 1px solid #e2e8f0 !important;
  }
  .p-column-filter-overlay-menu .p-column-filter-constraint .p-column-filter-matchmode-dropdown {
    margin-bottom: 0.5rem !important;
  }
  .p-column-filter-overlay-menu .p-column-filter-constraint .p-column-filter-remove-button {
    margin-top: 0.5rem !important;
  }
  .p-column-filter-overlay-menu .p-column-filter-constraint:last-child {
    border-bottom: 0 none !important;
  }
  .p-column-filter-overlay-menu .p-column-filter-add-rule {
    padding: 0 1.125rem 1.125rem 1.125rem !important;
  }
  .p-column-filter-overlay-menu .p-column-filter-buttonbar {
    padding: 0 1.125rem 1.125rem 1.125rem !important;
  }

  .p-orderlist .p-orderlist-controls {
    padding: 0 1.125rem 1.125rem 1.125rem !important;
  }
  .p-orderlist .p-orderlist-controls .p-button {
    margin-bottom: 0.5rem !important;
  }
  .p-orderlist .p-orderlist-list-container {
    background: #ffffff !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 6px !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    outline-color: transparent !important;
  }
  .p-orderlist .p-orderlist-list-container.p-focus {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: -1px !important;
    box-shadow: none !important;
    border-color: #94a3b8 !important;
  }
  .p-orderlist .p-orderlist-header {
    color: #334155 !important;
    padding: 1.125rem !important;
    font-weight: 600 !important;
  }
  .p-orderlist .p-orderlist-header .p-orderlist-title {
    font-weight: 600 !important;
  }
  .p-orderlist .p-orderlist-filter-container {
    padding: 1.125rem !important;
    background: #ffffff !important;
    border: 1px solid #e2e8f0 !important;
    border-bottom: 0 none !important;
  }
  .p-orderlist .p-orderlist-filter-container .p-orderlist-filter-input {
    padding-right: 1.75rem !important;
  }
  .p-orderlist .p-orderlist-filter-container .p-orderlist-filter-icon {
    right: 0.75rem !important;
    color: #94a3b8 !important;
  }
  .p-orderlist .p-orderlist-list {
    color: #334155 !important;
    padding: 0.25rem 0.25rem !important;
    outline: 0 none !important;
  }
  .p-orderlist .p-orderlist-list:not(:first-child) {
    border-top: 1px solid #e2e8f0 !important;
  }
  .p-orderlist .p-orderlist-list .p-orderlist-item {
    padding: 0.5rem 0.75rem !important;
    margin: 2px 0 !important;
    border: 0 none !important;
    color: #334155 !important;
    background: transparent !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-orderlist .p-orderlist-list .p-orderlist-item:first-child {
    margin-top: 0 !important;
  }
  .p-orderlist .p-orderlist-list .p-orderlist-item:not(.p-highlight):hover {
    background: #f1f5f9 !important;
    color: #1e293b !important;
  }
  .p-orderlist .p-orderlist-list .p-orderlist-item.p-focus {
    color: #0f172a !important;
    background: #e2e8f0 !important;
  }
  .p-orderlist .p-orderlist-list .p-orderlist-item.p-highlight {
    color: #1D4ED8 !important;
    background: #EFF6FF !important;
  }
  .p-orderlist .p-orderlist-list .p-orderlist-item.p-highlight.p-focus {
    background: rgba(59, 130, 246, 0.24) !important;
  }
  .p-orderlist .p-orderlist-list .p-orderlist-empty-message {
    padding: 0.5rem 0.75rem !important;
    color: #334155 !important;
  }
  .p-orderlist .p-orderlist-list:not(.cdk-drop-list-dragging) .p-orderlist-item:not(.p-highlight):hover {
    background: #f1f5f9 !important;
    color: #1e293b !important;
  }
  .p-orderlist.p-orderlist-striped .p-orderlist-list .p-orderlist-item:nth-child(even) {
    background: #f8fafc !important;
  }
  .p-orderlist.p-orderlist-striped .p-orderlist-list .p-orderlist-item:nth-child(even):hover {
    background: #f1f5f9 !important;
  }

  .p-orderlist-item.cdk-drag-preview {
    padding: 0.5rem 0.75rem !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1) !important;
    border: 0 none !important;
    color: #334155 !important;
    background: #ffffff !important;
    margin: 0 !important;
  }

  .p-organizationchart .p-organizationchart-node-content.p-organizationchart-selectable-node:not(.p-highlight):hover {
    background: #f1f5f9 !important;
    color: #1e293b !important;
  }
  .p-organizationchart .p-organizationchart-node-content.p-highlight {
    background: #EFF6FF !important;
    color: #1D4ED8 !important;
  }
  .p-organizationchart .p-organizationchart-node-content.p-highlight .p-node-toggler i {
    color: #70aeff !important;
  }
  .p-organizationchart .p-organizationchart-line-down {
    background: #e2e8f0 !important;
  }
  .p-organizationchart .p-organizationchart-line-left {
    border-right: 1px solid #e2e8f0 !important;
    border-color: #e2e8f0 !important;
  }
  .p-organizationchart .p-organizationchart-line-top {
    border-top: 1px solid #e2e8f0 !important;
    border-color: #e2e8f0 !important;
  }
  .p-organizationchart .p-organizationchart-node-content {
    border: 1px solid #e2e8f0 !important;
    background: #ffffff !important;
    color: #334155 !important;
    padding: 0 1.125rem 1.125rem 1.125rem !important;
  }
  .p-organizationchart .p-organizationchart-node-content .p-node-toggler {
    background: inherit !important;
    color: inherit !important;
    border-radius: 50% !important;
  }
  .p-organizationchart .p-organizationchart-node-content .p-node-toggler:focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }

  .p-paginator {
    background: #ffffff !important;
    color: #334155 !important;
    border: solid #e2e8f0 !important;
    border-width: 0 !important;
    padding: 0.5rem 1rem !important;
    border-radius: 6px !important;
  }
  .p-paginator .p-paginator-first,
.p-paginator .p-paginator-prev,
.p-paginator .p-paginator-next,
.p-paginator .p-paginator-last {
    background-color: transparent !important;
    border: 0 none !important;
    color: #64748b !important;
    min-width: 2.5rem !important;
    height: 2.5rem !important;
    margin: 0.143rem !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border-radius: 50% !important;
  }
  .p-paginator .p-paginator-first:not(.p-disabled):not(.p-highlight):hover,
.p-paginator .p-paginator-prev:not(.p-disabled):not(.p-highlight):hover,
.p-paginator .p-paginator-next:not(.p-disabled):not(.p-highlight):hover,
.p-paginator .p-paginator-last:not(.p-disabled):not(.p-highlight):hover {
    background: #f1f5f9 !important;
    border-color: transparent !important;
    color: #475569 !important;
  }
  .p-paginator .p-paginator-first {
    border-top-left-radius: 50% !important;
    border-bottom-left-radius: 50% !important;
  }
  .p-paginator .p-paginator-last {
    border-top-right-radius: 50% !important;
    border-bottom-right-radius: 50% !important;
  }
  .p-paginator .p-select {
    margin-left: 0.5rem !important;
    margin-right: 0.5rem !important;
    height: 2.5rem !important;
  }
  .p-paginator .p-select .p-select-label {
    padding-right: 0 !important;
  }
  .p-paginator .p-paginator-page-input {
    margin-left: 0.5rem !important;
    margin-right: 0.5rem !important;
  }
  .p-paginator .p-paginator-page-input .p-inputtext {
    max-width: 2.5rem !important;
  }
  .p-paginator .p-paginator-current {
    background-color: transparent !important;
    border: 0 none !important;
    color: #64748b !important;
    min-width: 2.5rem !important;
    height: 2.5rem !important;
    margin: 0.143rem !important;
    padding: 0 0.5rem !important;
  }
  .p-paginator .p-paginator-pages .p-paginator-page {
    background-color: transparent !important;
    border: 0 none !important;
    color: #64748b !important;
    min-width: 2.5rem !important;
    height: 2.5rem !important;
    margin: 0.143rem !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border-radius: 50% !important;
  }
  .p-paginator .p-paginator-pages .p-paginator-page.p-highlight {
    background: #EFF6FF !important;
    border-color: #EFF6FF !important;
    color: #1D4ED8 !important;
  }
  .p-paginator .p-paginator-pages .p-paginator-page:not(.p-highlight):hover {
    background: #f1f5f9 !important;
    border-color: transparent !important;
    color: #475569 !important;
  }

  .p-picklist .p-picklist-buttons {
    padding: 0 1.125rem 1.125rem 1.125rem !important;
  }
  .p-picklist .p-picklist-buttons .p-button {
    margin-bottom: 0.5rem !important;
  }
  .p-picklist .p-picklist-list-wrapper {
    background: #ffffff !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 6px !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    outline-color: transparent !important;
  }
  .p-picklist .p-picklist-list-wrapper.p-focus {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: -1px !important;
    box-shadow: none !important;
    border-color: #94a3b8 !important;
  }
  .p-picklist .p-picklist-header {
    color: #334155 !important;
    padding: 1.125rem !important;
    font-weight: 600 !important;
  }
  .p-picklist .p-picklist-header .p-picklist-title {
    font-weight: 600 !important;
  }
  .p-picklist .p-picklist-filter-container {
    padding: 1.125rem !important;
    background: #ffffff !important;
    border: 1px solid #e2e8f0 !important;
    border-bottom: 0 none !important;
  }
  .p-picklist .p-picklist-filter-container .p-picklist-filter-input {
    padding-right: 1.75rem !important;
  }
  .p-picklist .p-picklist-filter-container .p-picklist-filter-icon {
    right: 0.75rem !important;
    color: #94a3b8 !important;
  }
  .p-picklist .p-picklist-list {
    color: #334155 !important;
    padding: 0.25rem 0.25rem !important;
    outline: 0 none !important;
  }
  .p-picklist .p-picklist-list:not(:first-child) {
    border-top: 1px solid #e2e8f0 !important;
  }
  .p-picklist .p-picklist-list .p-picklist-item {
    padding: 0.5rem 0.75rem !important;
    margin: 2px 0 !important;
    border: 0 none !important;
    color: #334155 !important;
    background: transparent !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-picklist .p-picklist-list .p-picklist-item:first-child {
    margin-top: 0 !important;
  }
  .p-picklist .p-picklist-list .p-picklist-item:not(.p-highlight):hover {
    background: #f1f5f9 !important;
    color: #1e293b !important;
  }
  .p-picklist .p-picklist-list .p-picklist-item.p-focus {
    color: #0f172a !important;
    background: #e2e8f0 !important;
  }
  .p-picklist .p-picklist-list .p-picklist-item.p-highlight {
    color: #1D4ED8 !important;
    background: #EFF6FF !important;
  }
  .p-picklist .p-picklist-list .p-picklist-item.p-highlight.p-focus {
    background: rgba(59, 130, 246, 0.24) !important;
  }
  .p-picklist .p-picklist-list .p-picklist-empty-message {
    padding: 0.5rem 0.75rem !important;
    color: #334155 !important;
  }
  .p-picklist .p-picklist-list:not(.cdk-drop-list-dragging) .p-picklist-item:not(.p-highlight):hover {
    background: #f1f5f9 !important;
    color: #1e293b !important;
  }
  .p-picklist.p-picklist-striped .p-picklist-list .p-picklist-item:nth-child(even) {
    background: #f8fafc !important;
  }
  .p-picklist.p-picklist-striped .p-picklist-list .p-picklist-item:nth-child(even):hover {
    background: #f1f5f9 !important;
  }

  .p-picklist-item.cdk-drag-preview {
    padding: 0.5rem 0.75rem !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1) !important;
    border: 0 none !important;
    color: #334155 !important;
    background: #ffffff !important;
    margin: 0 !important;
  }

  .p-timeline .p-timeline-event-marker {
    border: 2px solid #e2e8f0 !important;
    border-radius: 50% !important;
    width: 1.125rem !important;
    height: 1.125rem !important;
    background-color: #ffffff !important;
  }
  .p-timeline .p-timeline-event-connector {
    background-color: #e2e8f0 !important;
  }
  .p-timeline.p-timeline-vertical .p-timeline-event-opposite,
.p-timeline.p-timeline-vertical .p-timeline-event-content {
    padding: 0 1rem !important;
  }
  .p-timeline.p-timeline-vertical .p-timeline-event-connector {
    width: 2px !important;
  }
  .p-timeline.p-timeline-horizontal .p-timeline-event-opposite,
.p-timeline.p-timeline-horizontal .p-timeline-event-content {
    padding: 1rem 0 !important;
  }
  .p-timeline.p-timeline-horizontal .p-timeline-event-connector {
    height: 2px !important;
  }

  .p-tree {
    border: 1px solid #e2e8f0 !important;
    background: #ffffff !important;
    color: #334155 !important;
    padding: 0 1.125rem 1.125rem 1.125rem !important;
    border-radius: 6px !important;
  }
  .p-tree .p-tree-container .p-treenode {
    padding: 0 0 !important;
    outline: 0 none !important;
  }
  .p-tree .p-tree-container .p-treenode:focus > .p-treenode-content {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: 0 none !important;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content {
    border-radius: 6px !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    padding: 0.25rem 0.5rem !important;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content .p-tree-toggler {
    margin-right: 0.5rem !important;
    width: 1.75rem !important;
    height: 1.75rem !important;
    color: #64748b !important;
    border: 0 none !important;
    background: transparent !important;
    border-radius: 50% !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content .p-tree-toggler:enabled:hover {
    color: #475569 !important;
    border-color: transparent !important;
    background: #f1f5f9 !important;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content .p-tree-toggler:focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content .p-treenode-icon {
    margin-right: 0.5rem !important;
    color: #475569 !important;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content .p-checkbox {
    margin-right: 0.5rem !important;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content .p-checkbox .p-indeterminate .p-checkbox-icon {
    color: #334155 !important;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content .p-checkbox.p-variant-filled .p-checkbox-box {
    background-color: #f8fafc !important;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content .p-checkbox.p-variant-filled .p-checkbox-box.p-highlight {
    background: #3B82F6 !important;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content .p-checkbox.p-variant-filled:not(.p-disabled) .p-checkbox-box:hover {
    background-color: #f8fafc !important;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content .p-checkbox.p-variant-filled:not(.p-disabled) .p-checkbox-box.p-highlight:hover {
    background: #2563eb !important;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content.p-highlight {
    background: #EFF6FF !important;
    color: #1D4ED8 !important;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content.p-highlight .p-tree-toggler,
.p-tree .p-tree-container .p-treenode .p-treenode-content.p-highlight .p-treenode-icon {
    color: #1D4ED8 !important;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content.p-highlight .p-tree-toggler:hover,
.p-tree .p-tree-container .p-treenode .p-treenode-content.p-highlight .p-treenode-icon:hover {
    color: #1D4ED8 !important;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content.p-treenode-selectable:not(.p-highlight):hover {
    background: #f1f5f9 !important;
    color: #1e293b !important;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content.p-treenode-dragover {
    background: #f1f5f9 !important;
    color: #1e293b !important;
  }
  .p-tree .p-tree-filter-container {
    margin-bottom: 0.5rem !important;
  }
  .p-tree .p-tree-filter-container .p-tree-filter {
    width: 100% !important;
    padding-right: 1.75rem !important;
  }
  .p-tree .p-tree-filter-container .p-tree-filter-icon {
    right: 0.75rem !important;
    color: #94a3b8 !important;
  }
  .p-tree .p-treenode-children {
    padding: 0 0 0 1rem !important;
  }
  .p-tree .p-tree-loading-icon {
    font-size: 2rem !important;
  }
  .p-tree .p-tree-loading-icon.p-icon {
    width: 2rem !important;
    height: 2rem !important;
  }
  .p-tree .p-treenode-droppoint.p-treenode-droppoint-active {
    background-color: #8cbeff !important;
  }
  .p-tree.p-tree-horizontal .p-treenode .p-treenode-content {
    border-radius: 6px !important;
    border: 1px solid #e2e8f0 !important;
    background-color: #ffffff !important;
    color: #334155 !important;
    padding: 0.25rem 0.5rem !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-tree.p-tree-horizontal .p-treenode .p-treenode-content.p-highlight {
    background-color: #EFF6FF !important;
    color: #1D4ED8 !important;
  }
  .p-tree.p-tree-horizontal .p-treenode .p-treenode-content.p-highlight .p-treenode-icon {
    color: #1D4ED8 !important;
  }
  .p-tree.p-tree-horizontal .p-treenode .p-treenode-content .p-tree-toggler {
    margin-right: 0.5rem !important;
  }
  .p-tree.p-tree-horizontal .p-treenode .p-treenode-content .p-treenode-icon {
    color: #475569 !important;
    margin-right: 0.5rem !important;
  }
  .p-tree.p-tree-horizontal .p-treenode .p-treenode-content .p-checkbox {
    margin-right: 0.5rem !important;
  }
  .p-tree.p-tree-horizontal .p-treenode .p-treenode-content .p-treenode-label:not(.p-highlight):hover {
    background-color: inherit !important;
    color: inherit !important;
  }
  .p-tree.p-tree-horizontal .p-treenode .p-treenode-content.p-treenode-selectable:not(.p-highlight):hover {
    background: #f1f5f9 !important;
    color: #1e293b !important;
  }
  .p-tree.p-tree-horizontal .p-treenode .p-treenode-content:focus {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }

  .p-treetable .p-paginator-top {
    border-width: 0 0 1px 0 !important;
    border-radius: 0 !important;
  }
  .p-treetable .p-paginator-bottom {
    border-width: 0 0 1px 0 !important;
    border-radius: 0 !important;
  }
  .p-treetable .p-treetable-header {
    background: #ffffff !important;
    color: #334155 !important;
    border: 1px solid #e2e8f0 !important;
    border-width: 0 0 1px 0 !important;
    padding: 0.75rem 1rem !important;
    font-weight: 600 !important;
  }
  .p-treetable .p-treetable-footer {
    background: #ffffff !important;
    color: #334155 !important;
    border: 1px solid #e2e8f0 !important;
    border-width: 0 0 1px 0 !important;
    padding: 0.75rem 1rem !important;
    font-weight: 600 !important;
  }
  .p-treetable .p-treetable-thead > tr > th {
    text-align: left !important;
    padding: 0.75rem 1rem !important;
    border: 1px solid #e2e8f0 !important;
    border-width: 0 0 1px 0 !important;
    font-weight: 600 !important;
    color: #334155 !important;
    background: #ffffff !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-treetable .p-treetable-tfoot > tr > td {
    text-align: left !important;
    padding: 0.75rem 1rem !important;
    border: 1px solid #e2e8f0 !important;
    border-width: 0 0 1px 0 !important;
    font-weight: 600 !important;
    color: #334155 !important;
    background: #ffffff !important;
  }
  .p-treetable .p-sortable-column {
    outline-color: var(--p-focus-ring-color) !important;
  }
  .p-treetable .p-sortable-column .p-sortable-column-icon {
    color: #64748b !important;
    margin-left: 0.5rem !important;
  }
  .p-treetable .p-sortable-column .p-sortable-column-badge {
    border-radius: 50% !important;
    height: 1rem !important;
    min-width: 1rem !important;
    line-height: 1rem !important;
    color: #1D4ED8 !important;
    background: #EFF6FF !important;
    margin-left: 0.5rem !important;
  }
  .p-treetable .p-sortable-column:not(.p-highlight):hover {
    background: #f1f5f9 !important;
    color: #1e293b !important;
  }
  .p-treetable .p-sortable-column:not(.p-highlight):hover .p-sortable-column-icon {
    color: #475569 !important;
  }
  .p-treetable .p-sortable-column.p-highlight {
    background: #EFF6FF !important;
    color: #1D4ED8 !important;
  }
  .p-treetable .p-sortable-column.p-highlight .p-sortable-column-icon {
    color: #1D4ED8 !important;
  }
  .p-treetable .p-treetable-tbody > tr {
    background: #ffffff !important;
    color: #334155 !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-treetable .p-treetable-tbody > tr > td {
    text-align: left !important;
    border: 1px solid #e2e8f0 !important;
    border-width: 0 0 1px 0 !important;
    padding: 0.75rem 1rem !important;
  }
  .p-treetable .p-treetable-tbody > tr > td .p-treetable-toggler {
    width: 1.75rem !important;
    height: 1.75rem !important;
    color: #64748b !important;
    border: 0 none !important;
    background: transparent !important;
    border-radius: 50% !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    margin-right: 0.5rem !important;
  }
  .p-treetable .p-treetable-tbody > tr > td .p-treetable-toggler:enabled:hover {
    color: #475569 !important;
    border-color: transparent !important;
    background: #f1f5f9 !important;
  }
  .p-treetable .p-treetable-tbody > tr > td .p-treetable-toggler:focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }
  .p-treetable .p-treetable-tbody > tr > td .p-treetable-toggler.p-icon {
    width: 2rem !important;
    height: 2rem !important;
  }
  .p-treetable .p-treetable-tbody > tr > td p-treetablecheckbox .p-checkbox {
    margin-right: 0.5rem !important;
  }
  .p-treetable .p-treetable-tbody > tr > td p-treetablecheckbox .p-checkbox .p-indeterminate .p-checkbox-icon {
    color: #334155 !important;
  }
  .p-treetable .p-treetable-tbody > tr > td p-treetablecheckbox .p-checkbox.p-variant-filled .p-checkbox-box {
    background-color: #f8fafc !important;
  }
  .p-treetable .p-treetable-tbody > tr > td p-treetablecheckbox .p-checkbox.p-variant-filled .p-checkbox-box.p-highlight {
    background: #3B82F6 !important;
  }
  .p-treetable .p-treetable-tbody > tr > td p-treetablecheckbox .p-checkbox.p-variant-filled:not(.p-disabled) .p-checkbox-box:hover {
    background-color: #f8fafc !important;
  }
  .p-treetable .p-treetable-tbody > tr > td p-treetablecheckbox .p-checkbox.p-variant-filled:not(.p-disabled) .p-checkbox-box.p-highlight:hover {
    background: #2563eb !important;
  }
  .p-treetable .p-treetable-tbody > tr:focus-visible {
    outline: 0.15rem solid var(--p-focus-ring-color) !important;
    outline-offset: -0.15rem !important;
  }
  .p-treetable .p-treetable-tbody > tr.p-highlight {
    background: #EFF6FF !important;
    color: #1D4ED8 !important;
  }
  .p-treetable .p-treetable-tbody > tr.p-highlight .p-treetable-toggler {
    color: #1D4ED8 !important;
  }
  .p-treetable .p-treetable-tbody > tr.p-highlight .p-treetable-toggler:hover {
    color: #1D4ED8 !important;
  }
  .p-treetable.p-treetable-hoverable-rows .p-treetable-tbody > tr:not(.p-highlight):hover {
    background: #f1f5f9 !important;
    color: #1e293b !important;
  }
  .p-treetable.p-treetable-hoverable-rows .p-treetable-tbody > tr:not(.p-highlight):hover .p-treetable-toggler {
    color: #1e293b !important;
  }
  .p-treetable .p-column-resizer-helper {
    background: #3B82F6 !important;
  }
  .p-treetable .p-treetable-scrollable-header,
.p-treetable .p-treetable-scrollable-footer {
    background: #ffffff !important;
  }
  .p-treetable .p-treetable-loading-icon {
    font-size: 2rem !important;
  }
  .p-treetable .p-treetable-loading-icon.p-icon {
    width: 2rem !important;
    height: 2rem !important;
  }
  .p-treetable.p-treetable-gridlines .p-datatable-header {
    border-width: 1px 1px 0 1px !important;
  }
  .p-treetable.p-treetable-gridlines .p-treetable-footer {
    border-width: 0 1px 1px 1px !important;
  }
  .p-treetable.p-treetable-gridlines .p-treetable-top {
    border-width: 0 1px 0 1px !important;
  }
  .p-treetable.p-treetable-gridlines .p-treetable-bottom {
    border-width: 0 1px 1px 1px !important;
  }
  .p-treetable.p-treetable-gridlines .p-treetable-thead > tr > th {
    border-width: 1px !important;
  }
  .p-treetable.p-treetable-gridlines .p-treetable-tbody > tr > td {
    border-width: 1px !important;
  }
  .p-treetable.p-treetable-gridlines .p-treetable-tfoot > tr > td {
    border-width: 1px !important;
  }
  .p-treetable.p-treetable-sm .p-treetable-header {
    padding: 0.65625rem 0.875rem !important;
  }
  .p-treetable.p-treetable-sm .p-treetable-thead > tr > th {
    padding: 0.375rem 0.5rem !important;
  }
  .p-treetable.p-treetable-sm .p-treetable-tbody > tr > td {
    padding: 0.375rem 0.5rem !important;
  }
  .p-treetable.p-treetable-sm .p-treetable-tfoot > tr > td {
    padding: 0.375rem 0.5rem !important;
  }
  .p-treetable.p-treetable-sm .p-treetable-footer {
    padding: 0.375rem 0.5rem !important;
  }
  .p-treetable.p-treetable-lg .p-treetable-header {
    padding: 0.9375rem 1.25rem !important;
  }
  .p-treetable.p-treetable-lg .p-treetable-thead > tr > th {
    padding: 0.9375rem 1.25rem !important;
  }
  .p-treetable.p-treetable-lg .p-treetable-tbody > tr > td {
    padding: 0.9375rem 1.25rem !important;
  }
  .p-treetable.p-treetable-lg .p-treetable-tfoot > tr > td {
    padding: 0.9375rem 1.25rem !important;
  }
  .p-treetable.p-treetable-lg .p-treetable-footer {
    padding: 0.9375rem 1.25rem !important;
  }

  .p-virtualscroller .p-virtualscroller-header {
    background: #ffffff !important;
    color: #334155 !important;
    border: 1px solid #e2e8f0 !important;
    border-width: 0 0 1px 0 !important;
    padding: 0.75rem 1rem !important;
    font-weight: 600 !important;
  }
  .p-virtualscroller .p-virtualscroller-content {
    background: #ffffff !important;
    color: #334155 !important;
    border: 0 none !important;
    padding: 0 !important;
  }
  .p-virtualscroller .p-virtualscroller-footer {
    background: #ffffff !important;
    color: #334155 !important;
    border: 1px solid #e2e8f0 !important;
    border-width: 0 0 1px 0 !important;
    padding: 0.75rem 1rem !important;
    font-weight: 600 !important;
    border-bottom-left-radius: 6px !important;
    border-bottom-right-radius: 6px !important;
  }

  .p-accordion .p-accordion-header .p-accordion-header-link {
    padding: 1.125rem 1.125rem 1.125rem 1.125rem !important;
    border: 0 none !important;
    color: #64748b !important;
    background: #ffffff !important;
    font-weight: 600 !important;
    border-radius: 6px !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-accordion .p-accordion-header .p-accordion-header-link .p-accordion-toggle-icon {
    margin-right: 0.5rem !important;
  }
  .p-accordion .p-accordion-header:not(.p-disabled) .p-accordion-header-link:focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: inset none !important;
  }
  .p-accordion .p-accordion-header:not(.p-highlight):not(.p-disabled):hover .p-accordion-header-link {
    background: #ffffff !important;
    border-color: #e2e8f0 !important;
    color: #334155 !important;
  }
  .p-accordion .p-accordion-header:not(.p-disabled).p-highlight .p-accordion-header-link {
    background: #ffffff !important;
    border-color: #e2e8f0 !important;
    color: #334155 !important;
    border-bottom-right-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
  }
  .p-accordion .p-accordion-header:not(.p-disabled).p-highlight:hover .p-accordion-header-link {
    border-color: #e2e8f0 !important;
    background: #ffffff !important;
    color: #334155 !important;
  }
  .p-accordion .p-accordion-content {
    padding: 0 1.125rem 1.125rem 1.125rem !important;
    border: 0 none !important;
    background: #ffffff !important;
    color: #334155 !important;
    border-top: 0 !important;
    border-top-right-radius: 0 !important;
    border-top-left-radius: 0 !important;
    border-bottom-right-radius: 6px !important;
    border-bottom-left-radius: 6px !important;
  }
  .p-accordion p-accordiontab .p-accordion-tab {
    margin-bottom: 0 !important;
  }
  .p-accordion p-accordiontab .p-accordion-header .p-accordion-header-link {
    border-radius: 0 !important;
  }
  .p-accordion p-accordiontab .p-accordion-content {
    border-radius: 0 !important;
  }
  .p-accordion p-accordiontab:not(:first-child) .p-accordion-header .p-accordion-header-link {
    border-top: 0 none !important;
  }
  .p-accordion p-accordiontab:not(:first-child) .p-accordion-header:not(.p-highlight):not(.p-disabled):hover .p-accordion-header-link, .p-accordion p-accordiontab:not(:first-child) .p-accordion-header:not(.p-disabled).p-highlight:hover .p-accordion-header-link {
    border-top: 0 none !important;
  }
  .p-accordion p-accordiontab:first-child .p-accordion-header .p-accordion-header-link {
    border-top-right-radius: 6px !important;
    border-top-left-radius: 6px !important;
  }
  .p-accordion p-accordiontab:last-child .p-accordion-header:not(.p-highlight) .p-accordion-header-link {
    border-bottom-right-radius: 6px !important;
    border-bottom-left-radius: 6px !important;
  }
  .p-accordion p-accordiontab:last-child .p-accordion-content {
    border-bottom-right-radius: 6px !important;
    border-bottom-left-radius: 6px !important;
  }

  .p-card {
    background: #ffffff !important;
    color: #334155 !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1) !important;
    border-radius: 6px !important;
  }
  .p-card .p-card-body {
    padding: 1.5rem !important;
  }
  .p-card .p-card-title {
    font-size: 1.25rem !important;
    font-weight: 600 !important;
    margin-bottom: 0.5rem !important;
  }
  .p-card .p-card-subtitle {
    font-weight: 400 !important;
    margin-bottom: 0.5rem !important;
    color: #64748b !important;
  }
  .p-card .p-card-content {
    padding: 0 0 0 0 !important;
  }
  .p-card .p-card-footer {
    padding: 0 0 0 0 !important;
  }

  .p-divider .p-divider-content {
    background-color: #ffffff !important;
  }
  .p-divider.p-divider-horizontal {
    margin: 0px !important;
    padding: 0 0.5rem !important;
  }
  .p-divider.p-divider-horizontal:before {
    border-top: 1px solid #e2e8f0 !important;
  }
  .p-divider.p-divider-horizontal .p-divider-content {
    padding: 0 0.5rem !important;
  }
  .p-divider.p-divider-vertical {
    margin: 0px !important;
    padding: 0.5rem 0 !important;
  }
  .p-divider.p-divider-vertical:before {
    border-left: 1px #e2e8f0 !important;
  }
  .p-divider.p-divider-vertical .p-divider-content {
    padding: 0.5rem 0 !important;
  }

  .p-fieldset {
    border: 1px solid #e2e8f0 !important;
    background: #ffffff !important;
    color: #334155 !important;
    border-radius: 6px !important;
  }
  .p-fieldset .p-fieldset-legend {
    padding: 1.125rem !important;
    border: 1px solid #e2e8f0 !important;
    color: #334155 !important;
    background: #ffffff !important;
    font-weight: 600 !important;
    border-radius: 6px !important;
  }
  .p-fieldset.p-fieldset-toggleable .p-fieldset-legend {
    padding: 0 !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-fieldset.p-fieldset-toggleable .p-fieldset-legend a {
    padding: 1.125rem !important;
    color: #334155 !important;
    border-radius: 6px !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-fieldset.p-fieldset-toggleable .p-fieldset-legend a .p-fieldset-toggler {
    margin-right: 0.5rem !important;
  }
  .p-fieldset.p-fieldset-toggleable .p-fieldset-legend a:focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }
  .p-fieldset.p-fieldset-toggleable .p-fieldset-legend:hover {
    background: #ffffff !important;
    border-color: #ffffff !important;
    color: #1e293b !important;
  }
  .p-fieldset .p-fieldset-content {
    padding: 0 1.125rem 1.125rem 1.125rem !important;
  }

  .p-panel .p-panel-header {
    border: 1px solid #e2e8f0 !important;
    padding: 0.25rem 0.5rem !important;
    margin-bottom:0.25rem;
    background: #ffffff !important;
    color: #334155 !important;
    border-top-right-radius: 6px !important;
    border-top-left-radius: 6px !important;
  }
  .p-panel .p-panel-header .p-panel-title {
    font-weight: 600 !important;
  }
  .p-panel .p-panel-header .p-panel-header-icon {
    width: 1.75rem !important;
    height: 1.75rem !important;
    color: #64748b !important;
    border: 0 none !important;
    background: transparent !important;
    border-radius: 50% !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-panel .p-panel-header .p-panel-header-icon:enabled:hover {
    color: #475569 !important;
    border-color: transparent !important;
    background: #f1f5f9 !important;
  }
  .p-panel .p-panel-header .p-panel-header-icon:focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }
  .p-panel.p-panel-toggleable .p-panel-header {
    padding: 0.75rem 1.125rem !important;
  }
  .p-panel .p-panel-content {
    padding: 0 0.5rem 0.5rem 0.5rem !important;
    border: 1px solid #e2e8f0 !important;
    background: #ffffff !important;
    color: #334155 !important;
    border-top: 0 none !important;
  }
  .p-panel .p-panel-content:last-child {
    border-bottom-right-radius: 6px !important;
    border-bottom-left-radius: 6px !important;
  }
  .p-panel .p-panel-footer {
    padding: 0 0.5rem 0.5rem 0.5rem !important;
    border: 1px solid #e2e8f0 !important;
    background: #ffffff !important;
    color: #334155 !important;
    border-bottom-right-radius: 6px !important;
    border-bottom-left-radius: 6px !important;
    border-top: 0 none !important;
  }
  .p-panel .p-panel-icons-end {
    order: 2 !important;
    margin-left: auto !important;
  }
  .p-panel .p-panel-icons-start {
    order: 0 !important;
    margin-right: 0.5rem !important;
  }
  .p-panel .p-panel-icons-center {
    order: 2 !important;
    width: 100% !important;
    text-align: center !important;
  }

  .p-scrollpanel .p-scrollpanel-bar {
    background: #f1f5f9 !important;
    border: 0 none !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-scrollpanel .p-scrollpanel-bar:focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }

  .p-splitter {
    border: 1px solid #e2e8f0 !important;
    background: #ffffff !important;
    border-radius: 6px !important;
    color: #334155 !important;
  }
  .p-splitter .p-splitter-gutter {
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    background: #e2e8f0 !important;
  }
  .p-splitter .p-splitter-gutter .p-splitter-gutter-handle {
    background: #e2e8f0 !important;
  }
  .p-splitter .p-splitter-gutter .p-splitter-gutter-handle:focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }
  .p-splitter .p-splitter-gutter-resizing {
    background: #e2e8f0 !important;
  }

  .p-tabview .p-tabview-nav-content {
    scroll-padding-inline: 2.5rem !important;
  }
  .p-tabview .p-tabview-nav {
    background: #ffffff !important;
    border: 1px solid #e2e8f0 !important;
    border-width: 0 0 1px 0 !important;
  }
  .p-tabview .p-tabview-nav li {
    margin-right: 0 !important;
  }
  .p-tabview .p-tabview-nav li .p-tabview-nav-link {
    border: solid #e2e8f0 !important;
    border-width: 0 0 1px 0 !important;
    border-color: transparent transparent #e2e8f0 transparent !important;
    background: #ffffff !important;
    color: #64748b !important;
    padding: 0.5rem 1rem !important;
    font-weight: 600 !important;
    border-top-right-radius: 6px !important;
    border-top-left-radius: 6px !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    margin: 0 0 -1px 0 !important;
  }
  .p-tabview .p-tabview-nav li .p-tabview-nav-link:not(.p-disabled):focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: inset none !important;
  }
  .p-tabview .p-tabview-nav li:not(.p-highlight):not(.p-disabled):hover .p-tabview-nav-link {
    background: #ffffff !important;
    border-color: #e2e8f0 !important;
    color: #334155 !important;
  }
  .p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link {
    background: #ffffff !important;
    border-color: #e2e8f0 !important;
    color: #3B82F6 !important;
  }
  .p-tabview .p-tabview-left-icon {
    margin-right: 0.5rem !important;
  }
  .p-tabview .p-tabview-right-icon {
    margin-left: 0.5rem !important;
  }
  .p-tabview .p-tabview-close {
    margin-left: 0.5rem !important;
  }
  .p-tabview .p-tabview-nav-btn.p-link {
    background: #ffffff !important;
    color: #3B82F6 !important;
    width: 2.5rem !important;
    box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12) !important;
    border-radius: 0 !important;
  }
  .p-tabview .p-tabview-nav-btn.p-link:focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: inset none !important;
  }
  .p-tabview .p-tabview-panels {
    background: #ffffff !important;
    padding: 0.875rem 1.125rem 1.125rem 1.125rem !important;
    border: 0 none !important;
    color: #334155 !important;
    border-bottom-right-radius: 6px !important;
    border-bottom-left-radius: 6px !important;
  }

  .p-toolbar {
    background: #ffffff !important;
    border: 1px solid #e2e8f0 !important;
    padding: 1.125rem !important;
    border-radius: 6px !important;
    gap: 0.5rem !important;
  }
  .p-toolbar .p-toolbar-separator {
    margin: 0 0.5rem !important;
  }

  .p-stepper .p-stepper-nav {
    position: relative !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin: 0 !important;
    padding: 0 !important;
    list-style-type: none !important;
    overflow-x: auto !important;
  }

  .p-stepper-vertical .p-stepper-nav {
    flex-direction: column !important;
  }

  .p-stepper-header {
    position: relative !important;
    display: flex !important;
    flex: 1 1 auto !important;
    align-items: center !important;
  }
  .p-stepper-header:last-of-type {
    flex: initial !important;
  }

  .p-stepper-header .p-stepper-action {
    border: 0 none !important;
    display: inline-flex !important;
    align-items: center !important;
    text-decoration: none !important;
    cursor: pointer !important;
  }
  .p-stepper-header .p-stepper-action:focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }

  .p-stepper.p-stepper-readonly .p-stepper-header {
    cursor: auto !important;
  }

  .p-stepper-header.p-highlight .p-stepper-action {
    cursor: default !important;
  }

  .p-stepper-title {
    display: block !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: 100% !important;
  }

  .p-stepper-number {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  .p-stepper-separator {
    flex: 1 1 0 !important;
  }

  .p-stepper .p-stepper-nav {
    display: flex !important;
    justify-content: space-between !important;
    margin: 0 !important;
    padding: 0 !important;
    list-style-type: none !important;
  }
  .p-stepper .p-stepper-header {
    padding: 0.5rem !important;
  }
  .p-stepper .p-stepper-header .p-stepper-action {
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border-radius: 6px !important;
    background: #ffffff !important;
    outline-color: transparent !important;
  }
  .p-stepper .p-stepper-header .p-stepper-action .p-stepper-number {
    color: #64748b !important;
    border: 1px solid #e2e8f0 !important;
    border-width: 2px !important;
    background: #ffffff !important;
    min-width: 2rem !important;
    height: 2rem !important;
    line-height: 2rem !important;
    font-size: 1.143rem !important;
    border-radius: 50% !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-stepper .p-stepper-header .p-stepper-action .p-stepper-title {
    margin-left: 0.5rem !important;
    color: #334155 !important;
    font-weight: 500 !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-stepper .p-stepper-header .p-stepper-action:not(.p-disabled):focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }
  .p-stepper .p-stepper-header.p-highlight .p-stepper-number {
    background: #EFF6FF !important;
    color: #1D4ED8 !important;
  }
  .p-stepper .p-stepper-header.p-highlight .p-stepper-title {
    color: #334155 !important;
  }
  .p-stepper .p-stepper-header:not(.p-disabled):focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }
  .p-stepper .p-stepper-header:has(~ .p-highlight) .p-stepper-separator {
    background-color: #3B82F6 !important;
  }
  .p-stepper .p-stepper-panels {
    background: #ffffff !important;
    padding: 0.875rem 1.125rem 1.125rem 1.125rem !important;
    color: #334155 !important;
  }
  .p-stepper .p-stepper-separator {
    background-color: #e2e8f0 !important;
    width: 100% !important;
    height: 2px !important;
    margin-inline-start: 1rem !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-stepper.p-stepper-vertical {
    display: flex !important;
    flex-direction: column !important;
  }
  .p-stepper.p-stepper-vertical .p-stepper-toggleable-content {
    display: flex !important;
    flex: 1 1 auto !important;
    background: #ffffff !important;
    color: #334155 !important;
  }
  .p-stepper.p-stepper-vertical .p-stepper-panel {
    display: flex !important;
    flex-direction: column !important;
    flex: initial !important;
  }
  .p-stepper.p-stepper-vertical .p-stepper-panel.p-stepper-panel-active {
    flex: 1 1 auto !important;
  }
  .p-stepper.p-stepper-vertical .p-stepper-panel .p-stepper-header {
    flex: initial !important;
  }
  .p-stepper.p-stepper-vertical .p-stepper-panel .p-stepper-content {
    width: 100% !important;
    padding-left: 1rem !important;
  }
  .p-stepper.p-stepper-vertical .p-stepper-panel .p-stepper-separator {
    flex: 0 0 auto !important;
    width: 2px !important;
    height: auto !important;
    margin-inline-start: calc(1.75rem + 2px) !important;
  }
  .p-stepper.p-stepper-vertical .p-stepper-panel:has(~ .p-stepper-panel-active) .p-stepper-separator {
    background-color: #3B82F6 !important;
  }
  .p-stepper.p-stepper-vertical .p-stepper-panel:last-of-type .p-stepper-content {
    padding-left: 3rem !important;
  }

  .p-confirm-popup {
    background: #ffffff !important;
    color: #334155 !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 6px !important;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1) !important;
  }
  .p-confirm-popup .p-confirm-popup-content {
    padding: 0 1.125rem 1.125rem 1.125rem !important;
  }
  .p-confirm-popup .p-confirm-popup-footer {
    text-align: right !important;
    padding: 0 1.125rem 1.125rem 1.125rem !important;
  }
  .p-confirm-popup .p-confirm-popup-footer button {
    margin: 0 0.5rem 0 0 !important;
    width: auto !important;
  }
  .p-confirm-popup .p-confirm-popup-footer button:last-child {
    margin: 0 !important;
  }
  .p-confirm-popup:after {
    border: solid transparent !important;
    border-color: rgba(255, 255, 255, 0) !important;
    border-bottom-color: #ffffff !important;
  }
  .p-confirm-popup:before {
    border: solid transparent !important;
    border-color: rgba(226, 232, 240, 0) !important;
    border-bottom-color: #e2e8f0 !important;
  }
  .p-confirm-popup.p-confirm-popup-flipped:after {
    border-top-color: #ffffff !important;
  }
  .p-confirm-popup.p-confirm-popup-flipped:before {
    border-top-color: #e2e8f0 !important;
  }
  .p-confirm-popup .p-confirm-popup-icon {
    font-size: 1.5rem !important;
  }
  .p-confirm-popup .p-confirm-popup-icon.p-icon {
    width: 1.5rem !important;
    height: 1.5rem !important;
  }
  .p-confirm-popup .p-confirm-popup-message {
    margin-left: 1rem !important;
  }

  .p-dialog {
    border-radius: 6px !important;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #e2e8f0 !important;
  }
  .p-dialog .p-dialog-header {
    background: #ffffff !important;
    color: #334155 !important;
    border-top-right-radius: 6px !important;
    border-top-left-radius: 6px !important;
  }
  .p-dialog .p-dialog-header .p-dialog-title {
    font-family: 'Source Sans Pro';
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 30px;
    color: #0F1925;
  }
  .p-dialog .p-dialog-header .p-dialog-header-icon {
    width: 1.75rem !important;
    height: 1.75rem !important;
    color: #64748b !important;
    border: 0 none !important;
    background: transparent !important;
    border-radius: 50% !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    margin-right: 0.5rem !important;
  }
  .p-dialog .p-dialog-header .p-dialog-header-icon:enabled:hover {
    color: #475569 !important;
    border-color: transparent !important;
    background: #f1f5f9 !important;
  }
  .p-dialog .p-dialog-header .p-dialog-header-icon:focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }
  .p-dialog .p-dialog-header .p-dialog-header-icon:last-child {
    margin-right: 0 !important;
  }
  .p-dialog .p-dialog-content {
    background: #ffffff !important;
    color: #334155 !important;
  }
  .p-dialog .p-dialog-content:last-of-type {
    border-bottom-right-radius: 6px !important;
    border-bottom-left-radius: 6px !important;
  }
  .p-dialog .p-dialog-footer {
    background: #ffffff !important;
    color: #334155 !important;
    text-align: right !important;
    border-bottom-right-radius: 6px !important;
    border-bottom-left-radius: 6px !important;
  }
  .p-dialog .p-dialog-footer button {
    width: auto !important;
  }
  .p-dialog.p-confirm-dialog .p-confirm-dialog-icon {
    font-size: 2rem !important;
  }
  .p-dialog.p-confirm-dialog .p-confirm-dialog-icon.p-icon {
    width: 2rem !important;
    height: 2rem !important;
  }
  .p-dialog.p-confirm-dialog .p-confirm-dialog-message {
    margin-left: 1rem !important;
  }

  .p-overlaypanel {
    background: #ffffff !important;
    color: #334155 !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 6px !important;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1) !important;
  }
  .p-overlaypanel .p-overlaypanel-content {
    padding: 0 1.125rem 1.125rem 1.125rem !important;
  }
  .p-overlaypanel .p-overlaypanel-close {
    background: #3B82F6 !important;
    color: #ffffff !important;
    width: 1.75rem !important;
    height: 1.75rem !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border-radius: 50% !important;
    position: absolute !important;
    top: -0.875rem !important;
    right: -0.875rem !important;
  }
  .p-overlaypanel .p-overlaypanel-close:enabled:hover {
    background: #2563eb !important;
    color: #ffffff !important;
  }
  .p-overlaypanel:after {
    border: solid transparent !important;
    border-color: rgba(255, 255, 255, 0) !important;
    border-bottom-color: #ffffff !important;
  }
  .p-overlaypanel:before {
    border: solid transparent !important;
    border-color: rgba(226, 232, 240, 0) !important;
    border-bottom-color: #d3dce8 !important;
  }
  .p-overlaypanel.p-overlaypanel-flipped:after {
    border-top-color: #ffffff !important;
  }
  .p-overlaypanel.p-overlaypanel-flipped:before {
    border-top-color: #e2e8f0 !important;
  }

  .p-sidebar {
    background: #ffffff !important;
    color: #334155 !important;
    border: 1px solid #e2e8f0 !important;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1) !important;
  }
  .p-sidebar .p-sidebar-header {
    padding: 1.125rem !important;
  }
  .p-sidebar .p-sidebar-header .p-sidebar-close,
.p-sidebar .p-sidebar-header .p-sidebar-icon {
    width: 1.75rem !important;
    height: 1.75rem !important;
    color: #64748b !important;
    border: 0 none !important;
    background: transparent !important;
    border-radius: 50% !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-sidebar .p-sidebar-header .p-sidebar-close:enabled:hover,
.p-sidebar .p-sidebar-header .p-sidebar-icon:enabled:hover {
    color: #475569 !important;
    border-color: transparent !important;
    background: #f1f5f9 !important;
  }
  .p-sidebar .p-sidebar-header .p-sidebar-close:focus-visible,
.p-sidebar .p-sidebar-header .p-sidebar-icon:focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }
  .p-sidebar .p-sidebar-header + .p-sidebar-content {
    padding-top: 0 !important;
  }
  .p-sidebar .p-sidebar-content {
    padding: 0 1.125rem 1.125rem 1.125rem !important;
  }
  .p-sidebar .p-sidebar-footer {
    padding: 1.125rem !important;
  }

  .p-tooltip .p-tooltip-text {
    background: #334155 !important;
    color: #ffffff !important;
    padding: 0.5rem 0.75rem !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1) !important;
    border-radius: 6px !important;
  }
  .p-tooltip.p-tooltip-right .p-tooltip-arrow {
    border-right-color: #334155 !important;
  }
  .p-tooltip.p-tooltip-left .p-tooltip-arrow {
    border-left-color: #334155 !important;
  }
  .p-tooltip.p-tooltip-top .p-tooltip-arrow {
    border-top-color: #334155 !important;
  }
  .p-tooltip.p-tooltip-bottom .p-tooltip-arrow {
    border-bottom-color: #334155 !important;
  }

  .p-fileupload .p-fileupload-buttonbar {
    background: #ffffff !important;
    padding: 1.125rem !important;
    border: 1px solid #e2e8f0 !important;
    color: #334155 !important;
    border-bottom: 0 none !important;
    border-top-right-radius: 6px !important;
    border-top-left-radius: 6px !important;
  }
  .p-fileupload .p-fileupload-buttonbar .p-button {
    margin-right: 0.5rem !important;
  }
  .p-fileupload .p-fileupload-buttonbar .p-button.p-fileupload-choose.p-focus {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }
  .p-fileupload .p-fileupload-content {
    background: #ffffff !important;
    padding: 0 1.125rem 1.125rem 1.125rem !important;
    border: 1px solid #e2e8f0 !important;
    color: #334155 !important;
    border-bottom-right-radius: 6px !important;
    border-bottom-left-radius: 6px !important;
  }
  .p-fileupload .p-fileupload-content.p-fileupload-highlight {
    border-color: 1px dashed #3B82F6 !important;
    border-style: dashed !important;
    background-color: #EFF6FF !important;
  }
  .p-fileupload .p-progressbar {
    height: 3px !important;
  }
  .p-fileupload .p-fileupload-row > div {
    padding: 0.75rem 1rem !important;
  }
  .p-fileupload.p-fileupload-advanced .p-message {
    margin-top: 0 !important;
  }

  .p-fileupload-choose:not(.p-disabled):hover {
    background: #2563eb !important;
    color: #ffffff !important;
    border-color: #2563eb !important;
  }
  .p-fileupload-choose:not(.p-disabled):active {
    background: #1D4ED8 !important;
    color: #ffffff !important;
    border-color: #1D4ED8 !important;
  }

  .p-breadcrumb {
    background: #ffffff !important;
    border: 0 none !important;
    border-radius: 6px !important;
    padding: 1rem !important;
  }
  .p-breadcrumb .p-breadcrumb-list li .p-menuitem-link {
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border-radius: 6px !important;
  }
  .p-breadcrumb .p-breadcrumb-list li .p-menuitem-link:focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }
  .p-breadcrumb .p-breadcrumb-list li .p-menuitem-link .p-menuitem-text {
    color: #334155 !important;
  }
  .p-breadcrumb .p-breadcrumb-list li .p-menuitem-link .p-menuitem-icon {
    color: #94a3b8 !important;
  }
  .p-breadcrumb .p-breadcrumb-list li.p-menuitem-separator {
    margin: 0 0.5rem 0 0.5rem !important;
    color: #94a3b8 !important;
  }
  .p-breadcrumb .p-breadcrumb-list li:last-child .p-menuitem-text {
    color: #334155 !important;
  }
  .p-breadcrumb .p-breadcrumb-list li:last-child .p-menuitem-icon {
    color: #94a3b8 !important;
  }

  .p-contextmenu {
    padding: 0.25rem 0.25rem !important;
    background: #ffffff !important;
    color: #334155 !important;
    border: 1px solid #e2e8f0 !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1) !important;
    border-radius: 6px !important;
    width: 12.5rem !important;
  }
  .p-contextmenu .p-contextmenu-root-list {
    outline: 0 none !important;
  }
  .p-contextmenu .p-submenu-list {
    padding: 0.25rem 0.25rem !important;
    background: #ffffff !important;
    border: 1px solid #e2e8f0 !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1) !important;
    border-radius: 6px !important;
  }
  .p-contextmenu .p-menuitem > .p-menuitem-content {
    color: #334155 !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border-radius: 4px !important;
  }
  .p-contextmenu .p-menuitem > .p-menuitem-content .p-menuitem-link {
    color: #334155 !important;
    padding: 0.5rem 0.75rem !important;
    user-select: none !important;
  }
  .p-contextmenu .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #334155 !important;
  }
  .p-contextmenu .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-icon {
    color: #94a3b8 !important;
    margin-right: 0.5rem !important;
  }
  .p-contextmenu .p-menuitem > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #94a3b8 !important;
  }
  .p-contextmenu .p-menuitem.p-highlight > .p-menuitem-content {
    color: #1D4ED8 !important;
    background: #EFF6FF !important;
  }
  .p-contextmenu .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #1D4ED8 !important;
  }
  .p-contextmenu .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-menuitem-icon,
.p-contextmenu .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #1D4ED8 !important;
  }
  .p-contextmenu .p-menuitem.p-highlight.p-focus > .p-menuitem-content {
    background: rgba(59, 130, 246, 0.24) !important;
  }
  .p-contextmenu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content {
    color: #334155 !important;
    background: #e2e8f0 !important;
  }
  .p-contextmenu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #334155 !important;
  }
  .p-contextmenu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-menuitem-icon,
.p-contextmenu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #334155 !important;
  }
  .p-contextmenu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover {
    color: #334155 !important;
    background: #f1f5f9 !important;
  }
  .p-contextmenu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-text {
    color: #334155 !important;
  }
  .p-contextmenu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-icon,
.p-contextmenu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-submenu-icon {
    color: #334155 !important;
  }
  .p-contextmenu .p-menuitem-separator {
    border-top: 1px solid #e2e8f0 !important;
    margin: 2px 0 !important;
  }
  .p-contextmenu .p-submenu-icon {
    font-size: 0.875rem !important;
  }
  .p-contextmenu .p-submenu-icon.p-icon {
    width: 0.875rem !important;
    height: 0.875rem !important;
  }

  .p-dock .p-dock-list-container {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    padding: 0.5rem 0.5rem !important;
    border-radius: 0.5rem !important;
  }
  .p-dock .p-dock-list-container .p-dock-list {
    outline: 0 none !important;
  }
  .p-dock .p-dock-item {
    padding: 0.5rem !important;
    border-radius: 6px !important;
  }
  .p-dock .p-dock-item.p-focus {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: 0 none !important;
  }
  .p-dock .p-dock-link {
    width: 3rem !important;
    height: 3rem !important;
  }
  .p-dock.p-dock-top .p-dock-item-second-prev,
.p-dock.p-dock-top .p-dock-item-second-next, .p-dock.p-dock-bottom .p-dock-item-second-prev,
.p-dock.p-dock-bottom .p-dock-item-second-next {
    margin: 0 0.9rem !important;
  }
  .p-dock.p-dock-top .p-dock-item-prev,
.p-dock.p-dock-top .p-dock-item-next, .p-dock.p-dock-bottom .p-dock-item-prev,
.p-dock.p-dock-bottom .p-dock-item-next {
    margin: 0 1.3rem !important;
  }
  .p-dock.p-dock-top .p-dock-item-current, .p-dock.p-dock-bottom .p-dock-item-current {
    margin: 0 1.5rem !important;
  }
  .p-dock.p-dock-left .p-dock-item-second-prev,
.p-dock.p-dock-left .p-dock-item-second-next, .p-dock.p-dock-right .p-dock-item-second-prev,
.p-dock.p-dock-right .p-dock-item-second-next {
    margin: 0.9rem 0 !important;
  }
  .p-dock.p-dock-left .p-dock-item-prev,
.p-dock.p-dock-left .p-dock-item-next, .p-dock.p-dock-right .p-dock-item-prev,
.p-dock.p-dock-right .p-dock-item-next {
    margin: 1.3rem 0 !important;
  }
  .p-dock.p-dock-left .p-dock-item-current, .p-dock.p-dock-right .p-dock-item-current {
    margin: 1.5rem 0 !important;
  }

  @media screen and (max-width: 960px) {
    .p-dock.p-dock-top .p-dock-list-container, .p-dock.p-dock-bottom .p-dock-list-container {
      overflow-x: auto !important;
      width: 100% !important;
    }
    .p-dock.p-dock-top .p-dock-list-container .p-dock-list, .p-dock.p-dock-bottom .p-dock-list-container .p-dock-list {
      margin: 0 auto !important;
    }
    .p-dock.p-dock-left .p-dock-list-container, .p-dock.p-dock-right .p-dock-list-container {
      overflow-y: auto !important;
      height: 100% !important;
    }
    .p-dock.p-dock-left .p-dock-list-container .p-dock-list, .p-dock.p-dock-right .p-dock-list-container .p-dock-list {
      margin: auto 0 !important;
    }
    .p-dock .p-dock-list .p-dock-item {
      transform: none !important;
      margin: 0 !important;
    }
  }
  .p-megamenu {
    padding: 0.5rem 0.5rem !important;
    background: #ffffff !important;
    color: #334155 !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 6px !important;
  }
  .p-megamenu .p-megamenu-root-list {
    outline: 0 none !important;
  }
  .p-megamenu .p-menuitem > .p-menuitem-content {
    color: #334155 !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border-radius: 4px !important;
  }
  .p-megamenu .p-menuitem > .p-menuitem-content .p-menuitem-link {
    color: #334155 !important;
    padding: 0.5rem 0.75rem !important;
    user-select: none !important;
  }
  .p-megamenu .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #334155 !important;
  }
  .p-megamenu .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-icon {
    color: #94a3b8 !important;
    margin-right: 0.5rem !important;
  }
  .p-megamenu .p-menuitem > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #94a3b8 !important;
  }
  .p-megamenu .p-menuitem.p-highlight > .p-menuitem-content {
    color: #1D4ED8 !important;
    background: #EFF6FF !important;
  }
  .p-megamenu .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #1D4ED8 !important;
  }
  .p-megamenu .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-menuitem-icon,
.p-megamenu .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #1D4ED8 !important;
  }
  .p-megamenu .p-menuitem.p-highlight.p-focus > .p-menuitem-content {
    background: rgba(59, 130, 246, 0.24) !important;
  }
  .p-megamenu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content {
    color: #334155 !important;
    background: #e2e8f0 !important;
  }
  .p-megamenu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #334155 !important;
  }
  .p-megamenu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-menuitem-icon,
.p-megamenu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #334155 !important;
  }
  .p-megamenu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover {
    color: #334155 !important;
    background: #f1f5f9 !important;
  }
  .p-megamenu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-text {
    color: #334155 !important;
  }
  .p-megamenu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-icon,
.p-megamenu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-submenu-icon {
    color: #334155 !important;
  }
  .p-megamenu .p-megamenu-panel {
    background: #ffffff !important;
    color: #334155 !important;
    border: 1px solid #e2e8f0 !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1) !important;
    border-radius: 6px !important;
  }
  .p-megamenu .p-submenu-header {
    margin: 0 !important;
    padding: 0.5rem 0.75rem !important;
    color: #94a3b8 !important;
    background: #ffffff !important;
    font-weight: 600 !important;
    border-top-right-radius: 6px !important;
    border-top-left-radius: 6px !important;
  }
  .p-megamenu .p-submenu-list {
    padding: 0.25rem 0.25rem !important;
    width: 12.5rem !important;
  }
  .p-megamenu .p-submenu-list .p-menuitem-separator {
    border-top: 1px solid #e2e8f0 !important;
    margin: 2px 0 !important;
  }
  .p-megamenu.p-megamenu-vertical {
    width: 12.5rem !important;
    padding: 0.25rem 0.25rem !important;
  }
  .p-megamenu.p-megamenu-horizontal .p-megamenu-root-list > .p-menuitem > .p-menuitem-content {
    color: #334155 !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border-radius: 6px !important;
  }
  .p-megamenu.p-megamenu-horizontal .p-megamenu-root-list > .p-menuitem > .p-menuitem-content .p-menuitem-link {
    padding: 0.5rem 0.75rem !important;
    user-select: none !important;
  }
  .p-megamenu.p-megamenu-horizontal .p-megamenu-root-list > .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #334155 !important;
  }
  .p-megamenu.p-megamenu-horizontal .p-megamenu-root-list > .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-icon {
    color: #94a3b8 !important;
    margin-right: 0.5rem !important;
  }
  .p-megamenu.p-megamenu-horizontal .p-megamenu-root-list > .p-menuitem > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #94a3b8 !important;
    margin-left: 0.5rem !important;
  }
  .p-megamenu.p-megamenu-horizontal .p-megamenu-root-list > .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover {
    color: #334155 !important;
    background: #f1f5f9 !important;
  }
  .p-megamenu.p-megamenu-horizontal .p-megamenu-root-list > .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-text {
    color: #334155 !important;
  }
  .p-megamenu.p-megamenu-horizontal .p-megamenu-root-list > .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-icon,
.p-megamenu.p-megamenu-horizontal .p-megamenu-root-list > .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-submenu-icon {
    color: #334155 !important;
  }

  .p-menu {
    padding: 0.25rem 0.25rem !important;
    background: #ffffff !important;
    color: #334155 !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 6px !important;
    width: 12.5rem !important;
  }
  .p-menu .p-menuitem > .p-menuitem-content {
    color: #334155 !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border-radius: 4px !important;
  }
  .p-menu .p-menuitem > .p-menuitem-content .p-menuitem-link {
    color: #334155 !important;
    padding: 0.5rem 0.75rem !important;
    user-select: none !important;
  }
  .p-menu .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #334155 !important;
  }
  .p-menu .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-icon {
    color: #94a3b8 !important;
    margin-right: 0.5rem !important;
  }
  .p-menu .p-menuitem > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #94a3b8 !important;
  }
  .p-menu .p-menuitem.p-highlight > .p-menuitem-content {
    color: #1D4ED8 !important;
    background: #EFF6FF !important;
  }
  .p-menu .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #1D4ED8 !important;
  }
  .p-menu .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-menuitem-icon,
.p-menu .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #1D4ED8 !important;
  }
  .p-menu .p-menuitem.p-highlight.p-focus > .p-menuitem-content {
    background: rgba(59, 130, 246, 0.24) !important;
  }
  .p-menu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content {
    color: #334155 !important;
    background: #e2e8f0 !important;
  }
  .p-menu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #334155 !important;
  }
  .p-menu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-menuitem-icon,
.p-menu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #334155 !important;
  }
  .p-menu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover {
    color: #334155 !important;
    background: #f1f5f9 !important;
  }
  .p-menu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-text {
    color: #334155 !important;
  }
  .p-menu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-icon,
.p-menu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-submenu-icon {
    color: #334155 !important;
  }
  .p-menu.p-menu-overlay {
    background: #ffffff !important;
    border: 1px solid #e2e8f0 !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1) !important;
  }
  .p-menu .p-submenu-header {
    margin: 0 !important;
    padding: 0.5rem 0.75rem !important;
    color: #94a3b8 !important;
    background: #ffffff !important;
    font-weight: 600 !important;
    border-top-right-radius: 0 !important;
    border-top-left-radius: 0 !important;
  }
  .p-menu .p-menuitem-separator {
    border-top: 1px solid #e2e8f0 !important;
    margin: 2px 0 !important;
  }
  .p-menu .p-menuitem-badge {
    background: #3B82F6 !important;
    color: #ffffff !important;
    font-size: 0.75rem !important;
    font-weight: 700 !important;
    min-width: 1.5rem !important;
    height: 1.5rem !important;
    line-height: 1.5rem !important;
    border-radius: 6px !important;
    margin-left: 0.5rem !important;
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }

  .p-menubar {
    padding: 0.5rem 0.5rem !important;
    background: #ffffff !important;
    color: #334155 !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 6px !important;
  }
  .p-menubar .p-menubar-root-list {
    outline: 0 none !important;
  }
  .p-menubar .p-menubar-root-list > .p-menuitem > .p-menuitem-content {
    color: #334155 !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border-radius: 6px !important;
  }
  .p-menubar .p-menubar-root-list > .p-menuitem > .p-menuitem-content .p-menuitem-link {
    padding: 0.5rem 0.75rem !important;
    user-select: none !important;
  }
  .p-menubar .p-menubar-root-list > .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #334155 !important;
  }
  .p-menubar .p-menubar-root-list > .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-icon {
    color: #94a3b8 !important;
    margin-right: 0.5rem !important;
  }
  .p-menubar .p-menubar-root-list > .p-menuitem > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #94a3b8 !important;
    margin-left: 0.5rem !important;
  }
  .p-menubar .p-menubar-root-list > .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover {
    color: #334155 !important;
    background: #f1f5f9 !important;
  }
  .p-menubar .p-menubar-root-list > .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-text {
    color: #334155 !important;
  }
  .p-menubar .p-menubar-root-list > .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-icon,
.p-menubar .p-menubar-root-list > .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-submenu-icon {
    color: #334155 !important;
  }
  .p-menubar .p-menuitem > .p-menuitem-content {
    color: #334155 !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border-radius: 4px !important;
  }
  .p-menubar .p-menuitem > .p-menuitem-content .p-menuitem-link {
    color: #334155 !important;
    padding: 0.5rem 0.75rem !important;
    user-select: none !important;
  }
  .p-menubar .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #334155 !important;
  }
  .p-menubar .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-icon {
    color: #94a3b8 !important;
    margin-right: 0.5rem !important;
  }
  .p-menubar .p-menuitem > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #94a3b8 !important;
  }
  .p-menubar .p-menuitem.p-highlight > .p-menuitem-content {
    color: #1D4ED8 !important;
    background: #EFF6FF !important;
  }
  .p-menubar .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #1D4ED8 !important;
  }
  .p-menubar .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-menuitem-icon,
.p-menubar .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #1D4ED8 !important;
  }
  .p-menubar .p-menuitem.p-highlight.p-focus > .p-menuitem-content {
    background: rgba(59, 130, 246, 0.24) !important;
  }
  .p-menubar .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content {
    color: #334155 !important;
    background: #e2e8f0 !important;
  }
  .p-menubar .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #334155 !important;
  }
  .p-menubar .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-menuitem-icon,
.p-menubar .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #334155 !important;
  }
  .p-menubar .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover {
    color: #334155 !important;
    background: #f1f5f9 !important;
  }
  .p-menubar .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-text {
    color: #334155 !important;
  }
  .p-menubar .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-icon,
.p-menubar .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-submenu-icon {
    color: #334155 !important;
  }
  .p-menubar .p-submenu-list {
    padding: 0.25rem 0.25rem !important;
    background: #ffffff !important;
    border: 1px solid #e2e8f0 !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1) !important;
    width: 12.5rem !important;
    border-radius: 6px !important;
  }
  .p-menubar .p-submenu-list .p-menuitem-separator {
    border-top: 1px solid #e2e8f0 !important;
    margin: 2px 0 !important;
  }
  .p-menubar .p-submenu-list .p-submenu-icon {
    font-size: 0.875rem !important;
  }

  @media screen and (max-width: 960px) {
    .p-menubar {
      position: relative !important;
    }
    .p-menubar .p-menubar-button {
      display: flex !important;
      width: 1.75rem !important;
      height: 1.75rem !important;
      color: #94a3b8 !important;
      border-radius: 50% !important;
      transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    }
    .p-menubar .p-menubar-button:hover {
      color: #334155 !important;
      background: #f1f5f9 !important;
    }
    .p-menubar .p-menubar-button:focus {
      outline: 1px solid var(--p-focus-ring-color) !important;
      outline-offset: 2px !important;
      box-shadow: none !important;
    }
    .p-menubar .p-menubar-root-list {
      position: absolute !important;
      display: none !important;
      padding: 0.25rem 0.25rem !important;
      background: #ffffff !important;
      border: 1px solid #e2e8f0 !important;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1) !important;
      width: 100% !important;
    }
    .p-menubar .p-menubar-root-list .p-menuitem-separator {
      border-top: 1px solid #e2e8f0 !important;
      margin: 2px 0 !important;
    }
    .p-menubar .p-menubar-root-list .p-submenu-icon {
      font-size: 0.875rem !important;
    }
    .p-menubar .p-menubar-root-list .p-menuitem {
      width: 100% !important;
      position: static !important;
    }
    .p-menubar .p-menubar-root-list .p-menuitem .p-menuitem-content .p-menuitem-link .p-submenu-icon {
      margin-left: auto !important;
      transition: transform 0.2s !important;
    }
    .p-menubar .p-menubar-root-list .p-menuitem.p-menuitem-active > .p-menuitem-content > .p-menuitem-link > .p-submenu-icon {
      transform: rotate(-180deg) !important;
    }
    .p-menubar .p-menubar-root-list .p-submenu-list {
      width: 100% !important;
      position: static !important;
      box-shadow: none !important;
      border: 0 none !important;
    }
    .p-menubar .p-menubar-root-list .p-submenu-list .p-submenu-icon {
      transition: transform 0.2s !important;
      transform: rotate(90deg) !important;
    }
    .p-menubar .p-menubar-root-list .p-submenu-list .p-menuitem-active > .p-menuitem-content > .p-menuitem-link > .p-submenu-icon {
      transform: rotate(-90deg) !important;
    }
    .p-menubar .p-menubar-root-list .p-menuitem {
      width: 100% !important;
      position: static !important;
    }
    .p-menubar .p-menubar-root-list .p-submenu-list .p-menuitem .p-menuitem-content .p-menuitem-link {
      padding-left: 1.5rem !important;
    }
    .p-menubar .p-menubar-root-list .p-submenu-list .p-menuitem .p-submenu-list .p-menuitem .p-menuitem-content .p-menuitem-link {
      padding-left: 2.5rem !important;
    }
    .p-menubar .p-menubar-root-list .p-submenu-list .p-menuitem .p-submenu-list .p-menuitem .p-submenu-list .p-menuitem .p-menuitem-content .p-menuitem-link {
      padding-left: 3.5rem !important;
    }
    .p-menubar .p-menubar-root-list .p-submenu-list .p-menuitem .p-submenu-list .p-menuitem .p-submenu-list .p-menuitem .p-submenu-list .p-menuitem .p-menuitem-content .p-menuitem-link {
      padding-left: 4.5rem !important;
    }
    .p-menubar .p-menubar-root-list .p-submenu-list .p-menuitem .p-submenu-list .p-menuitem .p-submenu-list .p-menuitem .p-submenu-list .p-menuitem .p-submenu-list .p-menuitem .p-menuitem-content .p-menuitem-link {
      padding-left: 5.5rem !important;
    }
    .p-menubar.p-menubar-mobile-active .p-menubar-root-list {
      display: flex !important;
      flex-direction: column !important;
      top: 100% !important;
      left: 0 !important;
      z-index: 1 !important;
    }
  }
  .p-panelmenu .p-panelmenu-header {
    outline: 0 none !important;
  }
  .p-panelmenu .p-panelmenu-header .p-panelmenu-header-content {
    border: 0 none !important;
    color: #64748b !important;
    background: #ffffff !important;
    border-radius: 6px !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-panelmenu .p-panelmenu-header .p-panelmenu-header-content .p-panelmenu-header-action {
    color: #64748b !important;
    padding: 1.125rem 1.125rem 1.125rem 1.125rem !important;
    font-weight: 600 !important;
  }
  .p-panelmenu .p-panelmenu-header .p-panelmenu-header-content .p-panelmenu-header-action .p-submenu-icon {
    margin-right: 0.5rem !important;
  }
  .p-panelmenu .p-panelmenu-header .p-panelmenu-header-content .p-panelmenu-header-action .p-menuitem-icon {
    margin-right: 0.5rem !important;
  }
  .p-panelmenu .p-panelmenu-header:not(.p-disabled):focus-visible .p-panelmenu-header-content {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: inset none !important;
  }
  .p-panelmenu .p-panelmenu-header:not(.p-highlight):not(.p-disabled):hover .p-panelmenu-header-content {
    background: #ffffff !important;
    border-color: #e2e8f0 !important;
    color: #334155 !important;
  }
  .p-panelmenu .p-panelmenu-header:not(.p-disabled).p-highlight .p-panelmenu-header-content {
    background: #ffffff !important;
    border-color: #e2e8f0 !important;
    color: #334155 !important;
    border-bottom-right-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
    margin-bottom: 0 !important;
  }
  .p-panelmenu .p-panelmenu-header:not(.p-disabled).p-highlight:hover .p-panelmenu-header-content {
    border-color: #e2e8f0 !important;
    background: #ffffff !important;
    color: #334155 !important;
  }
  .p-panelmenu .p-panelmenu-content {
    padding: 0.25rem 0.25rem !important;
    border: 0 none !important;
    background: #ffffff !important;
    color: #334155 !important;
    border-top: 0 !important;
    border-top-right-radius: 0 !important;
    border-top-left-radius: 0 !important;
    border-bottom-right-radius: 6px !important;
    border-bottom-left-radius: 6px !important;
  }
  .p-panelmenu .p-panelmenu-content .p-panelmenu-root-list {
    outline: 0 none !important;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem > .p-menuitem-content {
    color: #334155 !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border-radius: 4px !important;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem > .p-menuitem-content .p-menuitem-link {
    color: #334155 !important;
    padding: 0.5rem 0.75rem !important;
    user-select: none !important;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #334155 !important;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-icon {
    color: #94a3b8 !important;
    margin-right: 0.5rem !important;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #94a3b8 !important;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem.p-highlight > .p-menuitem-content {
    color: #1D4ED8 !important;
    background: #EFF6FF !important;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #1D4ED8 !important;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-menuitem-icon,
.p-panelmenu .p-panelmenu-content .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #1D4ED8 !important;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem.p-highlight.p-focus > .p-menuitem-content {
    background: rgba(59, 130, 246, 0.24) !important;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content {
    color: #334155 !important;
    background: #e2e8f0 !important;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #334155 !important;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-menuitem-icon,
.p-panelmenu .p-panelmenu-content .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #334155 !important;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover {
    color: #334155 !important;
    background: #f1f5f9 !important;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-text {
    color: #334155 !important;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-icon,
.p-panelmenu .p-panelmenu-content .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-submenu-icon {
    color: #334155 !important;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    margin-right: 0.5rem !important;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem-separator {
    border-top: 1px solid #e2e8f0 !important;
    margin: 2px 0 !important;
  }
  .p-panelmenu .p-panelmenu-content .p-submenu-list:not(.p-panelmenu-root-list) {
    padding: 0 0 0 1rem !important;
  }
  .p-panelmenu .p-panelmenu-panel {
    margin-bottom: 0 !important;
  }
  .p-panelmenu .p-panelmenu-panel .p-panelmenu-header .p-panelmenu-header-content {
    border-radius: 0 !important;
  }
  .p-panelmenu .p-panelmenu-panel .p-panelmenu-content {
    border-radius: 0 !important;
  }
  .p-panelmenu .p-panelmenu-panel:not(:first-child) .p-panelmenu-header .p-panelmenu-header-content {
    border-top: 0 none !important;
  }
  .p-panelmenu .p-panelmenu-panel:not(:first-child) .p-panelmenu-header:not(.p-highlight):not(.p-disabled):hover .p-panelmenu-header-content, .p-panelmenu .p-panelmenu-panel:not(:first-child) .p-panelmenu-header:not(.p-disabled).p-highlight:hover .p-panelmenu-header-content {
    border-top: 0 none !important;
  }
  .p-panelmenu .p-panelmenu-panel:first-child .p-panelmenu-header .p-panelmenu-header-content {
    border-top-right-radius: 6px !important;
    border-top-left-radius: 6px !important;
  }
  .p-panelmenu .p-panelmenu-panel:last-child .p-panelmenu-header:not(.p-highlight) .p-panelmenu-header-content {
    border-bottom-right-radius: 6px !important;
    border-bottom-left-radius: 6px !important;
  }
  .p-panelmenu .p-panelmenu-panel:last-child .p-panelmenu-content {
    border-bottom-right-radius: 6px !important;
    border-bottom-left-radius: 6px !important;
  }

  .p-slidemenu {
    padding: 0.25rem 0.25rem !important;
    background: #ffffff !important;
    color: #334155 !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 6px !important;
    width: 12.5rem !important;
  }
  .p-slidemenu .p-slidemenu-root-list {
    outline: 0 none !important;
  }
  .p-slidemenu .p-submenu-list {
    outline: 0 none !important;
  }
  .p-slidemenu .p-menuitem > .p-menuitem-content {
    color: #334155 !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border-radius: 4px !important;
  }
  .p-slidemenu .p-menuitem > .p-menuitem-content .p-menuitem-link {
    color: #334155 !important;
    padding: 0.5rem 0.75rem !important;
    user-select: none !important;
  }
  .p-slidemenu .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #334155 !important;
  }
  .p-slidemenu .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-icon {
    color: #94a3b8 !important;
    margin-right: 0.5rem !important;
  }
  .p-slidemenu .p-menuitem > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #94a3b8 !important;
  }
  .p-slidemenu .p-menuitem.p-highlight > .p-menuitem-content {
    color: #1D4ED8 !important;
    background: #EFF6FF !important;
  }
  .p-slidemenu .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #1D4ED8 !important;
  }
  .p-slidemenu .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-menuitem-icon,
.p-slidemenu .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #1D4ED8 !important;
  }
  .p-slidemenu .p-menuitem.p-highlight.p-focus > .p-menuitem-content {
    background: rgba(59, 130, 246, 0.24) !important;
  }
  .p-slidemenu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content {
    color: #334155 !important;
    background: #e2e8f0 !important;
  }
  .p-slidemenu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #334155 !important;
  }
  .p-slidemenu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-menuitem-icon,
.p-slidemenu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #334155 !important;
  }
  .p-slidemenu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover {
    color: #334155 !important;
    background: #f1f5f9 !important;
  }
  .p-slidemenu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-text {
    color: #334155 !important;
  }
  .p-slidemenu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-icon,
.p-slidemenu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-submenu-icon {
    color: #334155 !important;
  }
  .p-slidemenu.p-slidemenu-overlay {
    background: #ffffff !important;
    border: 1px solid #e2e8f0 !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1) !important;
  }
  .p-slidemenu .p-slidemenu-list {
    padding: 0.25rem 0.25rem !important;
    background: #ffffff !important;
    border: 1px solid #e2e8f0 !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1) !important;
  }
  .p-slidemenu .p-menuitem-separator {
    border-top: 1px solid #e2e8f0 !important;
    margin: 2px 0 !important;
  }
  .p-slidemenu .p-slidemenu-icon {
    font-size: 0.875rem !important;
  }
  .p-slidemenu .p-icon {
    width: 0.875rem !important;
    height: 0.875rem !important;
  }
  .p-slidemenu .p-slidemenu-backward {
    padding: 0.5rem 0.75rem !important;
    color: #334155 !important;
  }
  .p-slidemenu .p-slidemenu-backward:not(.p-disabled):focus {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: inset none !important;
  }
  .p-slidemenu .p-menuitem-badge {
    background: #3B82F6 !important;
    color: #ffffff !important;
    font-size: 0.75rem !important;
    font-weight: 700 !important;
    min-width: 1.5rem !important;
    height: 1.5rem !important;
    line-height: 1.5rem !important;
    border-radius: 6px !important;
    margin-left: 0.5rem !important;
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }

  .p-steps .p-steps-item .p-menuitem-link {
    background: transparent !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border-radius: 6px !important;
    background: #ffffff !important;
  }
  .p-steps .p-steps-item .p-menuitem-link .p-steps-number {
    color: #64748b !important;
    border: 1px solid #e2e8f0 !important;
    background: #ffffff !important;
    min-width: 2rem !important;
    height: 2rem !important;
    line-height: 2rem !important;
    font-size: 1.143rem !important;
    z-index: 1 !important;
    border-radius: 50% !important;
  }
  .p-steps .p-steps-item .p-menuitem-link .p-steps-title {
    margin-top: 0.5rem !important;
    color: #334155 !important;
  }
  .p-steps .p-steps-item .p-menuitem-link:not(.p-disabled):focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }
  .p-steps .p-steps-item.p-highlight .p-steps-number {
    background: #EFF6FF !important;
    color: #1D4ED8 !important;
  }
  .p-steps .p-steps-item.p-highlight .p-steps-title {
    font-weight: 500 !important;
    color: #334155 !important;
  }
  .p-steps .p-steps-item:before {
    content: " " !important;
    border-top: 1px solid #e2e8f0 !important;
    width: 100% !important;
    top: 50% !important;
    left: 0 !important;
    display: block !important;
    position: absolute !important;
    margin-top: -1rem !important;
  }

  .p-tabmenu .p-tabmenu-nav {
    background: #ffffff !important;
    border: 1px solid #e2e8f0 !important;
    border-width: 0 0 1px 0 !important;
  }
  .p-tabmenu .p-tabmenu-nav .p-menuitem-badge {
    background: #3B82F6 !important;
    color: #ffffff !important;
    font-size: 0.75rem !important;
    font-weight: 700 !important;
    min-width: 1.5rem !important;
    height: 1.5rem !important;
    line-height: 1.5rem !important;
    border-radius: 6px !important;
    margin-left: 0.5rem !important;
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }
  .p-tabmenu .p-tabmenu-nav .p-tabmenuitem {
    margin-right: 0 !important;
  }
  .p-tabmenu .p-tabmenu-nav .p-tabmenuitem .p-menuitem-link {
    border: solid #e2e8f0 !important;
    border-width: 0 0 1px 0 !important;
    border-color: transparent transparent #e2e8f0 transparent !important;
    background: #ffffff !important;
    color: #64748b !important;
    padding: 1rem 1.125rem !important;
    font-weight: 600 !important;
    border-top-right-radius: 6px !important;
    border-top-left-radius: 6px !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    margin: 0 0 -1px 0 !important;
  }
  .p-tabmenu .p-tabmenu-nav .p-tabmenuitem .p-menuitem-link .p-menuitem-icon {
    margin-right: 0.5rem !important;
  }
  .p-tabmenu .p-tabmenu-nav .p-tabmenuitem .p-menuitem-link:not(.p-disabled):focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: inset none !important;
  }
  .p-tabmenu .p-tabmenu-nav .p-tabmenuitem:not(.p-highlight):not(.p-disabled):hover .p-menuitem-link {
    background: #ffffff !important;
    border-color: #e2e8f0 !important;
    color: #334155 !important;
  }
  .p-tabmenu .p-tabmenu-nav .p-tabmenuitem.p-highlight .p-menuitem-link {
    background: #ffffff !important;
    border-color: #e2e8f0 !important;
    color: #3B82F6 !important;
  }
  .p-tabmenu .p-tabmenu-left-icon {
    margin-right: 0.5rem !important;
  }
  .p-tabmenu .p-tabmenu-right-icon {
    margin-left: 0.5rem !important;
  }
  .p-tabmenu .p-tabmenu-nav-btn.p-link {
    background: #ffffff !important;
    color: #3B82F6 !important;
    width: 2.5rem !important;
    box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12) !important;
    border-radius: 0 !important;
  }
  .p-tabmenu .p-tabmenu-nav-btn.p-link:focus {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: inset none !important;
  }

  .p-tieredmenu {
    padding: 0.25rem 0.25rem !important;
    background: #ffffff !important;
    color: #334155 !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 6px !important;
    width: 12.5rem !important;
  }
  .p-tieredmenu.p-tieredmenu-overlay {
    background: #ffffff !important;
    border: 1px solid #e2e8f0 !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1) !important;
  }
  .p-tieredmenu .p-tieredmenu-root-list {
    outline: 0 none !important;
  }
  .p-tieredmenu .p-submenu-list {
    padding: 0.25rem 0.25rem !important;
    background: #ffffff !important;
    border: 1px solid #e2e8f0 !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1) !important;
    border-radius: 6px !important;
  }
  .p-tieredmenu .p-menuitem > .p-menuitem-content {
    color: #334155 !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border-radius: 4px !important;
  }
  .p-tieredmenu .p-menuitem > .p-menuitem-content .p-menuitem-link {
    color: #334155 !important;
    padding: 0.5rem 0.75rem !important;
    user-select: none !important;
  }
  .p-tieredmenu .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #334155 !important;
  }
  .p-tieredmenu .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-icon {
    color: #94a3b8 !important;
    margin-right: 0.5rem !important;
  }
  .p-tieredmenu .p-menuitem > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #94a3b8 !important;
  }
  .p-tieredmenu .p-menuitem.p-highlight > .p-menuitem-content {
    color: #1D4ED8 !important;
    background: #EFF6FF !important;
  }
  .p-tieredmenu .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #1D4ED8 !important;
  }
  .p-tieredmenu .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-menuitem-icon,
.p-tieredmenu .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #1D4ED8 !important;
  }
  .p-tieredmenu .p-menuitem.p-highlight.p-focus > .p-menuitem-content {
    background: rgba(59, 130, 246, 0.24) !important;
  }
  .p-tieredmenu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content {
    color: #334155 !important;
    background: #e2e8f0 !important;
  }
  .p-tieredmenu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #334155 !important;
  }
  .p-tieredmenu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-menuitem-icon,
.p-tieredmenu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #334155 !important;
  }
  .p-tieredmenu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover {
    color: #334155 !important;
    background: #f1f5f9 !important;
  }
  .p-tieredmenu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-text {
    color: #334155 !important;
  }
  .p-tieredmenu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-icon,
.p-tieredmenu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-submenu-icon {
    color: #334155 !important;
  }
  .p-tieredmenu .p-menuitem-separator {
    border-top: 1px solid #e2e8f0 !important;
    margin: 2px 0 !important;
  }
  .p-tieredmenu .p-submenu-icon {
    font-size: 0.875rem !important;
  }
  .p-tieredmenu .p-submenu-icon.p-icon {
    width: 0.875rem !important;
    height: 0.875rem !important;
  }

  .p-inline-message {
    padding: 0.5rem 0.75rem !important;
    margin: 0 !important;
    border-radius: 6px !important;
  }
  .p-inline-message.p-inline-message-info {
    background: rgba(239, 246, 255, 0.95) !important;
    border: solid #bfdbfe !important;
    border-width: 1px !important;
    color: #2563eb !important;
  }
  .p-inline-message.p-inline-message-info .p-inline-message-icon {
    color: #2563eb !important;
  }
  .p-inline-message.p-inline-message-success {
    background: rgba(240, 253, 244, 0.95) !important;
    border: solid #bbf7d0 !important;
    border-width: 1px !important;
    color: #16a34a !important;
  }
  .p-inline-message.p-inline-message-success .p-inline-message-icon {
    color: #16a34a !important;
  }
  .p-inline-message.p-inline-message-warn {
    background: rgba(254, 252, 232, 0.95) !important;
    border: solid #fde68a !important;
    border-width: 1px !important;
    color: #ca8a04 !important;
  }
  .p-inline-message.p-inline-message-warn .p-inline-message-icon {
    color: #ca8a04 !important;
  }
  .p-inline-message.p-inline-message-error {
    background: rgba(254, 242, 242, 0.95) !important;
    border: solid #fecaca !important;
    border-width: 1px !important;
    color: #dc2626 !important;
  }
  .p-inline-message.p-inline-message-error .p-inline-message-icon {
    color: #dc2626 !important;
  }
  .p-inline-message .p-inline-message-icon {
    font-size: 1rem !important;
    margin-right: 0.5rem !important;
  }
  .p-inline-message .p-icon {
    width: 1rem !important;
    height: 1rem !important;
  }
  .p-inline-message .p-inline-message-text {
    font-size: 1rem !important;
  }
  .p-inline-message.p-inline-message-icon-only .p-inline-message-icon {
    margin-right: 0 !important;
  }

  .p-message {
    margin: 1rem 0 !important;
    border-radius: 6px !important;
  }
  .p-message .p-message-wrapper {
    padding: 0.5rem 0.75rem !important;
  }
  .p-message .p-message-close {
    width: 1.75rem !important;
    height: 1.75rem !important;
    border-radius: 50% !important;
    background: transparent !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-message .p-message-close:hover {
    background: rgba(255, 255, 255, 0.5) !important;
  }
  .p-message .p-message-close:focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }
  .p-message.p-message-info {
    background: rgba(239, 246, 255, 0.95) !important;
    border: solid #bfdbfe !important;
    border-width: 1px !important;
    color: #2563eb !important;
  }
  .p-message.p-message-info .p-message-icon {
    color: #2563eb !important;
  }
  .p-message.p-message-info .p-message-close {
    color: #2563eb !important;
  }
  .p-message.p-message-success {
    background: rgba(240, 253, 244, 0.95) !important;
    border: solid #bbf7d0 !important;
    border-width: 1px !important;
    color: #16a34a !important;
  }
  .p-message.p-message-success .p-message-icon {
    color: #16a34a !important;
  }
  .p-message.p-message-success .p-message-close {
    color: #16a34a !important;
  }
  .p-message.p-message-warn {
    background: rgba(254, 252, 232, 0.95) !important;
    border: solid #fde68a !important;
    border-width: 1px !important;
    color: #ca8a04 !important;
  }
  .p-message.p-message-warn .p-message-icon {
    color: #ca8a04 !important;
  }
  .p-message.p-message-warn .p-message-close {
    color: #ca8a04 !important;
  }
  .p-message.p-message-error {
    background: rgba(254, 242, 242, 0.95) !important;
    border: solid #fecaca !important;
    border-width: 1px !important;
    color: #dc2626 !important;
  }
  .p-message.p-message-error .p-message-icon {
    color: #dc2626 !important;
  }
  .p-message.p-message-error .p-message-close {
    color: #dc2626 !important;
  }
  .p-message .p-message-text {
    font-size: 1rem !important;
    font-weight: 500 !important;
  }
  .p-message .p-message-icon {
    font-size: 1rem !important;
    margin-right: 0.5rem !important;
  }
  .p-message .p-icon {
    width: 1rem !important;
    height: 1rem !important;
  }
  .p-message .p-message-summary {
    font-weight: 700 !important;
  }
  .p-message .p-message-detail {
    margin-left: 0.5rem !important;
  }
  .p-message.p-message-secondary {
    background: #f1f5f9 !important;
    border: solid #e2e8f0 !important;
    border-width: 1px !important;
    color: #475569 !important;
  }
  .p-message.p-message-secondary .p-message-icon {
    color: #475569 !important;
  }
  .p-message.p-message-secondary .p-message-close {
    color: #475569 !important;
  }
  .p-message.p-message-contrast {
    background: #020617 !important;
    border: solid #64748b !important;
    border-width: 1px !important;
    color: #f8fafc !important;
  }
  .p-message.p-message-contrast .p-message-icon {
    color: #f8fafc !important;
  }
  .p-message.p-message-contrast .p-message-close {
    color: #f8fafc !important;
  }

  .p-toast {
    opacity: 1 !important;
  }
  .p-toast .p-toast-message {
    margin: 0 0 1rem 0 !important;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !important;
    border-radius: 6px !important;
  }
  .p-toast .p-toast-message .p-toast-message-content {
    padding: 0.75rem !important;
    border-width: 1px !important;
  }
  .p-toast .p-toast-message .p-toast-message-content .p-toast-message-text {
    margin: 0 0 0 0.5rem !important;
  }
  .p-toast .p-toast-message .p-toast-message-content .p-toast-message-icon {
    font-size: 1.125rem !important;
    margin-top:5px !important;
  }
  .p-toast .p-toast-message .p-toast-message-content .p-icon:not(.p-toast-icon-close-icon) {
    width: 1.125rem !important;
    height: 1.125rem !important;
  }
  .p-toast .p-toast-message .p-toast-message-content .p-toast-summary {
    font-weight: 500 !important;
    font-size:1.25rem !important;
  }
  .p-toast .p-toast-message .p-toast-message-content .p-toast-detail {
    margin: 0.5rem 0 0 0 !important;
  }
  .p-toast .p-toast-message .p-toast-icon-close {
    width: 1.125rem !important;
    height: 1.125rem !important;
    border-radius: 50% !important;
    background: transparent !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-toast .p-toast-message .p-toast-icon-close:hover {
    background: rgba(255, 255, 255, 0.5) !important;
  }
  .p-toast .p-toast-message .p-toast-icon-close:focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }
  .p-toast .p-toast-message.p-toast-message-info {
    background: rgba(239, 246, 255, 0.95) !important;
    border: solid #bfdbfe !important;
    border-width: 1px !important;
    color: #2563eb !important;
  }
  .p-toast .p-toast-message.p-toast-message-info .p-toast-message-icon,
.p-toast .p-toast-message.p-toast-message-info .p-toast-icon-close {
    color: #2563eb !important;
  }
  .p-toast .p-toast-message.p-toast-message-success {
    background: rgba(240, 253, 244, 0.95) !important;
    border: solid #bbf7d0 !important;
    border-width: 1px !important;
    color: #16a34a !important;
  }
  .p-toast .p-toast-message.p-toast-message-success .p-toast-message-icon,
.p-toast .p-toast-message.p-toast-message-success .p-toast-icon-close {
    color: #16a34a !important;
  }
  .p-toast .p-toast-message.p-toast-message-warn {
    background: rgba(254, 252, 232, 0.95) !important;
    border: solid #fde68a !important;
    border-width: 1px !important;
    color: #ca8a04 !important;
  }
  .p-toast .p-toast-message.p-toast-message-warn .p-toast-message-icon,
.p-toast .p-toast-message.p-toast-message-warn .p-toast-icon-close {
    color: #ca8a04 !important;
  }
  .p-toast .p-toast-message.p-toast-message-error {
    background: rgba(254, 242, 242, 0.95) !important;
    border: solid #fecaca !important;
    border-width: 1px !important;
    color: #dc2626 !important;
  }
  .p-toast .p-toast-message.p-toast-message-error .p-toast-message-icon,
.p-toast .p-toast-message.p-toast-message-error .p-toast-icon-close {
    color: #dc2626 !important;
  }
  .p-toast .p-toast-message.p-toast-message-secondary {
    background: #f1f5f9 !important;
    border: solid #e2e8f0 !important;
    border-width: 1px !important;
    color: #475569 !important;
  }
  .p-toast .p-toast-message.p-toast-message-secondary .p-toast-message-icon,
.p-toast .p-toast-message.p-toast-message-secondary .p-toast-icon-close {
    color: #475569 !important;
  }
  .p-toast .p-toast-message.p-toast-message-contrast {
    background: #020617 !important;
    border: solid #64748b !important;
    border-width: 1px !important;
    color: #f8fafc !important;
  }
  .p-toast .p-toast-message.p-toast-message-contrast .p-toast-message-icon,
.p-toast .p-toast-message.p-toast-message-contrast .p-toast-icon-close {
    color: #f8fafc !important;
  }

  .p-galleria .p-galleria-close {
    margin: 0.5rem !important;
    background: transparent !important;
    color: #f1f5f9 !important;
    width: 4rem !important;
    height: 4rem !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border-radius: 50% !important;
  }
  .p-galleria .p-galleria-close .p-galleria-close-icon {
    font-size: 2rem !important;
  }
  .p-galleria .p-galleria-close .p-icon-wrapper .p-icon {
    width: 2rem !important;
    height: 2rem !important;
  }
  .p-galleria .p-galleria-close:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    color: #f1f5f9 !important;
  }
  .p-galleria .p-galleria-item-nav {
    background: transparent !important;
    color: #f1f5f9 !important;
    width: 4rem !important;
    height: 4rem !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border-radius: 6px !important;
    margin: 0 0.5rem !important;
  }
  .p-galleria .p-galleria-item-nav .p-galleria-item-prev-icon,
.p-galleria .p-galleria-item-nav .p-galleria-item-next-icon {
    font-size: 2rem !important;
  }
  .p-galleria .p-galleria-item-nav .p-icon-wrapper .p-icon {
    width: 2rem !important;
    height: 2rem !important;
  }
  .p-galleria .p-galleria-item-nav:not(.p-disabled):hover {
    background: rgba(255, 255, 255, 0.1) !important;
    color: #f1f5f9 !important;
  }
  .p-galleria .p-galleria-caption {
    background: rgba(0, 0, 0, 0.5) !important;
    color: #f1f5f9 !important;
    padding: 1rem !important;
  }
  .p-galleria .p-galleria-indicators {
    padding: 1rem !important;
  }
  .p-galleria .p-galleria-indicators .p-galleria-indicator button {
    background-color: #e2e8f0 !important;
    width: 1rem !important;
    height: 1rem !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border-radius: 50% !important;
  }
  .p-galleria .p-galleria-indicators .p-galleria-indicator button:hover {
    background: #cbd5e1 !important;
  }
  .p-galleria .p-galleria-indicators .p-galleria-indicator.p-highlight button {
    background: #EFF6FF !important;
    color: #1D4ED8 !important;
  }
  .p-galleria.p-galleria-indicators-bottom .p-galleria-indicator, .p-galleria.p-galleria-indicators-top .p-galleria-indicator {
    margin-right: 0.5rem !important;
  }
  .p-galleria.p-galleria-indicators-left .p-galleria-indicator, .p-galleria.p-galleria-indicators-right .p-galleria-indicator {
    margin-bottom: 0.5rem !important;
  }
  .p-galleria.p-galleria-indicator-onitem .p-galleria-indicators {
    background: rgba(0, 0, 0, 0.5) !important;
  }
  .p-galleria.p-galleria-indicator-onitem .p-galleria-indicators .p-galleria-indicator button {
    background: rgba(255, 255, 255, 0.4) !important;
  }
  .p-galleria.p-galleria-indicator-onitem .p-galleria-indicators .p-galleria-indicator button:hover {
    background: rgba(255, 255, 255, 0.6) !important;
  }
  .p-galleria.p-galleria-indicator-onitem .p-galleria-indicators .p-galleria-indicator.p-highlight button {
    background: #EFF6FF !important;
    color: #1D4ED8 !important;
  }
  .p-galleria .p-galleria-thumbnail-container {
    background: rgba(0, 0, 0, 0.9) !important;
    padding: 1rem 0.25rem !important;
  }
  .p-galleria .p-galleria-thumbnail-container .p-galleria-thumbnail-prev,
.p-galleria .p-galleria-thumbnail-container .p-galleria-thumbnail-next {
    margin: 0.5rem !important;
    background-color: transparent !important;
    color: #f1f5f9 !important;
    width: 2rem !important;
    height: 2rem !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border-radius: 50% !important;
  }
  .p-galleria .p-galleria-thumbnail-container .p-galleria-thumbnail-prev:hover,
.p-galleria .p-galleria-thumbnail-container .p-galleria-thumbnail-next:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    color: #f1f5f9 !important;
  }
  .p-galleria .p-galleria-thumbnail-container .p-galleria-thumbnail-item-content:focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }

  .p-galleria-mask {
    --maskbg: rgba(0, 0, 0, 0.9) !important;
  }

  .p-image-mask {
    --maskbg: rgba(0, 0, 0, 0.9) !important;
  }

  .p-image-preview-indicator {
    background-color: transparent !important;
    color: #f8fafc !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }

  .p-image-preview-container:hover > .p-image-preview-indicator {
    background-color: rgba(0, 0, 0, 0.5) !important;
  }

  .p-image-toolbar {
    padding: 1rem !important;
  }

  .p-image-action.p-link {
    color: #f8fafc !important;
    background-color: transparent !important;
    width: 3rem !important;
    height: 3rem !important;
    border-radius: 50% !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    margin-right: 0.5rem !important;
  }
  .p-image-action.p-link:last-child {
    margin-right: 0 !important;
  }
  .p-image-action.p-link:hover {
    color: #ffffff !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
  }
  .p-image-action.p-link i {
    font-size: 1.5rem !important;
  }
  .p-image-action.p-link .p-icon {
    width: 1.5rem !important;
    height: 1.5rem !important;
  }

  .p-avatar {
    background-color: #e2e8f0 !important;
    border-radius: 6px !important;
  }
  .p-avatar.p-avatar-lg {
    width: 3rem !important;
    height: 3rem !important;
    font-size: 1.5rem !important;
  }
  .p-avatar.p-avatar-lg .p-avatar-icon {
    font-size: 1.5rem !important;
  }
  .p-avatar.p-avatar-xl {
    width: 4rem !important;
    height: 4rem !important;
    font-size: 2rem !important;
  }
  .p-avatar.p-avatar-xl .p-avatar-icon {
    font-size: 2rem !important;
  }

  .p-avatar-group .p-avatar {
    border: 2px solid #ffffff !important;
  }

  .p-badge {
    background: #3B82F6 !important;
    color: #ffffff !important;
    font-size: 0.75rem !important;
    font-weight: 700 !important;
    min-width: 1.5rem !important;
    height: 1.5rem !important;
    line-height: 1.5rem !important;
  }
  .p-badge.p-badge-secondary {
    background-color: #f1f5f9 !important;
    color: #475569 !important;
  }
  .p-badge.p-badge-contrast {
    background-color: #020617 !important;
    color: #ffffff !important;
  }
  .p-badge.p-badge-success {
    background-color: #22c55e !important;
    color: #ffffff !important;
  }
  .p-badge.p-badge-info {
    background-color: #0ea5e9 !important;
    color: #ffffff !important;
  }
  .p-badge.p-badge-warning {
    background-color: #f97316 !important;
    color: #ffffff !important;
  }
  .p-badge.p-badge-danger {
    background-color: #ef4444 !important;
    color: #ffffff !important;
  }
  .p-badge.p-badge-lg {
    font-size: 1.125rem !important;
    min-width: 2.25rem !important;
    height: 2.25rem !important;
    line-height: 2.25rem !important;
  }
  .p-badge.p-badge-xl {
    font-size: 1.5rem !important;
    min-width: 3rem !important;
    height: 3rem !important;
    line-height: 3rem !important;
  }

  .p-chip {
    background-color: #f1f5f9 !important;
    color: #1e293b !important;
    border-radius: 16px !important;
    padding: 0 0.75rem !important;
  }
  .p-chip .p-chip-text {
    line-height: 1.5 !important;
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }
  .p-chip .p-chip-icon {
    margin-right: 0.5rem !important;
  }
  .p-chip .pi-chip-remove-icon {
    margin-left: 0.5rem !important;
  }
  .p-chip img {
    width: 2rem !important;
    height: 2rem !important;
    margin-left: -0.75rem !important;
    margin-right: 0.5rem !important;
  }
  .p-chip .pi-chip-remove-icon {
    border-radius: 6px !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-chip .pi-chip-remove-icon:focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }
  .p-chip .pi-chip-remove-icon:focus {
    outline: 0 none !important;
  }

  .p-inplace .p-inplace-display {
    padding: 0.5rem 0.75rem !important;
    border-radius: 6px !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-inplace .p-inplace-display:not(.p-disabled):hover {
    background: #f1f5f9 !important;
    color: #1e293b !important;
  }
  .p-inplace .p-inplace-display:focus {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }

  .p-metergroup {
    display: flex !important;
  }

  .p-metergroup-meters {
    display: flex !important;
  }

  .p-metergroup-vertical .p-metergroup-meters {
    flex-direction: column !important;
  }

  .p-metergroup-labels {
    display: flex !important;
    flex-wrap: wrap !important;
    margin: 0 !important;
    padding: 0 !important;
    list-style-type: none !important;
  }

  .p-metergroup-vertical .p-metergroup-labels {
    align-items: start !important;
  }

  .p-metergroup-labels-vertical {
    flex-direction: column !important;
  }

  .p-metergroup-label {
    display: inline-flex !important;
    align-items: center !important;
  }

  .p-metergroup-label-marker {
    display: inline-flex !important;
  }

  .p-metergroup {
    gap: 1rem !important;
  }
  .p-metergroup .p-metergroup-meters {
    background: #e2e8f0 !important;
    border-radius: 6px !important;
  }
  .p-metergroup .p-metergroup-meter {
    border: 0 none !important;
    background: #3B82F6 !important;
  }
  .p-metergroup .p-metergroup-labels .p-metergroup-label {
    gap: 0.5rem !important;
  }
  .p-metergroup .p-metergroup-labels .p-metergroup-label-marker {
    background: #3B82F6 !important;
    width: 0.5rem !important;
    height: 0.5rem !important;
    border-radius: 100% !important;
  }
  .p-metergroup .p-metergroup-labels .p-metergroup-label-icon {
    width: 1rem !important;
    height: 1rem !important;
  }
  .p-metergroup .p-metergroup-labels.p-metergroup-labels-vertical {
    gap: 0.5rem !important;
  }
  .p-metergroup .p-metergroup-labels.p-metergroup-labels-horizontal {
    gap: 1rem !important;
  }
  .p-metergroup.p-metergroup-horizontal {
    flex-direction: column !important;
  }
  .p-metergroup.p-metergroup-horizontal .p-metergroup-meters {
    height: 0.5rem !important;
  }
  .p-metergroup.p-metergroup-horizontal .p-metergroup-meter:first-of-type {
    border-top-left-radius: 6px !important;
    border-bottom-left-radius: 6px !important;
  }
  .p-metergroup.p-metergroup-horizontal .p-metergroup-meter:last-of-type {
    border-top-right-radius: 6px !important;
    border-bottom-right-radius: 6px !important;
  }
  .p-metergroup.p-metergroup-vertical {
    flex-direction: row !important;
  }
  .p-metergroup.p-metergroup-vertical .p-metergroup-meters {
    width: 0.5rem !important;
    height: 100% !important;
  }
  .p-metergroup.p-metergroup-vertical .p-metergroup-meter:first-of-type {
    border-top-left-radius: 6px !important;
    border-top-right-radius: 6px !important;
  }
  .p-metergroup.p-metergroup-vertical .p-metergroup-meter:last-of-type {
    border-bottom-left-radius: 6px !important;
    border-bottom-right-radius: 6px !important;
  }

  .p-progressbar {
    border: 0 none !important;
    height: 3px;
    background: #e2e8f0 !important;
    border-radius: 6px !important;
  }
  .p-progressbar .p-progressbar-value {
    border: 0 none !important;
    margin: 0 !important;
    background: #3B82F6 !important;
  }
  .p-progressbar .p-progressbar-label {
    color: #ffffff !important;
    line-height: 1.25rem !important;
  }

  .p-scrolltop {
    width: 3rem !important;
    height: 3rem !important;
    border-radius: 50% !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1) !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-scrolltop.p-link {
    background: rgba(0, 0, 0, 0.7) !important;
  }
  .p-scrolltop.p-link:hover {
    background: rgba(0, 0, 0, 0.8) !important;
  }
  .p-scrolltop .p-scrolltop-icon {
    font-size: 1.5rem !important;
    color: #f8fafc !important;
  }
  .p-scrolltop .p-icon {
    width: 1.5rem !important;
    height: 1.5rem !important;
  }

  .p-skeleton {
    background-color: #e2e8f0 !important;
    border-radius: 6px !important;
  }
  .p-skeleton:after {
    background: linear-gradient(90deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0)) !important;
  }

  .p-tag {
    background: #3B82F6 !important;
    color: #ffffff !important;
    font-size: 0.75rem !important;
    font-weight: 700 !important;
    padding: 0.25rem 0.4rem !important;
    border-radius: 6px !important;
  }
  .p-tag.p-tag-success {
    background-color: #22c55e !important;
    color: #ffffff !important;
  }
  .p-tag.p-tag-info {
    background-color: #0ea5e9 !important;
    color: #ffffff !important;
  }
  .p-tag.p-tag-warning {
    background-color: #f97316 !important;
    color: #ffffff !important;
  }
  .p-tag.p-tag-danger {
    background-color: #ef4444 !important;
    color: #ffffff !important;
  }
  .p-tag .p-tag-icon {
    margin-right: 0.25rem !important;
    font-size: 0.75rem !important;
  }
  .p-tag .p-icon {
    width: 0.75rem !important;
    height: 0.75rem !important;
  }
  .p-tag.p-tag-secondary {
    background-color: #f1f5f9 !important;
    color: #475569 !important;
  }
  .p-tag.p-tag-contrast {
    background-color: #020617 !important;
    color: #ffffff !important;
  }

  .p-terminal {
    background: #ffffff !important;
    color: #334155 !important;
    border: 1px solid #e2e8f0 !important;
    padding: 0 1.125rem 1.125rem 1.125rem !important;
  }
  .p-terminal .p-terminal-input {
    font-family: var(--font-family) !important;
    font-feature-settings: var(--font-feature-settings, normal) !important;
    font-size: 1rem !important;
  }
}
@layer primeng {
  .p-accordion .p-accordion-header .p-accordion-header-link {
    border-radius: 6px !important !important;
    flex-direction: row-reverse !important;
    justify-content: space-between !important;
  }
  .p-accordion .p-accordion-header .p-accordion-header-link:hover {
    border-bottom-color: #e2e8f0 !important;
  }
  .p-accordion .p-accordion-header .p-accordion-header-link .p-accordion-toggle-icon {
    transform: rotate(90deg) !important;
  }
  .p-accordion .p-accordion-header.p-highlight .p-accordion-toggle-icon {
    transform: rotate(-180deg) !important;
  }
  .p-accordion .p-accordion-header:not(.p-disabled) .p-accordion-header-link:focus-visible {
    outline-offset: -2px !important;
  }
  .p-accordion p-accordiontab .p-accordion-tab {
    border-bottom: 1px solid #e2e8f0 !important;
  }
  .p-accordion p-accordiontab:last-of-type .p-accordion-tab {
    border-bottom: 0 none !important;
  }

  .p-autocomplete .p-autocomplete-multiple-container {
    padding: 0.25rem 0.25rem !important;
    gap: 0.25rem !important;
  }
  .p-autocomplete .p-autocomplete-multiple-container .p-autocomplete-token {
    border-radius: 4px !important;
    margin: 0 !important;
  }
  .p-autocomplete .p-autocomplete-multiple-container .p-autocomplete-token .p-autocomplete-token-icon {
    margin-left: 0.375rem !important;
  }
  .p-autocomplete .p-autocomplete-multiple-container .p-autocomplete-input-token {
    margin-left: 0.5rem !important;
  }
  .p-autocomplete .p-autocomplete-multiple-container:has(.p-autocomplete-token) .p-autocomplete-input-token {
    margin-left: 0.5rem !important;
  }
  .p-autocomplete.p-disabled {
    opacity: 1 !important;
  }

  .p-button-label {
    font-weight: 500 !important;
  }

  .p-button.p-button-success:enabled:focus-visible {
    outline-color: #22c55e !important;
  }
  .p-button.p-button-info:enabled:focus-visible {
    outline-color: #0ea5e9 !important;
  }
  .p-button.p-button-warning:enabled:focus-visible {
    outline-color: #f97316 !important;
  }
  .p-button.p-button-help:enabled:focus-visible {
    outline-color: #a855f7 !important;
  }
  .p-button.p-button-danger:enabled:focus-visible {
    outline-color: #ef4444 !important;
  }
  .p-button.p-button-contrast:enabled:focus-visible {
    outline-color: #020617 !important;
  }
  .p-button.p-button-outlined {
    border-color: var(--primary-200) !important;
  }
  .p-button.p-button-outlined:not(:disabled):hover, .p-button.p-button-outlined:not(:disabled):active {
    border-color: var(--primary-200) !important;
  }
  .p-button.p-button-outlined.p-button-secondary {
    border-color: var(--surface-200) !important;
    color: #64748b !important;
  }
  .p-button.p-button-outlined.p-button-secondary:not(:disabled):hover {
    color: #64748b !important;
    background-color: #f1f5f9 !important;
    border-color: var(--surface-200) !important;
  }
  .p-button.p-button-outlined.p-button-secondary:not(:disabled):active {
    color: #64748b !important;
    background-color: #e2e8f0 !important;
    border-color: var(--surface-200) !important;
  }
  .p-button.p-button-outlined.p-button-success {
    border-color: #bbf7d0 !important;
  }
  .p-button.p-button-outlined.p-button-success:not(:disabled):hover, .p-button.p-button-outlined.p-button-success:not(:disabled):active {
    border-color: #bbf7d0 !important;
  }
  .p-button.p-button-outlined.p-button-info {
    border-color: #bae6fd !important;
  }
  .p-button.p-button-outlined.p-button-info:not(:disabled):hover, .p-button.p-button-outlined.p-button-info:not(:disabled):active {
    border-color: #bae6fd !important;
  }
  .p-button.p-button-outlined.p-button-warning {
    border-color: #fed7aa !important;
  }
  .p-button.p-button-outlined.p-button-warning:not(:disabled):hover, .p-button.p-button-outlined.p-button-warning:not(:disabled):active {
    border-color: #fed7aa !important;
  }
  .p-button.p-button-outlined.p-button-help {
    border-color: #e9d5ff !important;
  }
  .p-button.p-button-outlined.p-button-help:not(:disabled):hover, .p-button.p-button-outlined.p-button-help:not(:disabled):active {
    border-color: #e9d5ff !important;
  }
  .p-button.p-button-outlined.p-button-danger {
    border-color: #fecaca !important;
  }
  .p-button.p-button-outlined.p-button-danger:not(:disabled):hover, .p-button.p-button-outlined.p-button-danger:not(:disabled):active {
    border-color: #fecaca !important;
  }
  .p-button.p-button-outlined.p-button-contrast {
    border-color: #334155 !important;
  }
  .p-button.p-button-outlined.p-button-contrast:not(:disabled):hover, .p-button.p-button-outlined.p-button-contrast:not(:disabled):active {
    border-color: #334155 !important;
  }
  .p-button.p-button-secondary.p-button-text {
    color: #64748b !important;
  }
  .p-button.p-button-secondary.p-button-text:not(:disabled):hover {
    background: #f1f5f9 !important;
    color: #64748b !important;
  }
  .p-button.p-button-secondary.p-button-text:not(:disabled):active {
    background: #e2e8f0 !important;
    color: #64748b !important;
  }

  .p-datepicker-buttonbar .p-button {
    padding: 0.25rem 0.75rem !important;
    font-size: 0.875rem !important;
  }

  .p-datepicker .p-datepicker-group-container + .p-timepicker {
    margin-top: 0.5rem !important;
    padding-top: 0.5rem !important;
  }
  .p-datepicker table th {
    font-weight: 500 !important;
  }

  .p-card {
    border-radius: 12px !important;
    display: flex !important;
    flex-direction: column !important;
  }
  .p-card .p-card-caption {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.5rem !important;
  }
  .p-card .p-card-caption .p-card-title,
.p-card .p-card-caption .p-card-subtitle {
    margin-bottom: 0 !important;
  }
  .p-card .p-card-body {
    display: flex !important;
    flex-direction: column !important;
    gap: 1rem !important;
  }

  .p-carousel .p-carousel-indicators .p-carousel-indicator.p-highlight button {
    background-color: #3B82F6 !important;
  }

  .p-cascadeselect {
    box-shadow: 0 0 #0000, 0 0 #0000, 0 1px 2px 0 rgba(18, 18, 23, 0.05) !important;
  }
  .p-cascadeselect .p-cascadeselect-label {
    box-shadow: none !important;
  }
  .p-cascadeselect.p-disabled {
    opacity: 1 !important;
    background-color: #e2e8f0 !important;
  }
  .p-cascadeselect.p-disabled .p-cascadeselect-label {
    color: #64748b !important;
  }

  div.p-cascadeselect-panel {
    border: 0 none !important;
    box-shadow: none !important;
  }

  .p-checkbox .p-checkbox-box {
    border-radius: 4px !important;
    box-shadow: 0 0 #0000, 0 0 #0000, 0 1px 2px 0 rgba(18, 18, 23, 0.05) !important;
  }
  .p-checkbox .p-checkbox-box.p-focus {
    outline-offset: 2px !important;
  }
  .p-checkbox .p-checkbox-box.p-focus.p-highlight {
    border-color: #3B82F6 !important;
  }
  .p-checkbox.p-checkbox-disabled {
    opacity: 1 !important;
  }
  .p-checkbox.p-checkbox-disabled .p-checkbox-box {
    background-color: #e2e8f0 !important;
    border: 1px solid #cbd5e1 !important;
  }
  .p-checkbox.p-checkbox-disabled .p-checkbox-box .p-checkbox-icon {
    color: #64748b !important;
  }

  .p-chips .p-chips-multiple-container {
    padding: 0.25rem 0.25rem !important;
    gap: 0.25rem !important;
  }
  .p-chips .p-chips-multiple-container .p-chips-token {
    border-radius: 4px !important;
    margin: 0 !important;
  }
  .p-chips .p-chips-multiple-container .p-chips-token .p-chips-token-icon {
    margin-left: 0.375rem !important;
  }
  .p-chips .p-chips-multiple-container .p-chips-input-token {
    margin-left: 0.5rem !important;
  }
  .p-chips .p-chips-multiple-container:has(.p-chips-token) .p-chips-input-token {
    margin-left: 0.5rem !important;
  }
  .p-chips.p-disabled .p-chips-multiple-container {
    opacity: 1 !important;
    background-color: #e2e8f0 !important;
  }

  .p-chip {
    border-radius: 16px !important;
    padding: 0.25rem 0.75rem !important;
  }
  .p-chip .p-chip-text {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
  .p-chip .p-chip-remove-icon {
    margin-left: 0.375rem !important;
  }
  .p-chip:has(.p-chip-remove-icon) {
    padding-right: 0.5rem !important;
  }
  .p-chip img {
    margin-left: -0.5rem !important;
  }

  .p-colorpicker-preview {
    padding: 0 !important;
  }
  .p-colorpicker-preview:enabled:focus {
    outline-offset: 2px !important;
  }
  .p-colorpicker-preview.p-inputtext.p-disabled {
    opacity: 0.6 !important;
  }

  .p-confirm-popup {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1) !important;
  }

  .p-datatable .p-datatable-tbody > tr.p-datatable-dragpoint-top > td {
    box-shadow: inset 0 2px 0 0 #3B82F6 !important;
  }
  .p-datatable .p-datatable-tbody > tr.p-datatable-dragpoint-bottom > td {
    box-shadow: inset 0 -2px 0 0 #3B82F6 !important;
  }
  .p-datatable .p-datatable-tbody > tr:has(+ .p-highlight) > td {
    border-bottom-color: #bedaff !important;
  }
  .p-datatable .p-datatable-tbody > tr.p-highlight > td {
    border-bottom-color: #bedaff !important;
  }

  .p-dataview-layout-options.p-selectbutton .p-button svg {
    position: relative !important;
  }

  .p-dialog {
    border-radius: 12px !important;
    background-color: #ffffff !important;
  }
  .p-dialog.p-dialog-maximized {
    border-radius: 0 !important;
  }
  .p-dialog .p-dialog-header {
    border-top-right-radius: 12px !important;
    border-top-left-radius: 12px !important;
  }
  .p-dialog .p-dialog-content:last-of-type {
    border-bottom-right-radius: 12px !important;
    border-bottom-left-radius: 12px !important;
  }
  .p-dialog .p-dialog-footer {
    border-bottom-right-radius: 12px !important;
    border-bottom-left-radius: 12px !important;
  }

  .p-select {
    box-shadow: 0 0 #0000, 0 0 #0000, 0 1px 2px 0 rgba(18, 18, 23, 0.05) !important;
  }
  .p-select .p-select-label {
    box-shadow: none !important;
  }
  .p-select.p-disabled {
    opacity: 1 !important;
    background-color: #e2e8f0 !important;
  }
  .p-select.p-disabled .p-select-label {
    color: #64748b !important;
  }

  .p-select-panel .p-select-items .p-select-item .p-select-check-icon {
    margin-left: -0.375rem !important;
    margin-right: 0.375rem !important;
  }

  .p-treetable .p-treetable-tbody > tr:has(+ .p-highlight) > td {
    border-bottom-color: #bedaff !important;
  }
  .p-treetable .p-treetable-tbody > tr.p-highlight > td {
    border-bottom-color: #bedaff !important;
  }
  .p-treetable .p-treetable-tbody > tr.p-highlight > td .p-treetable-toggler:hover {
    background-color: #EFF6FF !important;
    color: #1D4ED8 !important;
  }

  .p-fieldset {
    padding: 0 1.125rem 1.125rem 1.125rem !important;
    margin: 0 !important;
  }
  .p-fieldset.p-fieldset-toggleable .p-fieldset-legend a {
    padding: 0.5rem 0.75rem !important;
  }
  .p-fieldset.p-fieldset-toggleable .p-fieldset-legend a .p-fieldset-legend-text {
    padding: 0 !important;
  }
  .p-fieldset.p-fieldset-toggleable .p-fieldset-legend a .p-fieldset-toggler {
    color: #64748b !important;
  }
  .p-fieldset.p-fieldset-toggleable .p-fieldset-legend:hover {
    background-color: #f1f5f9 !important;
  }
  .p-fieldset .p-fieldset-legend {
    border: 0 none !important;
    padding: 0 !important;
    margin-bottom: 0.375rem !important;
  }
  .p-fieldset .p-fieldset-legend span {
    padding: 0.5rem 0.75rem !important;
  }
  .p-fieldset .p-fieldset-content {
    padding: 0 !important;
  }

  .p-column-filter-overlay-menu {
    padding: 0.75rem !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 0.5rem !important;
  }
  .p-column-filter-overlay-menu .p-column-filter-operator {
    padding: 0 !important;
  }
  .p-column-filter-overlay-menu .p-column-filter-constraints {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.5rem !important;
  }
  .p-column-filter-overlay-menu .p-column-filter-constraint {
    padding: 0 0 0 0 !important;
  }
  .p-column-filter-overlay-menu .p-column-filter-constraint .p-column-filter-remove-button {
    margin-bottom: 0.5rem !important;
  }
  .p-column-filter-overlay-menu .p-column-filter-constraint:last-child .p-column-filter-remove-button {
    margin-bottom: 0 !important;
  }
  .p-column-filter-overlay-menu .p-column-filter-add-rule {
    padding: 0 !important;
  }
  .p-column-filter-overlay-menu .p-column-filter-buttonbar {
    padding: 0 !important;
  }

  .p-fileupload .p-fileupload-content {
    border-top: 0 none !important;
    padding-top: 1.125rem !important;
  }

  .p-galleria .p-galleria-indicators .p-galleria-indicator.p-highlight button {
    background-color: #3B82F6 !important;
  }

  .p-inline-message-text {
    font-weight: 500 !important;
  }

  .p-inline-message {
    backdrop-filter: blur(1.5px) !important;
  }

  .p-inline-message.p-inline-message-info {
    box-shadow: 0px 4px 8px 0px rgba(59, 130, 246, 0.04) !important;
  }

  .p-inline-message.p-inline-message-success {
    box-shadow: 0px 4px 8px 0px rgba(34, 197, 94, 0.04) !important;
  }

  .p-inline-message.p-inline-message-warn {
    box-shadow: 0px 4px 8px 0px rgba(234, 179, 8, 0.04) !important;
  }

  .p-inline-message.p-inline-message-error {
    box-shadow: 0px 4px 8px 0px rgba(239, 68, 68, 0.04) !important;
  }

  .p-inline-message.p-inline-message-secondary {
    box-shadow: 0px 4px 8px 0px rgba(74, 85, 103, 0.04) !important;
  }

  .p-inline-message.p-inline-message-contrast {
    box-shadow: 0px 4px 8px 0px rgba(2, 6, 23, 0.04) !important;
  }

  .p-inputgroup-addon {
    padding: 0.5rem !important;
  }

  .p-inputnumber.p-inputnumber-buttons-stacked {
    position: relative !important;
  }
  .p-inputnumber.p-inputnumber-buttons-stacked .p-inputnumber-input {
    border-top-right-radius: 5px !important;
    border-bottom-right-radius: 5px !important;
  }
  .p-inputnumber.p-inputnumber-buttons-stacked .p-inputnumber-button-group {
    position: absolute !important;
    top: 1px !important;
    right: 1px !important;
    height: calc(100% - 2px) !important;
  }
  .p-inputnumber.p-inputnumber-buttons-stacked .p-inputnumber-button {
    border-top-right-radius: 5px !important;
    border-bottom-right-radius: 5px !important;
    background-color: transparent !important;
    border: 0 none !important;
    color: #64748b !important;
  }
  .p-inputnumber.p-inputnumber-buttons-stacked .p-inputnumber-button:hover {
    background-color: #f1f5f9 !important;
    color: #475569 !important;
  }
  .p-inputnumber.p-inputnumber-buttons-stacked .p-inputnumber-button:active {
    background-color: #e2e8f0 !important;
    color: #334155 !important;
  }
  .p-inputnumber.p-inputnumber-buttons-horizontal .p-inputnumber-button {
    background-color: transparent !important;
    border: 1px solid #cbd5e1 !important;
    color: #64748b !important;
  }
  .p-inputnumber.p-inputnumber-buttons-horizontal .p-inputnumber-button:hover {
    background-color: #f1f5f9 !important;
    color: #475569 !important;
  }
  .p-inputnumber.p-inputnumber-buttons-horizontal .p-inputnumber-button:active {
    background-color: #e2e8f0 !important;
    color: #334155 !important;
  }
  .p-inputnumber.p-inputnumber-buttons-horizontal .p-inputnumber-button.p-inputnumber-button-up {
    border-left: 0 none !important;
  }
  .p-inputnumber.p-inputnumber-buttons-horizontal .p-inputnumber-button.p-inputnumber-button-down {
    border-right: 0 none !important;
  }
  .p-inputnumber.p-inputnumber-buttons-vertical .p-inputnumber-button {
    background-color: transparent !important;
    border: 1px solid #cbd5e1 !important;
    color: #64748b !important;
  }
  .p-inputnumber.p-inputnumber-buttons-vertical .p-inputnumber-button:hover {
    background-color: #f1f5f9 !important;
    color: #475569 !important;
  }
  .p-inputnumber.p-inputnumber-buttons-vertical .p-inputnumber-button:active {
    background-color: #e2e8f0 !important;
    color: #334155 !important;
  }
  .p-inputnumber.p-inputnumber-buttons-vertical .p-inputnumber-button.p-inputnumber-button-up {
    border-bottom: 0 none !important;
  }
  .p-inputnumber.p-inputnumber-buttons-vertical .p-inputnumber-button.p-inputnumber-button-down {
    border-top: 0 none !important;
  }

  .p-inputswitch .p-inputswitch-slider {
    border: 0 none !important;
  }
  .p-inputswitch.p-highlight p-inputswitch-slider:before {
    left: 1.25rem !important;
    transform: none !important;
  }
  .p-inputswitch.p-invalid > .p-inputswitch-slider {
    background: #f87171 !important;
  }
  .p-inputswitch.p-invalid > .p-inputswitch-slider:before {
    background: #ffffff !important;
  }

  .p-inputtext {
    box-shadow: 0 0 #0000, 0 0 #0000, 0 1px 2px 0 rgba(18, 18, 23, 0.05) !important;
  }

  .p-inputtext:disabled {
    opacity: 1 !important;
    background-color: #e2e8f0 !important;
    color: #000 !important;
  }
  .p-password-input.p-disabled{
    opacity: 1 !important;
    background-color: #e2e8f0 !important;
    color: #000 !important;
  }

  .p-knob svg {
    border-radius: 6px !important;
    outline-color: transparent !important;
    transition: outline-color 0.2s !important;
  }
  .p-knob svg:focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }

  .p-listbox {
    box-shadow: 0 0 #0000, 0 0 #0000, 0 1px 2px 0 rgba(18, 18, 23, 0.05) !important;
  }
  .p-listbox.p-disabled {
    opacity: 1 !important;
    background-color: #e2e8f0 !important;
  }
  .p-listbox.p-disabled .p-listbox-list .p-listbox-item {
    color: #64748b !important;
  }
  .p-listbox .p-listbox-header:has(.p-checkbox) {
    padding: 0.5rem 1rem 0 1rem !important;
  }

  .p-message {
    backdrop-filter: blur(1.5px) !important;
  }

  .p-message.p-message-info {
    box-shadow: 0px 4px 8px 0px rgba(59, 130, 246, 0.04) !important;
  }
  .p-message.p-message-info .p-message-close:focus-visible {
    outline-color: #2563eb !important;
  }
  .p-message.p-message-info .p-message-close:hover {
    background: #dbeafe !important;
  }

  .p-message.p-message-success {
    box-shadow: 0px 4px 8px 0px rgba(34, 197, 94, 0.04) !important;
  }
  .p-message.p-message-success .p-message-close:focus-visible {
    outline-color: #16a34a !important;
  }
  .p-message.p-message-success .p-message-close:hover {
    background: #dcfce7 !important;
  }

  .p-message.p-message-warn {
    box-shadow: 0px 4px 8px 0px rgba(234, 179, 8, 0.04) !important;
  }
  .p-message.p-message-warn .p-message-close:focus-visible {
    outline-color: #ca8a04 !important;
  }
  .p-message.p-message-warn .p-message-close:hover {
    background: #fef9c3 !important;
  }

  .p-message.p-message-error {
    box-shadow: 0px 4px 8px 0px rgba(239, 68, 68, 0.04) !important;
  }
  .p-message.p-message-error .p-message-close:focus-visible {
    outline-color: #dc2626 !important;
  }
  .p-message.p-message-error .p-message-close:hover {
    background: #fee2e2 !important;
  }

  .p-message.p-message-secondary {
    box-shadow: 0px 4px 8px 0px rgba(74, 85, 103, 0.04) !important;
  }
  .p-message.p-message-secondary .p-message-close:focus-visible {
    outline-color: #475569 !important;
  }
  .p-message.p-message-secondary .p-message-close:hover {
    background: #e2e8f0 !important;
  }

  .p-message.p-message-contrast {
    box-shadow: 0px 4px 8px 0px rgba(2, 6, 23, 0.04) !important;
  }
  .p-message.p-message-contrast .p-message-close:focus-visible {
    outline-color: #f8fafc !important;
  }
  .p-message.p-message-contrast .p-message-close:hover {
    background: #1e293b !important;
  }

  .p-multiselect-panel .p-multiselect-header {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  .p-multiselect {
    box-shadow: 0 0 #0000, 0 0 #0000, 0 1px 2px 0 rgba(18, 18, 23, 0.05) !important;
  }
  .p-multiselect.p-disabled {
    opacity: 1 !important;
    background-color: #e2e8f0 !important;
  }
  .p-multiselect.p-disabled .p-multiselect-label {
    color: #64748b !important;
  }
  .p-multiselect.p-multiselect-chip .p-multiselect-token {
    border-radius: 4px !important;
    margin-right: 0.25rem !important;
  }
  .p-multiselect.p-multiselect-chip .p-multiselect-token .p-multiselect-token-icon {
    margin-left: 0.375rem !important;
  }

  .p-inputwrapper-filled .p-multiselect-chip .p-multiselect-label {
    padding: 0.25rem 0.25rem !important;
  }

  .p-panelmenu .p-panelmenu-header {
    border-radius: 4px !important;
  }
  .p-panelmenu .p-panelmenu-header:not(.p-disabled):focus-visible .p-panelmenu-header-content {
    outline: 0 none !important;
  }
  .p-panelmenu .p-panelmenu-header:not(.p-disabled):focus-visible .p-panelmenu-header-content .p-panelmenu-header-action {
    background: #e2e8f0 !important;
  }
  .p-panelmenu .p-panelmenu-header:not(.p-disabled):focus-visible .p-panelmenu-header-content .p-panelmenu-header-action .p-menuitem-text {
    color: #334155 !important;
  }
  .p-panelmenu .p-panelmenu-header:not(.p-disabled):focus-visible .p-panelmenu-header-content .p-panelmenu-header-action .p-submenu-icon {
    color: #334155 !important;
  }
  .p-panelmenu .p-panelmenu-header:not(.p-disabled):focus-visible .p-panelmenu-header-content .p-panelmenu-header-action .p-menuitem-icon {
    color: #334155 !important;
  }
  .p-panelmenu .p-panelmenu-header .p-panelmenu-header-content {
    border-radius: 4px !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-panelmenu .p-panelmenu-header .p-panelmenu-header-content .p-panelmenu-header-action {
    color: #334155 !important;
    padding: 0.5rem 0.75rem !important;
    font-weight: 600 !important;
    border-radius: 4px !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-panelmenu .p-panelmenu-header .p-panelmenu-header-content .p-panelmenu-header-action .p-menuitem-text {
    color: #334155 !important;
  }
  .p-panelmenu .p-panelmenu-header .p-panelmenu-header-content .p-panelmenu-header-action .p-submenu-icon {
    color: #94a3b8 !important;
  }
  .p-panelmenu .p-panelmenu-header .p-panelmenu-header-content .p-panelmenu-header-action .p-menuitem-icon {
    color: #94a3b8 !important;
  }
  .p-panelmenu .p-panelmenu-header .p-panelmenu-header-content .p-panelmenu-header-action:hover {
    background: #f1f5f9 !important;
  }
  .p-panelmenu .p-panelmenu-header .p-panelmenu-header-content .p-panelmenu-header-action:hover .p-menuitem-text {
    color: #334155 !important;
  }
  .p-panelmenu .p-panelmenu-header .p-panelmenu-header-content .p-panelmenu-header-action:hover .p-submenu-icon {
    color: #334155 !important;
  }
  .p-panelmenu .p-panelmenu-header .p-panelmenu-header-content .p-panelmenu-header-action:hover .p-menuitem-icon {
    color: #334155 !important;
  }
  .p-panelmenu .p-panelmenu-header .p-panelmenu-header-content .p-panelmenu-header-action .p-submenu-icon {
    margin-right: 0.5rem !important;
  }
  .p-panelmenu .p-panelmenu-header .p-panelmenu-header-content .p-panelmenu-header-action .p-menuitem-icon {
    margin-right: 0.5rem !important;
  }
  .p-panelmenu .p-panelmenu-content {
    border-radius: 6px !important;
    padding: 0 0 0 1rem !important;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem:first-child {
    margin-top: 2px !important;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem:last-child {
    margin-top: 2px !important;
  }
  .p-panelmenu .p-panelmenu-panel {
    padding: 0.25rem 0.25rem !important;
    overflow: hidden !important;
    margin-bottom: 0.75rem !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 6px !important;
  }

  .p-password-panel {
    background-color: #ffffff !important;
    border: 1px solid #e2e8f0 !important;
    padding: 0.75rem !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1) !important;
    border-radius: 6px !important;
  }
  .p-password-panel .p-password-meter {
    margin-bottom: 0.75rem !important;
    background: #e2e8f0 !important;
    border-radius: 6px !important;
  }
  .p-password-panel .p-password-meter .p-password-strength {
    border-radius: 6px !important;
  }
  .p-password-panel .p-password-meter .p-password-strength.weak {
    background: #ef4444 !important;
  }
  .p-password-panel .p-password-meter .p-password-strength.medium {
    background: #f59e0b !important;
  }
  .p-password-panel .p-password-meter .p-password-strength.strong {
    background: #22c55e !important;
  }

  .p-orderlist-controls .p-button {
    transition: opacity 0.2s, outline-color 0.2s, background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s !important;
    color: #475569 !important;
    background: #f1f5f9 !important;
    border: 1px solid #f1f5f9 !important;
  }
  .p-orderlist-controls .p-button:not(:disabled):hover {
    background: #e2e8f0 !important;
    color: #334155 !important;
    border-color: #e2e8f0 !important;
  }
  .p-orderlist-controls .p-button:not(:disabled):focus {
    box-shadow: none !important;
  }
  .p-orderlist-controls .p-button:not(:disabled):active {
    background: #cbd5e1 !important;
    color: #1e293b !important;
    border-color: #cbd5e1 !important;
  }

  .p-orderlist .p-orderlist-header {
    background: #ffffff !important;
    color: #334155 !important;
    border: 0 none !important;
    border-bottom: 0 none !important;
    padding: 0.75rem 1rem 0.5rem 1rem !important;
    font-weight: 600 !important;
    color: #64748b !important;
    border-top-right-radius: 6px !important;
    border-top-left-radius: 6px !important;
  }
  .p-orderlist .p-orderlist-list {
    border: 1px solid #cbd5e1 !important;
    border: 0 none !important;
    background: #ffffff !important;
    color: #334155 !important;
    padding: 0.25rem 0.25rem !important;
    border-bottom-right-radius: 6px !important;
    border-bottom-left-radius: 6px !important;
  }
  .p-orderlist .p-orderlist-list .p-orderlist-item {
    border-radius: 6px !important;
  }
  .p-orderlist .p-orderlist-list:not(:first-child) {
    border: 0 none !important;
  }
  .p-orderlist .p-orderlist-filter-container {
    border: 0 none !important;
  }

  .p-organizationchart .p-organizationchart-node-content {
    padding: 0.75rem 1rem !important;
    border-radius: 6px !important;
  }
  .p-organizationchart .p-organizationchart-node-content .p-node-toggler {
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    border: 1px solid #e2e8f0 !important;
    display: inline-flex !important;
    justify-content: center !important;
    align-items: center !important;
  }
  .p-organizationchart .p-organizationchart-node-content .p-node-toggler .p-node-toggler-icon {
    position: static !important;
  }
  .p-organizationchart .p-organizationchart-node-content:has(.p-node-toggler) {
    padding: 0.75rem 1rem 1.25rem 1rem !important;
  }
  .p-organizationchart .p-organizationchart-lines :nth-child(1 of .p-organizationchart-line-left) {
    border-right: 0 none !important;
  }
  .p-organizationchart .p-organizationchart-lines :nth-last-child(1 of .p-organizationchart-line-left) {
    border-top-right-radius: 6px !important;
  }
  .p-organizationchart .p-organizationchart-lines :nth-child(1 of .p-organizationchart-line-right) {
    border-left: 1px solid #e2e8f0 !important;
    border-top-left-radius: 6px !important;
  }

  .p-overlaypanel {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1) !important;
  }
  .p-overlaypanel .p-overlaypanel-content {
    padding: 0.75rem !important;
  }
  .p-overlaypanel .p-overlaypanel-close {
    width: 1.75rem !important;
    height: 1.75rem !important;
    color: #64748b !important;
    border: 0 none !important;
    background: transparent !important;
    border-radius: 50% !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    position: absolute !important;
    top: 0.25rem !important;
    right: 0.25rem !important;
  }
  .p-overlaypanel .p-overlaypanel-close:enabled:hover {
    color: #475569 !important;
    border-color: transparent !important;
    background: #f1f5f9 !important;
  }
  .p-overlaypanel .p-overlaypanel-close:focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }

  .p-panel {
    border: 1px solid #e2e8f0 !important;
    border-radius: 6px !important;
    background-color: #ffffff !important;
  }
  .p-panel .p-panel-header,
.p-panel .p-panel-content,
.p-panel .p-panel-footer {
    background: transparent !important;
    border: 0 none;
  }
  .p-panel:has(.p-panel-footer) .p-panel-content {
    padding-bottom: 0.875rem !important;
  }

  .p-picklist-buttons .p-button {
    transition: opacity 0.2s, outline-color 0.2s, background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s !important;
    color: #475569 !important;
    background: #f1f5f9 !important;
    border: 1px solid #f1f5f9 !important;
  }
  .p-picklist-buttons .p-button:not(:disabled):hover {
    background: #e2e8f0 !important;
    color: #334155 !important;
    border-color: #e2e8f0 !important;
  }
  .p-picklist-buttons .p-button:not(:disabled):focus {
    box-shadow: none !important;
  }
  .p-picklist-buttons .p-button:not(:disabled):active {
    background: #cbd5e1 !important;
    color: #1e293b !important;
    border-color: #cbd5e1 !important;
  }

  .p-picklist .p-picklist-header {
    background: #ffffff !important;
    color: #334155 !important;
    border: 1px solid #cbd5e1 !important;
    border: 0 none !important;
    padding: 0.75rem 1rem 0.5rem 1rem !important;
    font-weight: 600 !important;
    color: #64748b !important;
    border-top-right-radius: 6px !important;
    border-top-left-radius: 6px !important;
  }
  .p-picklist .p-picklist-filter-container {
    border: 0 !important;
  }
  .p-picklist .p-picklist-list {
    border: 1px solid #cbd5e1 !important;
    border: 0 none !important;
    background: #ffffff !important;
    color: #334155 !important;
    padding: 0.25rem 0.25rem !important;
    border-bottom-right-radius: 6px !important;
    border-bottom-left-radius: 6px !important;
  }
  .p-picklist .p-picklist-list .p-picklist-item {
    border-radius: 6px !important;
  }
  .p-picklist .p-picklist-list:not(:first-child) {
    border: 0 none !important;
  }

  .p-progressbar-label {
    font-size: 0.75rem !important;
    font-weight: 600 !important;
  }

  .p-radiobutton .p-radiobutton-box {
    box-shadow: 0 0 #0000, 0 0 #0000, 0 1px 2px 0 rgba(18, 18, 23, 0.05) !important;
  }
  .p-radiobutton .p-radiobutton-box.p-focus {
    outline-offset: 2px !important;
  }
  .p-radiobutton .p-radiobutton-box.p-focus.p-highlight {
    border-color: #3B82F6 !important;
  }
  .p-radiobutton.p-radiobutton-disabled {
    opacity: 1 !important;
  }
  .p-radiobutton.p-radiobutton-disabled .p-radiobutton-box {
    border: 1px solid #cbd5e1 !important;
    background-color: #e2e8f0 !important;
  }
  .p-radiobutton.p-radiobutton-disabled .p-radiobutton-box .p-radiobutton-icon {
    background-color: #64748b !important;
  }

  .p-rating {
    gap: 0.25rem !important;
  }

  .p-selectbutton .p-button .pi,
.p-selectbutton .p-button .p-button-label {
    position: relative !important;
    transition: none !important;
  }
  .p-selectbutton .p-button::before {
    content: "" !important;
    background-color: transparent !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    position: absolute !important;
    left: 0.25rem !important;
    top: 0.25rem !important;
    width: calc(100% - 0.5rem) !important;
    height: calc(100% - 0.5rem) !important;
    border-radius: 4px !important;
  }
  .p-selectbutton .p-button.p-highlight::before {
    background: #ffffff !important;
    border-color: #ffffff !important;
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.02), 0px 1px 2px 0px rgba(0, 0, 0, 0.04) !important;
  }
  .p-selectbutton .p-button:focus-visible {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: -1px !important;
    box-shadow: none !important;
    border-color: #94a3b8 !important;
  }
  .p-selectbutton .p-button.p-disabled {
    opacity: 1 !important;
    color: #94a3b8 !important;
  }
  .p-selectbutton.p-invalid {
    box-shadow: 0 0 0 1px #f87171 !important;
    border-radius: 6px !important;
  }
  .p-selectbutton.p-invalid > .p-button {
    border: 1px solid #f1f5f9 !important;
  }
  .p-selectbutton.p-disabled {
    opacity: 1 !important;
  }
  .p-selectbutton.p-disabled .p-button {
    color: #94a3b8 !important;
  }

  .p-slider .p-slider-handle {
    display: flex !important !important;
    justify-content: center !important;
    align-items: center !important;
  }
  .p-slider .p-slider-handle::before {
    content: "" !important;
    width: 16px !important;
    height: 16px !important;
    display: block !important;
    background-color: #ffffff !important;
    border-radius: 50% !important;
    box-shadow: 0px 0.5px 0px 0px rgba(0, 0, 0, 0.08), 0px 1px 1px 0px rgba(0, 0, 0, 0.14) !important;
  }
  .p-slider .p-slider-handle:focus-visible {
    outline: 0 none !important;
    box-shadow: 0 0 0 2px #ffffff, 0 0 0 4px var(--p-focus-ring-color), 0 1px 2px 0 black !important;
  }

  .p-speeddial-item.p-focus > .p-speeddial-action {
    outline-color: #9dc1fb !important;
  }

  .p-speeddial-button.p-speeddial-rotate {
    transition: transform 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }

  .p-splitter-gutter-handle {
    border-radius: 6px !important;
  }

  .p-stepper .p-stepper-header .p-stepper-number::after {
    content: " " !important;
    position: absolute !important;
    height: 100% !important;
    box-shadow: 0px 0.5px 0px 0px rgba(0, 0, 0, 0.06), 0px 1px 1px 0px rgba(0, 0, 0, 0.12) !important;
  }
  .p-stepper .p-stepper-header.p-highlight .p-stepper-number {
    background: #ffffff !important;
    color: #3B82F6 !important;
  }
  .p-stepper .p-stepper-header.p-highlight .p-stepper-title {
    color: #3B82F6 !important;
  }

  .p-steps .p-steps-item .p-menuitem-link .p-steps-number {
    border-width: 2px !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-steps .p-steps-item .p-menuitem-link .p-steps-title {
    font-weight: 500 !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-steps .p-steps-item .p-steps-number {
    position: relative !important;
    font-weight: 500 !important;
  }
  .p-steps .p-steps-item .p-steps-number::after {
    content: " " !important;
    position: absolute !important;
    width: 100% !important;
    height: 100% !important;
    border-radius: 50% !important;
    box-shadow: 0px 0.5px 0px 0px rgba(0, 0, 0, 0.06), 0px 1px 1px 0px rgba(0, 0, 0, 0.12) !important;
  }
  .p-steps .p-steps-item.p-highlight .p-steps-number {
    background: #ffffff !important;
    color: #3B82F6 !important;
  }
  .p-steps .p-steps-item.p-highlight .p-steps-title {
    color: #3B82F6 !important;
  }
  .p-steps .p-steps-item.p-disabled {
    opacity: 1 !important;
  }
  .p-steps .p-steps-item:before {
    border-top-width: 2px !important;
    margin-top: calc(-1rem + 1px) !important;
  }
  .p-steps .p-steps-item:first-child::before {
    width: calc(50% + 1rem) !important;
    transform: translateX(100%) !important;
  }
  .p-steps .p-steps-item:last-child::before {
    width: 50% !important;
  }

  .p-tabmenu .p-tabmenu-ink-bar {
    z-index: 1 !important;
    display: block !important;
    position: absolute !important;
    bottom: -1px !important;
    height: 1px !important;
    background-color: #3B82F6 !important;
    transition: 250ms cubic-bezier(0.35, 0, 0.25, 1) !important;
  }
  .p-tabmenu .p-tabmenu-nav {
    position: relative !important;
  }
  .p-tabmenu .p-tabmenu-nav .p-tabmenuitem .p-menuitem-link {
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-tabmenu .p-tabmenu-nav-container .p-tabmenu-nav-btn {
    outline: 0 none !important;
  }

  .p-tabview .p-tabview-ink-bar {
    z-index: 1 !important;
    display: block !important;
    position: absolute !important;
    bottom: 0 !important;
    height: 1px !important;
    background-color: #3B82F6 !important;
    transition: 250ms cubic-bezier(0.35, 0, 0.25, 1) !important;
  }
  .p-tabview .p-tabview-nav {
    position: relative !important;
  }
  .p-tabview .p-tabview-nav li .p-tabview-nav-link {
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  }
  .p-tabview .p-tabview-nav-btn.p-link {
    color: #64748b !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12) !important;
    box-shadow: 0px 0px 10px 50px rgba(255, 255, 255, 0.6) !important;
  }
  .p-tabview .p-tabview-nav-btn.p-link:hover {
    color: #475569 !important;
  }

  .p-tag {
    background: var(--primary-100) !important;
    color: var(--primary-700) !important;
  }
  .p-tag.p-tag-success {
    background: var(--green-100) !important;
    color: var(--green-700) !important;
  }
  .p-tag.p-tag-info {
    background: var(--blue-100) !important;
    color: var(--blue-700) !important;
  }
  .p-tag.p-tag-warning {
    background: var(--orange-100) !important;
    color: var(--orange-700) !important;
  }
  .p-tag.p-tag-danger {
    background: var(--red-100) !important;
    color: var(--red-700) !important;
  }

  .p-terminal {
    background: #ffffff !important;
    color: #334155 !important;
    border: 1px solid #cbd5e1 !important;
    padding: 0.5rem 0.75rem !important;
    border-radius: 6px !important;
  }
  .p-terminal .p-terminal-prompt {
    margin-right: 0.25rem !important;
  }
  .p-terminal .p-terminal-response {
    margin: 2px 0 !important;
  }

  .p-timeline .p-timeline-event-marker {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    position: relative !important;
  }
  .p-timeline .p-timeline-event-marker::before {
    content: " " !important;
    border-radius: 50% !important;
    width: 0.375rem !important;
    height: 0.375rem !important;
    background-color: #3B82F6 !important;
  }
  .p-timeline .p-timeline-event-marker::after {
    content: " " !important;
    position: absolute !important;
    width: 100% !important;
    height: 100% !important;
    border-radius: 50% !important;
    box-shadow: 0px 0.5px 0px 0px rgba(0, 0, 0, 0.06), 0px 1px 1px 0px rgba(0, 0, 0, 0.12) !important;
  }

  .p-toast .p-toast-message {
    backdrop-filter: blur(1.5px) !important;
  }
  .p-toast .p-toast-message .p-toast-message-content .p-toast-detail {
    font-size: 16px !important;
  }
  .p-toast .p-toast-message .p-toast-message-content .p-toast-icon-close {
    width: 1.75rem !important;
    height: 1.75rem !important;
    position: relative !important;
  }

  .p-toast .p-toast-message.p-toast-message-info {
    box-shadow: 0px 4px 8px 0px rgba(59, 130, 246, 0.04) !important;
  }
  .p-toast .p-toast-message.p-toast-message-info .p-toast-detail {
    color: #334155 !important;
  }
  .p-toast .p-toast-message.p-toast-message-info .p-toast-icon-close {
    outline-color: #2563eb !important;
  }
  .p-toast .p-toast-message.p-toast-message-info .p-toast-icon-close:hover {
    background: #dbeafe !important;
  }
  .p-toast .p-toast-message.p-toast-message-success {
    box-shadow: 0px 4px 8px 0px rgba(34, 197, 94, 0.04) !important;
  }
  .p-toast .p-toast-message.p-toast-message-success .p-toast-detail {
    color: #334155 !important;
  }
  .p-toast .p-toast-message.p-toast-message-success .p-toast-icon-close {
    outline-color: #16a34a !important;
  }
  .p-toast .p-toast-message.p-toast-message-success .p-toast-icon-close:hover {
    background: #dcfce7 !important;
  }
  .p-toast .p-toast-message.p-toast-message-warn {
    box-shadow: 0px 4px 8px 0px rgba(234, 179, 8, 0.04) !important;
  }
  .p-toast .p-toast-message.p-toast-message-warn .p-toast-detail {
    color: #334155 !important;
  }
  .p-toast .p-toast-message.p-toast-message-warn .p-toast-icon-close {
    outline-color: #ca8a04 !important;
  }
  .p-toast .p-toast-message.p-toast-message-warn .p-toast-icon-close:hover {
    background: #fef9c3 !important;
  }
  .p-toast .p-toast-message.p-toast-message-error {
    box-shadow: 0px 4px 8px 0px rgba(239, 68, 68, 0.04) !important;
  }
  .p-toast .p-toast-message.p-toast-message-error .p-toast-detail {
    color: #334155 !important;
  }
  .p-toast .p-toast-message.p-toast-message-error .p-toast-icon-close {
    outline-color: #dc2626 !important;
  }
  .p-toast .p-toast-message.p-toast-message-error .p-toast-icon-close:hover {
    background: #fee2e2 !important;
  }
  .p-toast .p-toast-message.p-toast-message-secondary {
    box-shadow: 0px 4px 8px 0px rgba(74, 85, 103, 0.04) !important;
  }
  .p-toast .p-toast-message.p-toast-message-secondary .p-toast-icon-close {
    outline-color: #dc2626 !important;
  }
  .p-toast .p-toast-message.p-toast-message-secondary .p-toast-icon-close:hover {
    background: #e2e8f0 !important;
  }
  .p-toast .p-toast-message.p-toast-message-contrast {
    box-shadow: 0px 4px 8px 0px rgba(2, 6, 23, 0.04) !important;
  }
  .p-toast .p-toast-message.p-toast-message-contrast .p-toast-icon-close {
    outline-color: #dc2626 !important;
  }
  .p-toast .p-toast-message.p-toast-message-contrast .p-toast-icon-close:hover {
    background: #1e293b !important;
  }

  .p-togglebutton.p-button {
    padding: 0.25rem !important;
  }
  .p-togglebutton.p-button .p-button-label {
    padding: 0.25rem 0.5rem !important;
  }
  .p-togglebutton.p-button .p-button-icon {
    padding: 0.35rem 0.25rem !important;
  }
  .p-togglebutton.p-button .p-button-label,
.p-togglebutton.p-button .p-button-icon {
    position: relative !important;
    transition: none !important;
  }
  .p-togglebutton.p-button::before {
    content: "" !important;
    background-color: transparent !important;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
    position: absolute !important;
    left: 0.25rem !important;
    top: 0.25rem !important;
    width: calc(100% - 0.5rem) !important;
    height: calc(100% - 0.5rem) !important;
    border-radius: 4px !important;
  }
  .p-togglebutton.p-highlight .p-button::before {
    background: #ffffff !important;
    border-color: #ffffff !important;
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.02), 0px 1px 2px 0px rgba(0, 0, 0, 0.04) !important;
  }
  .p-togglebutton.p-highlight .p-button-label {
    background: #ffffff !important;
    border-color: #ffffff !important;
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.02), 0px 1px 2px 0px rgba(0, 0, 0, 0.04) !important;
    border-radius: 4px !important;
  }
  .p-togglebutton.p-highlight:has(.p-button-icon) .p-button-icon {
    margin-right: 0 !important;
    background: #ffffff !important;
    box-shadow: 2px 2px 2px 0px rgba(0, 0, 0, 0.02), -2px 2px 2px 0px rgba(0, 0, 0, 0.04) !important;
    border: none !important;
    border-radius: 4px 0px 0px 4px !important;
  }
  .p-togglebutton.p-highlight:has(.p-button-icon) .p-button-label {
    background: #ffffff !important;
    box-shadow: 2px 2px 2px 0px rgba(0, 0, 0, 0.02), 2px 2px 2px 0px rgba(0, 0, 0, 0.04) !important;
    border-radius: 0px 4px 4px 0px !important;
  }
  .p-togglebutton:has(:not(.p-highlight)):has(.p-button-icon) .p-button-icon {
    margin-right: 0 !important;
    border: none !important;
    border-radius: 4px 0px 0px 4px !important;
  }
  .p-togglebutton:has(:not(.p-highlight)):has(.p-button-icon) .p-button-label {
    border-radius: 0px 4px 4px 0px !important;
  }
  .p-togglebutton.p-disabled {
    opacity: 1 !important;
    color: #94a3b8 !important;
  }
  .p-togglebutton.p-disabled .p-button-icon {
    color: #94a3b8 !important;
  }

  .p-toolbar {
    padding: 0.75rem !important;
  }

  .p-tree {
    border: 0 none !important;
    padding: 1rem !important;
  }
  .p-tree .p-tree-container .p-treenode {
    margin: 2px 0 !important;
  }
  .p-tree .p-tree-container .p-treenode:focus > .p-treenode-content {
    outline-offset: -2px !important;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content.p-highlight {
    background: none !important;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content.p-highlight .p-tree-toggler:hover {
    background-color: #EFF6FF !important;
  }
  .p-tree .p-tree-container > .p-treenode:first-child {
    margin-top: 0 !important;
  }
  .p-tree .p-tree-container > .p-treenode:last-child {
    margin-bottom: 0 !important;
  }

  .p-treeselect-panel .p-tree {
    padding: 0.25rem 0.25rem !important;
  }

  .p-treeselect {
    box-shadow: 0 0 #0000, 0 0 #0000, 0 1px 2px 0 rgba(18, 18, 23, 0.05) !important;
  }
  .p-treeselect.p-disabled {
    opacity: 1 !important;
    background-color: #e2e8f0 !important;
  }
  .p-treeselect.p-disabled .p-treeselect-label {
    color: #64748b !important;
  }
  .p-treeselect.p-treeselect-chip .p-treeselect-label-container .p-treeselect-token {
    border-radius: 4px !important;
  }
}
