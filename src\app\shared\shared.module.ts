import { CommonModule, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Ng<PERSON><PERSON> } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ConfirmationService } from 'primeng/api';
import { DialogService } from 'primeng/dynamicdialog';

import { PrimeNgModule } from './primeng.module';
import {
  TpsCellActionComponent,
  TpsCheckboxComponent,
  TpsDatePickerComponent,
  TpsFileListComponent,
  TpsFormLayoutComponent,
  TpsHeaderComponent,
  TpsIconComponent,
  TpsMultiFormFieldComponent,
  TpsMultiSelectComponent,
  TpsNumberComponent,
  TpsPrimaryButtonComponent,
  TpsRadioComponent,
  TpsSecondaryButtonComponent,
  TpsSelectedButtonComponent,
  TpsSingleSelectComponent,
  TpsSliderComponent,
  TpsTableComponent,
  TpsTextAreaComponent,
  TpsTextComponent,
  TpsTextSearchComponent,
  TpsToggleButtonComponent,
} from './tapas-ui';





@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    PrimeNgModule,
    NgFor,
    NgIf,
    NgSwitch,
    ReactiveFormsModule,
    FormsModule,
    TpsTableComponent,
    TpsCheckboxComponent,
    TpsDatePickerComponent,
    TpsMultiSelectComponent,
    TpsNumberComponent,
    TpsRadioComponent,
    TpsSingleSelectComponent,
    TpsTextComponent,
    TpsMultiFormFieldComponent,
    TpsTextAreaComponent,
    TpsTextSearchComponent,
    TpsHeaderComponent,
    TpsPrimaryButtonComponent,
    TpsSecondaryButtonComponent,
    TpsCellActionComponent,
    TpsFileListComponent,
    TpsFormLayoutComponent,
    TpsSliderComponent,
    TpsToggleButtonComponent,
    TpsSelectedButtonComponent,
    TpsIconComponent
  ],
  exports: [
    CommonModule,
    PrimeNgModule,
    NgFor,
    NgIf,
    NgSwitch,
    ReactiveFormsModule,
    FormsModule,
    TpsTableComponent,
    TpsCheckboxComponent,
    TpsDatePickerComponent,
    TpsMultiSelectComponent,
    TpsNumberComponent,
    TpsRadioComponent,
    TpsSingleSelectComponent,
    TpsTextComponent,
    TpsTextAreaComponent,
    TpsTextSearchComponent,
    TpsSliderComponent,
    TpsToggleButtonComponent,
    TpsMultiFormFieldComponent,
    TpsHeaderComponent,
    TpsPrimaryButtonComponent,
    TpsSecondaryButtonComponent,
    TpsCellActionComponent,
    TpsFileListComponent,
    TpsFormLayoutComponent,
    TpsSelectedButtonComponent,
    TpsIconComponent,
  ],
  providers: [DialogService, ConfirmationService]
})
export class SharedModule { }
