/* 1. Components */

/* 2. Overrides */
@use 'overrides/angular-material';
@use 'overrides/highlightjs';
@use 'overrides/perfect-scrollbar';

// Core style
@use './core.scss';
@use './ag-grid-override.scss';


* {
    font-family: 'Source Sans Pro';
}

*:not(i):not(.fa):not(.ag-icon):not(.pi):not(.material-icons)::not(.mat-icon) {
    font-family: 'Source Sans Pro' !important;
}

* {
    /* Text rendering */
    text-rendering: optimizeLegibility;
    -o-text-rendering: optimizeLegibility;
    -ms-text-rendering: optimizeLegibility;
    -moz-text-rendering: optimizeLegibility;
    -webkit-text-rendering: optimizeLegibility;
    -webkit-tap-highlight-color: transparent;

    /* Remove the focus ring */
    &:focus {
        outline: none !important;
    }
}

/* HTML and Body default styles */
html,
body {
    display: flex;
    flex-direction: column;
    flex: 1 1 auto;
    width: 100%;
    min-height: 100%;
    -webkit-font-smoothing: auto;
    -moz-osx-font-smoothing: auto;
    background: linear-gradient(90deg, #265DAB 12.65%, #2B75BC 30.36%, #3F8CC6 48.58%) !important;
}

/* Font size */
html {
    font-size: 14px !important
}

body {
    font-size: 16px !important
}

/* Stylistic alternates for Inter */
body {
    font-feature-settings: 'salt';
}

/* Better spacing and border for horizontal rule */
hr {
    margin: 32px 0;
    border-bottom-width: 1px;
}

/* Make images and videos to take up all the available space */
img {
    width: 100%;
    vertical-align: top;
}



/* Set the background and foreground colors */
body,
.dark,
.light {
    @apply text-default bg-default #{!important};
}

/* Set the border color */
*,
::before,
::after {
    --tw-border-opacity: 1 !important;
    border-color: transparent;

    .dark & {
        --tw-border-opacity: 0.12 !important;
    }
}

/* Style scrollbars on platforms other than MacOS and iOS */
@media only screen and (min-width: 960px) {

    body:not(.os-mac) {

        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
            background-color: rgba(0, 0, 0, 0);
        }

        ::-webkit-scrollbar:hover {
            width: 8px;
            height: 8px;
            background-color: rgba(0, 0, 0, 0.06);
        }

        ::-webkit-scrollbar-thumb {
            border: 2px solid transparent;
            border-radius: 20px;
            box-shadow: inset 0 0 0 20px rgba(0, 0, 0, 0.24);
        }

        ::-webkit-scrollbar-thumb:active {
            border-radius: 20px;
            box-shadow: inset 0 0 0 20px rgba(0, 0, 0, 0.37);
        }

        &.dark {
            ::-webkit-scrollbar-thumb {
                box-shadow: inset 0 0 0 20px rgba(255, 255, 255, 0.24);
            }

            ::-webkit-scrollbar-thumb:active {
                box-shadow: inset 0 0 0 20px rgba(255, 255, 255, 0.37);
            }
        }
    }
}

/* Set the foreground color for disabled elements */
[disabled] * {
    @apply text-disabled #{'!important'};
}

/* Print styles */
@media print {

    /* Make the base font size smaller for print so everything is scaled nicely */
    html {
        font-size: 12px !important;
    }

    body,
    .dark,
    .light {
        background: none !important;
    }
}
