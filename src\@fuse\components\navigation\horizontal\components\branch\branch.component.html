<div *ngIf="!child" style="border: 5px solid transparent;" [ngClass]="{'fuse-horizontal-navigation-menu-active': trigger.menuOpen,
                 'fuse-horizontal-navigation-menu-active-forced': item.active}" [matMenuTriggerFor]="matMenu"
    (onMenuOpen)="triggerChangeDetection()" (onMenuClose)="triggerChangeDetection()" #trigger="matMenuTrigger">
    <ng-container *ngTemplateOutlet="itemTemplate; context: {$implicit: item}"></ng-container>
</div>

<mat-menu class="fuse-horizontal-navigation-menu-panel" [overlapTrigger]="false" #matMenu="matMenu">

    <ng-container *ngFor="let item of item.children">

        <!-- Skip the hidden items -->
        <ng-container *ngIf="(item.hidden && !item.hidden) || !item.hidden">

            <!-- Basic -->
            <div class="fuse-horizontal-navigation-menu-item" *ngIf="item.type === 'basic'" [disabled]="item.disabled"
                mat-menu-item>
                <fuse-horizontal-navigation-basic-item [item]="item" [name]="name">
                </fuse-horizontal-navigation-basic-item>
            </div>

            <!-- Branch: aside, collapsable, group -->
            <div class="fuse-horizontal-navigation-menu-item"
                *ngIf="item.type === 'aside' || item.type === 'collapsable' || item.type === 'group'"
                [disabled]="item.disabled" [matMenuTriggerFor]="branch.matMenu" mat-menu-item>
                <ng-container *ngTemplateOutlet="itemTemplate; context: {$implicit: item}"></ng-container>
                <fuse-horizontal-navigation-branch-item [child]="true" [item]="item" [name]="name" #branch>
                </fuse-horizontal-navigation-branch-item>
            </div>

            <!-- Divider -->
            <div class="fuse-horizontal-navigation-menu-item" *ngIf="item.type === 'divider'" mat-menu-item>
                <fuse-horizontal-navigation-divider-item [item]="item" [name]="name">
                </fuse-horizontal-navigation-divider-item>
            </div>

        </ng-container>

    </ng-container>

</mat-menu>

<!-- Item template -->
<ng-template let-item #itemTemplate>

    <div class="fuse-horizontal-navigation-item-wrapper"
        [class.fuse-horizontal-navigation-item-has-subtitle]="!!item.subtitle" [ngClass]="item.classes?.wrapper">

        <div class="fuse-horizontal-navigation-item" [ngClass]="{'fuse-horizontal-navigation-item-disabled': item.disabled,
                         'fuse-horizontal-navigation-item-active-forced': item.active}">

            <!-- Icon -->
            <!-- <mat-icon class="fuse-horizontal-navigation-item-icon menu_icon" [ngClass]="item.classes?.icon"
                *ngIf="item.icon" [svgIcon]="item.icon"></mat-icon> -->
            <i *ngIf="item.icon" class="pi {{item.icon}} fuse-horizontal-navigation-item-icon"></i>
            <!-- Title & Subtitle -->
            <div class="fuse-horizontal-navigation-item-title-wrapper ">
                <div class="fuse-horizontal-navigation-item-title"
                    style="display: flex;flex-direction: row;align-items: center;font-weight: 400;font-style: normal;font-size: 14px;"
                    [ngClass]="{'fuse-horizontal-navigation-item-active-forced': item.active}">
                    <span [ngClass]="item.classes?.title">
                        {{item.title}}
                    </span>
                    <img src="assets/svg/arrow_drop_down_black.svg" class="menu_icon normal-menu-icon"
                        *ngIf="item.isParent" />

                    <img src="assets/svg/arrow_down_blue.svg" class="menu_icon active-menu-icon"
                        *ngIf="item.isParent" />
                    <!-- <mat-icon class="menu_icon icon-color">arrow_drop_down</mat-icon> -->
                </div>
                <div class="fuse-horizontal-navigation-item-subtitle  font-size-14 font-style-normal"
                    *ngIf="item.subtitle" [ngClass]="{'fuse-horizontal-navigation-item-active-forced': item.active}">
                    <span [ngClass]="item.classes?.subtitle">
                        {{item.subtitle}}
                    </span>
                </div>
            </div>

            <!-- Badge -->
            <div class="fuse-horizontal-navigation-item-badge" *ngIf="item.badge">
                <div class="fuse-horizontal-navigation-item-badge-content  font-size-14 font-style-normal"
                    [ngClass]="item.badge.classes">
                    {{item.badge.title}}
                </div>
            </div>
        </div>
    </div>

</ng-template>