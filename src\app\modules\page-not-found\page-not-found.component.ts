import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { UI_CONFIGURATION } from 'app/core/navigation/navigation';
import { CommonService } from 'app/shared/tapas-ui';
import { environment } from 'environments/environment';

@Component({
  selector: 'app-page-not-found',
  standalone: true,
  imports: [],
  templateUrl: './page-not-found.component.html'
})
export class PageNotFoundComponent implements OnInit, OnDestroy {
  homePage: string;
  constructor(
    private _commonService: CommonService,
  ) { }

  public ngOnInit(): void {
    this.loadConfig(environment.Partner)
  }
  private loadConfig(client: string) {
    this.homePage = UI_CONFIGURATION[client].homePage;
  }

  public onNavigate(): void {
    this._commonService.navigate(this.homePage);
  }
  public ngOnDestroy(): void {

  }

}
