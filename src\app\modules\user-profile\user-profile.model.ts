import { FORM_CONTROL_TYPES, FORM_FIELDS_CONSTANTS_VALUES } from 'app/shared/tapas-ui';



export const USER_FORM_MODEL = {
    firstName: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "First Name",
        placeholder: 'Ex.Mick',
        show: false,
        rules: {
            required: true,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_NUMERIC_PATTERN_WITH_SPACE,
            maxLength: 255,
        }
    },
    lastName: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Last Name",
        placeholder: 'Ex.Morris',
        show: false,
        rules: {
            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_NUMERIC_PATTERN_WITH_SPACE,
            maxLength: 255,
        }
    },
    userName: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "User Name",
        placeholder: 'Ex.mick123',
        show: false,
        rules: {
            required: true,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_NUMERIC_PATTERN_NO_SPACE,
            minLength: 5,
            maxLength: 20,
        },
        tooltip: "User name should be alphabets and numbers(No space allowed) "
    },
    password: {
        type: FORM_CONTROL_TYPES.PASSWORD,
        value: "",
        label: "Password",
        placeholder: 'Ex.mick@123456',
        show: false,
        rules: {
            required: true,
            maxLength: 255,
        }
    },
    cPassword: {
        type: FORM_CONTROL_TYPES.PASSWORD,
        value: "",
        label: "New Password",
        placeholder: 'Ex.mick@42425',
        show: false,
        rules: {
            maxLength: 255,
        }
    },
    email: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Email",
        placeholder: '<EMAIL>',
        show: false,
        rules: {
            required: true,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.EMAIL_PATTERN,
            maxLength: 255,
        }
    },
    contactNo: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Contact No.",
        placeholder: 'Ex.0803842942',
        show: false,
        rules: {
            // required: true,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.NUMERIC_PATTERN,
            minLength: FORM_FIELDS_CONSTANTS_VALUES.PHONE_NUMBER_LENGTH,
            maxLength: FORM_FIELDS_CONSTANTS_VALUES.PHONE_NUMBER_LENGTH,

        }
    },
    city: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "City",
        placeholder: 'Ex.Bangalore',
        show: false,
        rules: {
            // required: true,
            maxLength: 255,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_PATTERN,
        }
    },
    country: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Country",
        placeholder: 'Ex.India',
        show: false,
        rules: {
            // required: true,
            maxLength: 255,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_PATTERN,
        }
    },
    pinCode: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "PinCode",
        placeholder: 'Ex.123456',
        show: false,
        rules: {
            minLength: 6,
            maxLength: 6,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.NUMERIC_PATTERN,
        }
    },
    role: {
        type: FORM_CONTROL_TYPES.TEXT,
        options: [],
        value: "",
        label: "Role",
        placeholder: 'Select Role',
        show: false,
        rules: {
            required: true,
        }
    },
    mfaEnable: {
        type: FORM_CONTROL_TYPES.RADIO,
        options: [],
        value: "",
        label: "MFA Enabled",
        placeholder: 'MFA Enabled',
        show: false,
        rules: {
            required: false,
        }
    }
}