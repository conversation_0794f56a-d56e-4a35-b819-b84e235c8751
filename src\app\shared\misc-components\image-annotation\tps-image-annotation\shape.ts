export class Shape {
    public id: string;
    public type: string;
    public code: any;
    public name: string;
    public imageName: string;
    public imageUid: string;
    public points: [];
    public zoneType: string
    public direction: string

    constructor() {
        this.id = '';
        this.type = '';
        this.code = '';
        this.imageName = '';
        this.points = [];
        this.name = '';
        this.zoneType = '';
        this.direction = '';

    }
}