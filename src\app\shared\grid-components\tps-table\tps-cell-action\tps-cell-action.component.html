<button mat-icon-button [matMenuTriggerFor]="menu" aria-label="Example icon-button with a menu"
    *ngIf="isShowMenuIconAction">
    <mat-icon>more_vert</mat-icon>
</button>
<mat-menu #menu="matMenu">
    <!-- <button mat-menu-item *ngFor="let action of actionsIconList" [attr.data-action-type]="action.type">
        <div class="flex flex-row gap-2 tfl-flex-align-center" appendTo="body" pTooltip=" {{action.title}}"
            tooltipPosition="bottom" (click)="onClickAction(action)">
            <img [src]="getIcon(action)">
            <span>{{action.title}}</span>
        </div>
    </button> -->
</mat-menu>
<div class="flex flex-row gap-4 tfl-flex-align-center" *ngIf="!isShowMenuIconAction">
    <div class="tfl-icon-button-wrapper">
        <ng-container *ngFor="let action of mainMenuAction;let i=index">
            <p-button *ngIf="!action.isSvg" icon="pi {{action.icon}}" [rounded]="true" [severity]="action.severity"
                [outlined]="true" [pTooltip]="action.title" (click)="onClickAction(action)" tooltipPosition="bottom"
                appendTo="body" />
            <p-button *ngIf="action.isSvg" [outlined]="true" [rounded]="true" [severity]="action.severity"
                [pTooltip]="action.title" (click)="onClickAction(action)" tooltipPosition="bottom" appendTo="body">
                <img style="width:18px;height:18px" src="assets/svg/{{action.icon}}.svg">
            </p-button>
        </ng-container>
        <div mat-icon-button [matMenuTriggerFor]="extraMenu" *ngIf="extraMenuAction.length>0">
            <i class="pi pi-ellipsis-v" style="font-size: 1.25rem"></i>
        </div>
        <mat-menu #extraMenu="matMenu">
            <div class="w-full" *ngFor="let item of extraMenuAction;let i=index" style="padding: 0rem !important;">
                <p-button *ngIf="!item.isSvg" appendTo="body" class="w-full" [text]="true" label="{{item.title}}"
                    icon="pi {{item?.icon}}" (click)="onClickAction(item)" [severity]="item.severity" />

                <p-button *ngIf="item.isSvg" class="w-full" [text]="true" [severity]="action.severity"
                    [pTooltip]="action.title" (click)="onClickAction(action)" tooltipPosition="bottom" appendTo="body">
                    <img style="width:18px;height:18px" src="assets/svg/{{action.icon}}.svg">
                </p-button>
            </div>
        </mat-menu>
    </div>
</div>