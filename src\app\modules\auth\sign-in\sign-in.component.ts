import { NgIf } from '@angular/common';
import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewEncapsulation } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { fuseAnimations } from '@fuse/animations';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { ACL } from 'app/core/acl/acl';
import { AuthService } from 'app/core/auth/auth.service';
import { iUser, QiUser } from 'app/core/models/user.model';
import { CommonService, HTTP_STATUS, InvokeService } from 'app/shared/tapas-ui';
import { environment, PARTNERS } from 'environments/environment';
import { IconFieldModule } from 'primeng/iconfield';
import { InputGroupModule } from 'primeng/inputgroup';
import { InputGroupAddonModule } from 'primeng/inputgroupaddon';
import { InputIconModule } from 'primeng/inputicon';
import { InputOtpModule } from 'primeng/inputotp';
import { InputTextModule } from 'primeng/inputtext';
import { MessageModule } from 'primeng/message';
import { PasswordModule } from 'primeng/password';
import { Subject, takeUntil } from 'rxjs';

import { UI_CONFIGURATION } from '../../../core/navigation/navigation';

@Component({
    selector: 'auth-sign-in',
    templateUrl: './sign-in.component.html',
    styleUrls: ["./sign-in.component.scss"],
    encapsulation: ViewEncapsulation.None,
    animations: fuseAnimations,
    standalone: true,
    imports: [NgIf, FormsModule, ReactiveFormsModule, MessageModule, PasswordModule, InputTextModule, IconFieldModule, InputIconModule, PasswordModule, InputOtpModule, InputGroupModule, InputGroupAddonModule],
})
export class AuthSignInComponent implements OnInit, OnDestroy {
    alert: [{ severity: string; detail: string }] = [{
        severity: 'success',
        detail: '',
    }];
    signInForm: UntypedFormGroup;
    showAlert: boolean = false;
    projectTile: string = environment.projectTitle;
    projectSubTitle: string = environment.projectSubTitle;
    projectPoweredBy: string = environment.projectPoweredBy;
    copyrights: string = "3Frames Software Labs Pvt Ltd";
    currentYear: number = new Date().getFullYear();
    headerLogo: string = `./assets/images/logo/${environment.tenantUid}.png`;
    company_logo: string = `./assets/images/logo/3frames.png`;
    sign_partner_logo: string = `./assets/images/logo/${environment.tenantUid}.png`;
    showOTP: boolean = false;
    showRegister: boolean = false;
    currentApplicationVersion = "1.0";
    authenticationCode: any;
    loggedInUser: iUser = new QiUser();
    private $destroyed: Subject<boolean> = new Subject();
    messages: any[] = [];
    loading: boolean = false;
    public qrCodeImageUrl: any;
    uiConfigurationObject: any;
    constructor(
        private _authService: AuthService,
        private _commonService: CommonService,
        private _formBuilder: UntypedFormBuilder,
        private _invokeService: InvokeService,
    ) {
        this.loadConfig(environment.Partner)
    }

    private loadConfig(client: string) {
        if (UI_CONFIGURATION[client]) {
            this.uiConfigurationObject = UI_CONFIGURATION[client];
        } else {
            this.uiConfigurationObject = UI_CONFIGURATION[PARTNERS.STAGE];
        }

        console.log("uiConfigurationObject:", this.uiConfigurationObject)
        window.top.document.title = `${this.uiConfigurationObject.title}-${this.uiConfigurationObject.subTitle}`;
        this.projectTile = this.uiConfigurationObject.title;
        this.projectSubTitle = this.uiConfigurationObject.subTitle;
    }

    public ngOnInit(): void {
        // Create the form
        this.signInForm = this._formBuilder.group({
            username: ['', [Validators.required]],
            password: ['', Validators.required],
        });
    }

    //1. Get tenant for user
    public getTenantByUser(): void {
        // Disable the form
        this.signInForm.disable();
        // Hide the alert
        this.showAlert = false;

        this._authService.getTenant(this.signInForm.controls['username'].value, this.signInForm.controls['password'].value)
            .pipe(takeUntil(this.$destroyed))
            .subscribe({
                next: response => {
                    if (response.status == HTTP_STATUS.SUCCESS) {
                        let data = response.body;
                        environment.tenantUid = data.tenantUid;
                        localStorage.setItem("tenantUid", JSON.stringify(data.tenantUid));
                        this.loginAfterTenant(this.signInForm.controls['username'].value, this.signInForm.controls['password'].value, data.tenantUid);
                        this.loadConfig(data.tenantUid);
                    } else {
                        this.loginErrorHandling(response);
                    }
                }, error: error => {
                    this.loginErrorHandling(error);
                }
            })
    }

    //2. Login after user got tenant
    public loginAfterTenant(username, password, tenantUid): void {
        if (this.signInForm.invalid) {
            Object.keys(this.signInForm.controls).forEach(key => {
                this.signInForm.controls[key].markAsDirty();
                this.signInForm.controls[key].markAsTouched();
            });
            this.signInForm.updateValueAndValidity();
            return;
        }
        this.signInForm.disable();
        this.showAlert = false;
        this._authService.signIn(this.signInForm.value)
            .pipe(takeUntil(this.$destroyed))
            .subscribe({
                next: response => {
                    if (response) {
                        this.getTenantConfig(response, tenantUid);
                    } else {
                        this.loginErrorHandling(response);
                    }
                },
                error: error => {
                    this.loginErrorHandling(error);
                }
            });

    }

    //3. Get tenant COnfiguration
    public getTenantConfig(user: any, tenantUid: string): void {
        this._authService.getTenantConfig({ username: user.userName, password: user.password }, tenantUid)
            .pipe(takeUntil(this.$destroyed))
            .subscribe({
                next: tenantConfig => {
                    if (tenantConfig) {
                        this.checkTenantConfig(user, tenantConfig)
                    } else {
                        this.checkTenantConfig(user, "")
                    }
                },
                error: error => {
                    console.log("error:", error)
                    this.checkTenantConfig(user, "")
                }
            })
    }
    private checkTenantConfig(user, tenantConfig): void {
        this.setStorage(user, tenantConfig);
        this.loggedInUser = this._authService.getLoggedInUser();
    }
    private setStorage(user: iUser, tenantConfig): void {
        this.loading = true;
        this.loggedInUser = this._authService.getLoggedInUser();
        this._authService.setLoggeInUser(user);
        sessionStorage.setItem('tenantConfig', JSON.stringify(tenantConfig));
        this._commonService.setBasicAuth('Basic ' + btoa(user.userName + ':' + user.password));
        this.getACLListAndStore();
        if (user && user.mfaEnable) {
            this.showOTP = true;
        } else {
            this.showOTP = false;
            this.redirectAfterLogin();
        }
    }
    private getACLListAndStore(): void {
        let user: iUser = this._commonService.getLoggedInUser();
        let getACLByPartner: any = ACL[environment.Partner]
        let acl_list_by_tenant: any = getACLByPartner[user.role];
        sessionStorage.setItem("acl_list", JSON.stringify(acl_list_by_tenant))
    }
    private redirectAfterLogin(): void {
        let redirectURL = this.uiConfigurationObject.homePage;
        this.navigateToPage(redirectURL);
    }




    public onOtpVerification(): void {
        if (this.authenticationCode && this.authenticationCode.length === 6) {
            APP_UI_CONFIG.auth.mfaValidation.paramList.mfaCode = this.authenticationCode;
            this._authService.validateMFACode(this.loggedInUser.userName, this.loggedInUser.password, this.authenticationCode, this.loggedInUser.tenantUid)
                .pipe(takeUntil(this.$destroyed))
                .subscribe({
                    next: response => {
                        if (response == 200) {
                            this.redirectAfterLogin();
                        } else {
                            this._commonService.error("Please enter valid MFA Code")
                        }
                    },
                    error: error => {
                        this._commonService.handleError(error)
                    }

                })
        } else {
            this._commonService.error("Please enter MFA code to login")
        }
    }

    private navigateToPage(redirectURL: string): void {
        setTimeout(() => {
            this.loading = false;
            this._commonService.navigate(redirectURL);
        }, 500);
    }

    private loginErrorHandling(error): void {
        this.showAlert = true;
        this.signInForm.enable();

        if (error.status == HTTP_STATUS.UNAUTHORIZED) {
            this.alert = [{
                severity: 'error',
                detail: 'Invalid username or password.'
            }];
        } else if (error.status == HTTP_STATUS.NOT_CONTENT) {
            this.alert = [{
                severity: 'error',
                detail: 'No Record Found.'
            }];
        } else {
            this.alert = [{
                severity: 'error',
                detail: 'Something Went Wrong Please Try Again After Sometime.'
            }];
        }
    }
    public onOpenTerms(hyperlink): void {
        window.open(hyperlink, "_blank")
    }

    public registerMfaAuthentication(): void {
        this.showRegister = true;
        this.getQrCodeImage();
    }
    public getQrCodeImage(): any {
        this.qrCodeImageUrl = "";
        this._authService.getQRImage(this.loggedInUser.userName, this.loggedInUser.password).subscribe({
            next: response => {
                this.qrCodeImageUrl = response
            }
        })
    }



    //destroy

    public ngOnDestroy(): void {
        this.$destroyed.next(null);
        this.$destroyed.complete();
    }
}
