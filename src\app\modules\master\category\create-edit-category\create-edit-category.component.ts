import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { Util } from 'app/core/common/util';
import { Category } from 'app/core/models/category-model';
import { SharedModule } from 'app/shared/shared.module';
import {
    CommonService,
    FORM_CONTROL_TYPES,
    FormFactoryService,
    HTTP_STATUS,
    InvokeService,
    TABLE_ACTION_TYPES,
} from 'app/shared/tapas-ui';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { ReplaySubject, takeUntil } from 'rxjs';

import { CATEGORY_FORM_MODEL } from './category-form.model';

@Component({
  selector: 'tps-create-edit-category',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './create-edit-category.component.html'
})
export class CreateEditCategoryComponent implements OnI<PERSON><PERSON>, OnDestroy {
  public hdr: string = 'Create Category';
  public categoryForm: FormGroup;
  public fields: any[] = [];
  public rec: Category = new Category();
  isMobile: boolean = false;

  //constants
  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  public TABLE_ACTION_TYPES = TABLE_ACTION_TYPES;
  public status: string;
  
  //unsubscribe
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);
  
  constructor(
    private _commonService: CommonService,
    private _dialogConfig: DynamicDialogConfig,
    public _dialogConfigRef: DynamicDialogRef,
    private _formFactoryService: FormFactoryService,
    private _invokeService: InvokeService,
  ) { }

  ngOnInit(): void {
    // Monitor screen size changes
    this._commonService.isMobile().subscribe(result => {
      this.isMobile = result.matches;
    });

    this.status = this._dialogConfig.data?.status;
    this.buildForm();
    this.configureForm();
  }

  //build form
  private buildForm() {
    const formGroupFields = this._formFactoryService.getFormControlsFields(CATEGORY_FORM_MODEL);
    this.fields = this._formFactoryService.getFieldsList(CATEGORY_FORM_MODEL);
    this.categoryForm = new FormGroup(formGroupFields);
  }
  
  //configure form
  private configureForm(): void {
    switch (this.status) {
      case TABLE_ACTION_TYPES.CREATE:
        break;
      case TABLE_ACTION_TYPES.EDIT:
        this.hdr = "Update Category";
        this.setDataToForm();
        break;
      case TABLE_ACTION_TYPES.VIEW:
        this.hdr = "View Category";
        this.setDataToForm();
        this.categoryForm.disable();
        break;
      default:
        break;
    }
  }

  private setDataToForm(): void {
    this.rec = this._dialogConfig.data?.data;
    this.categoryForm = this._formFactoryService.setFormControlsValues(this.categoryForm, this.rec, this.fields);
  }

  //submit form
  public onSubmit() {
    if (this.isMobile) {
      // Add any mobile-specific handling here
    }
    this.rec = this._formFactoryService.mapFormFieldsDataToModel(this.categoryForm, this.rec, this.fields);
    
    if (this.rec.uuid) {
      this._commonService.confirm(`Are you sure you want to update a category`, this.rec.category)
        .pipe(takeUntil(this.$destroyed))
        .subscribe(response => {
          if (response) {
            APP_UI_CONFIG.master.categories.update = this.rec.uuid;
            this._invokeService.serviceInvocation(APP_UI_CONFIG.master.categories.update, '', Util.clone(this.rec))
              .pipe(takeUntil(this.$destroyed))
              .subscribe({
                next: response => {
                  if (response.code == HTTP_STATUS.SUCCESS) {
                    this._commonService.success(`Category updated successfully`);
                    this.close(true);
                  } else if (response.code == 400) {
                    this._commonService.error(response.message);
                  }
                  else {
                    this._commonService.error('Failed to update the category details');
                  }
                }, error: error => {
                  this._commonService.handleError(error);
                }
              })
          }
        })
    } else {
      this._commonService.confirm(`Are you sure you want to create a category`, this.rec.category)
        .pipe(takeUntil(this.$destroyed))
        .subscribe(response => {
          if (response) {
            this._invokeService.serviceInvocation(APP_UI_CONFIG.master.categories.create, '', Util.clone(this.rec))
              .pipe(takeUntil(this.$destroyed))
              .subscribe({
                next: response => {
                  if (response.code == HTTP_STATUS.SUCCESS) {
                    this._commonService.success(`New category created successfully`);
                    this.close(true);
                  } else if (response.code == 400) {
                    this._commonService.error(response.message);
                  }
                  else {
                    this._commonService.error('Failed to create the category details');
                  }
                }, error: error => {
                  this._commonService.handleError(error);
                }
              })
          }
        })
    }
  }

  //close modal
  public close(status) {
    this._dialogConfigRef.close(status);
  }

  public ngOnDestroy(): void {
    this.$destroyed.next(null);
    this.$destroyed.complete();
  }
}