

<div class="flex min-w-0 flex-auto flex-col ">
    <div class=" relative overflow-hidden  px-4 pb-28 pt-8  sm:px-16 sm:pb-48 sm:pt-20"
        style="color:white;background-image: linear-gradient(90deg, #265DAB 12.65%, #2B75BC 30.36%, #3F8CC6 48.58%)">
        <div class="relative z-10 flex flex-col items-center">
        </div>
    </div>
    <div class="flex flex-col px-6 pb-6 sm:px-10 sm:pb-10">
        <div class="-mt-16 flex flex-row w-full gap-y-8 sm:-mt-24 md:gap-x-1 md:gap-y-0">
            <div class="bg-card relative flex flex-col overflow-hidden  shadow transition-shadow duration-150 ease-in-out hover:shadow-lg"
                style=" min-width:200px;min-height:200px; width:200px;height: 200px;border-radius: 50% !important;">
                <div class="tps-user-profile-photo">
                    <img src="assets/images/avatars/profile_avatar.png" alt="user icon"
                        style="width:200px;height: 200px;" />
                </div>
            </div>
            <div class="relative z-10 flex flex-col " style="margin-top: 7rem;margin-left:2rem">
                <div class="mt-1 text-4xl font-extrabold leading-tight tracking-tight sm:text-7xl">
                    {{rec.fullName}}</div>

                <h2 class="text-xl font-semibold"><p-chip label="{{rec.role}}" icon="pi pi-user" /></h2>
            </div>

        </div>
        <div class="mt-4">

            <p-tabView styleClass="tabview-custom" (onChange)="onChangeTab($event)">
                <p-tabPanel>
                    <ng-template pTemplate="header">
                        <div class="flex align-items-center gap-2">
                            <p-button icon="pi pi-user" [rounded]="true" severity="info" [outlined]="true" />
                            <span class="font-bold white-space-nowrap m-0">
                                Personal Information
                            </span>
                        </div>
                    </ng-template>
                    <div class="col-md-5">
                        <div class="w-full">
                            <tps-form class="w-full" [formGroup]="userProfileForm" [fields]="fields" [status]="status"
                                [layout]="'column'"></tps-form>
                        </div>
                    </div>
                </p-tabPanel>
                <p-tabPanel header="header">
                    <ng-template pTemplate="header">
                        <div class="flex align-items-center gap-2">
                            <p-button icon="pi pi-lock" [rounded]="true" severity="info" [outlined]="true" />
                            <span class="font-bold white-space-nowrap m-0">
                                Security
                            </span>
                        </div>
                    </ng-template>
                    <div class="col-md-5">
                        <div class="w-full">
                            <tps-form class="w-full" [formGroup]="userProfileForm" [fields]="fields" [status]="status"
                                [layout]="'column'"></tps-form>
                        </div>
                    </div>


                </p-tabPanel>
                <p-tabPanel header="header">
                    <ng-template pTemplate="header">
                        <div class="flex align-items-center gap-2">
                            <p-button icon="pi pi-phone" [rounded]="true" severity="info" [outlined]="true" />
                            <span class="font-bold white-space-nowrap m-0">
                                Contact Details
                            </span>
                        </div>
                    </ng-template>
                    <div class="col-md-5">
                        <div class="w-full">
                            <tps-form class="w-full" [formGroup]="userProfileForm" [fields]="fields" [status]="status"
                                [layout]="'column'"></tps-form>
                        </div>
                    </div>



                </p-tabPanel>
                <p-tabPanel header="header">
                    <ng-template pTemplate="header">
                        <div class="flex align-items-center gap-2">
                            <p-button icon="pi pi-map-marker" [rounded]="true" severity="info" [outlined]="true" />
                            <span class="font-bold white-space-nowrap m-0">
                                Communication Address
                            </span>
                        </div>
                    </ng-template>
                    <div class="col-md-5">
                        <div class="w-full">
                            <tps-form class="w-full" [formGroup]="userProfileForm" [fields]="fields" [status]="status"
                                [layout]="'column'"></tps-form>
                        </div>
                    </div>
                </p-tabPanel>
                <p-tabPanel header="header">
                    <ng-template pTemplate="header">
                        <div class="flex align-items-center gap-2">
                            <p-button icon="pi pi-shield" [rounded]="true" severity="info" [outlined]="true" />
                            <span class="font-bold white-space-nowrap m-0">
                                Multi Factor Authentication(MFA)
                            </span>
                        </div>
                    </ng-template>
                    <div class="col-md-5">
                        <div class="w-full">
                            <tps-form class="w-full" [formGroup]="userProfileForm" [fields]="fields" [status]="status"
                                [layout]="'column'"></tps-form>
                        </div>
                    </div>
                </p-tabPanel>
            </p-tabView>
            <div class="modal-footer">
                <div class="w-full flex flex-row justify-end gap-6">
                    <tps-secondary-button [buttonName]="'Cancel'" (onClick)="close()"></tps-secondary-button>
                    <tps-primary-button *ngIf="status != TABLE_ACTION_TYPES.VIEW" [buttonName]="'Update'"
                        [isDisabled]="userProfileForm.invalid" (onClick)="onSubmit()"></tps-primary-button>
                </div>
            </div>
        </div>
    </div>
</div>