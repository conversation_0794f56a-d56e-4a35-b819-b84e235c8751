import { CommonModule, NgF<PERSON>, NgIf } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { FormGroup, FormGroupDirective, ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'tps-error',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, NgIf],
  templateUrl: './tps-error.component.html'
})
export class TpsErrorComponent {
  @Input() field: any;
  @Input() fieldName: any;
  @Input() label: string;
  @Input() formName: FormGroup;
  constructor() { }

  ngOnInit(): void {
  }
}
