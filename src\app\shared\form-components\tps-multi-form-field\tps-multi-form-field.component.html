<form [formGroup]="formName" autocomplete="off">
    <div class="flex flex-col gap-2 mb-4">
        <div class="flex flex-row items-center gap-2">
            <div class="tps-dynamic-form-sub-title my-2">{{field.label}}
                <span *ngIf="field?.rules?.required && !formName.controls[field.fieldName].disabled"
                    class="requiredField">*</span>
            </div>
            <i *ngIf="field?.tooltip" [pTooltip]="field?.tooltip" tooltipPosition="bottom"
                class="cursor-pointer pi pi-question-circle helping_text_icon"></i>
        </div>
        <div class="row">
            <div class="col-md-10 offset-md-2">
                <form class="w-full flex flex-column gap-1" [formGroup]="dynamicForm" autocomplete="off">
                    <div formArrayName="dynamicFormArray" style="margin-left:14px">
                        <ng-container *ngIf="getDynamicFormControls().controls.length>0">

                        </ng-container>
                        <div class="w-full flex flex-col gap-4 items-center"
                            *ngFor="let control of getDynamicFormControls().controls; let i=index" [formGroupName]="i">
                            <div class="flex flex-row gap-6 w-full  min-w-0">
                                <ng-container *ngFor="let field of fields" [ngSwitch]="field.type">
                                    <ng-container *ngIf="field.show">
                                        <tps-text class="col-sm-2 col-2" *ngSwitchCase="FORM_CONTROL_TYPES.TEXT"
                                            [field]="field" [formName]="control" [showLabel]="i==0"
                                            [layout]="'column'"></tps-text>
                                    </ng-container>
                                    <ng-container *ngIf="field.show">
                                        <tps-single-select class="col-sm-2 col-2"
                                            *ngSwitchCase="FORM_CONTROL_TYPES.SINGLE_SELECT" [field]="field"
                                            [formName]="control" [showLabel]="i==0"
                                            [layout]="'column'"></tps-single-select>
                                    </ng-container>
                                </ng-container>
                                <div class="flex flex-row gap-6 items-center" *ngIf="status != TABLE_ACTION_TYPES.VIEW">

                                    <p-button *ngIf="i>=0 && status == TABLE_ACTION_TYPES.CREATE" icon="pi pi-plus"
                                        [rounded]="true" severity="info" [outlined]="true" pTooltip="Add More..."
                                        tooltipPosition="bottom" (click)="addNewRow()" />

                                    <p-button
                                        *ngIf="i>(getDynamicFormControls().controls.length-2) && status != TABLE_ACTION_TYPES.CREATE"
                                        icon="pi pi-plus" [rounded]="true" severity="info" [outlined]="true"
                                        pTooltip="Add More..." tooltipPosition="bottom" (click)="addNewRow()" />

                                    <p-button *ngIf="i>0 && status == TABLE_ACTION_TYPES.CREATE" icon="pi pi-times"
                                        [rounded]="true" severity="danger" [outlined]="true" pTooltip="Delete"
                                        tooltipPosition="bottom" (click)="deleteRow(i)" />

                                    <p-button *ngIf="status != TABLE_ACTION_TYPES.CREATE" icon="pi pi-times"
                                        [rounded]="true" severity="danger" [outlined]="true" pTooltip="Delete"
                                        tooltipPosition="bottom" (click)="deleteRow(i)" />
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</form>