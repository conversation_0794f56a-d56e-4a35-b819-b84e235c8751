// Primeng ng override
@use "./primeng-override.scss";
//icons
@use "primeicons/primeicons.css";
// Typography
@use "./tapas_typography.scss";


@font-face {
    font-family: 'Source Sans Pro';
    src: url('/assets/fonts/inter/Source_Sans_Pro/SourceSansPro-Regular.ttf') format('truetype');
}

//Import prime icons
:root {
    font-family: "Source Sans Pro" !important;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11" !important;
    font-variation-settings: normal !important;
    --font-family: "Source Sans Pro" !important;
    --font-feature-settings: "cv02", "cv03", "cv04", "cv11" !important;
    --text-color: #334155 !important;
    --text-color-secondary: #64748b !important;
    --primary-color: #3B82F6 !important;
    --primary-color-text: #ffffff !important;
    --surface-0: #ffffff !important;
    --surface-50: #f8fafc !important;
    --surface-100: #f1f5f9 !important;
    --surface-200: #e2e8f0 !important;
    --surface-300: #cbd5e1 !important;
    --surface-400: #94a3b8 !important;
    --surface-500: #64748b !important;
    --surface-600: #475569 !important;
    --surface-700: #334155 !important;
    --surface-800: #1e293b !important;
    --surface-900: #0f172a !important;
    --surface-950: #020617 !important;
    --gray-0: #ffffff !important;
    --gray-50: #f8fafc !important;
    --gray-100: #f1f5f9 !important;
    --gray-200: #e2e8f0 !important;
    --gray-300: #cbd5e1 !important;
    --gray-400: #94a3b8 !important;
    --gray-500: #64748b !important;
    --gray-600: #475569 !important;
    --gray-700: #334155 !important;
    --gray-800: #1e293b !important;
    --gray-900: #0f172a !important;
    --gray-950: #020617 !important;
    --content-padding: 1.125rem !important;
    --inline-spacing: 0.5rem !important;
    --border-radius: 6px !important;
    --surface-ground: #f8fafc !important;
    --surface-section: #ffffff !important;
    --surface-card: #ffffff !important;
    --surface-overlay: #ffffff !important;
    --surface-border: #e2e8f0 !important;
    --surface-hover: #f1f5f9 !important;
    --focus-ring: none !important;
    --maskbg: rgba(0, 0, 0, 0.4) !important;
    --highlight-bg: #EFF6FF !important;
    --highlight-text-color: #1D4ED8 !important;
    --p-anchor-gutter: 2px !important;
    color-scheme: light !important;
}

:root {
    --p-focus-ring-color: var(--primary-color) !important;
}

:root {
    --blue-50: #f5f9ff !important;
    --blue-100: #d0e1fd !important;
    --blue-200: #abc9fb !important;
    --blue-300: #85b2f9 !important;
    --blue-400: #609af8 !important;
    --blue-500: #3b82f6 !important;
    --blue-600: #326fd1 !important;
    --blue-700: #295bac !important;
    --blue-800: #204887 !important;
    --blue-900: #183462 !important;
    --green-50: #f4fcf7 !important;
    --green-100: #caf1d8 !important;
    --green-200: #a0e6ba !important;
    --green-300: #76db9b !important;
    --green-400: #4cd07d !important;
    --green-500: #22c55e !important;
    --green-600: #1da750 !important;
    --green-700: #188a42 !important;
    --green-800: #136c34 !important;
    --green-900: #0e4f26 !important;
    --yellow-50: #fefbf3 !important;
    --yellow-100: #faedc4 !important;
    --yellow-200: #f6de95 !important;
    --yellow-300: #f2d066 !important;
    --yellow-400: #eec137 !important;
    --yellow-500: #eab308 !important;
    --yellow-600: #c79807 !important;
    --yellow-700: #a47d06 !important;
    --yellow-800: #816204 !important;
    --yellow-900: #5e4803 !important;
    --cyan-50: #f3fbfd !important;
    --cyan-100: #c3edf5 !important;
    --cyan-200: #94e0ed !important;
    --cyan-300: #65d2e4 !important;
    --cyan-400: #35c4dc !important;
    --cyan-500: #06b6d4 !important;
    --cyan-600: #059bb4 !important;
    --cyan-700: #047f94 !important;
    --cyan-800: #036475 !important;
    --cyan-900: #024955 !important;
    --pink-50: #fef6fa !important;
    --pink-100: #fad3e7 !important;
    --pink-200: #f7b0d3 !important;
    --pink-300: #f38ec0 !important;
    --pink-400: #f06bac !important;
    --pink-500: #ec4899 !important;
    --pink-600: #c93d82 !important;
    --pink-700: #a5326b !important;
    --pink-800: #822854 !important;
    --pink-900: #5e1d3d !important;
    --indigo-50: #f7f7fe !important;
    --indigo-100: #dadafc !important;
    --indigo-200: #bcbdf9 !important;
    --indigo-300: #9ea0f6 !important;
    --indigo-400: #8183f4 !important;
    --indigo-500: #6366f1 !important;
    --indigo-600: #5457cd !important;
    --indigo-700: #4547a9 !important;
    --indigo-800: #363885 !important;
    --indigo-900: #282960 !important;
    --teal-50: #f3fbfb !important;
    --teal-100: #c7eeea !important;
    --teal-200: #9ae0d9 !important;
    --teal-300: #6dd3c8 !important;
    --teal-400: #41c5b7 !important;
    --teal-500: #14b8a6 !important;
    --teal-600: #119c8d !important;
    --teal-700: #0e8174 !important;
    --teal-800: #0b655b !important;
    --teal-900: #084a42 !important;
    --orange-50: #fff8f3 !important;
    --orange-100: #feddc7 !important;
    --orange-200: #fcc39b !important;
    --orange-300: #fba86f !important;
    --orange-400: #fa8e42 !important;
    --orange-500: #f97316 !important;
    --orange-600: #d46213 !important;
    --orange-700: #ae510f !important;
    --orange-800: #893f0c !important;
    --orange-900: #642e09 !important;
    --bluegray-50: #f7f8f9 !important;
    --bluegray-100: #dadee3 !important;
    --bluegray-200: #bcc3cd !important;
    --bluegray-300: #9fa9b7 !important;
    --bluegray-400: #818ea1 !important;
    --bluegray-500: #64748b !important;
    --bluegray-600: #556376 !important;
    --bluegray-700: #465161 !important;
    --bluegray-800: #37404c !important;
    --bluegray-900: #282e38 !important;
    --purple-50: #fbf7ff !important;
    --purple-100: #ead6fd !important;
    --purple-200: #dab6fc !important;
    --purple-300: #c996fa !important;
    --purple-400: #b975f9 !important;
    --purple-500: #a855f7 !important;
    --purple-600: #8f48d2 !important;
    --purple-700: #763cad !important;
    --purple-800: #5c2f88 !important;
    --purple-900: #432263 !important;
    --red-50: #fff5f5 !important;
    --red-100: #ffd0ce !important;
    --red-200: #ffaca7 !important;
    --red-300: #ff8780 !important;
    --red-400: #ff6259 !important;
    --red-500: #ff3d32 !important;
    --red-600: #d9342b !important;
    --red-700: #b32b23 !important;
    --red-800: #8c221c !important;
    --red-900: #661814 !important;
    --primary-50: #f5f9ff !important;
    --primary-100: #d0e1fd !important;
    --primary-200: #abc9fb !important;
    --primary-300: #85b2f9 !important;
    --primary-400: #609af8 !important;
    --primary-500: #3b82f6 !important;
    --primary-600: #326fd1 !important;
    --primary-700: #295bac !important;
    --primary-800: #204887 !important;
    --primary-900: #183462 !important;
}



* {
    font-family: 'Source Sans Pro';
}

*:not(i):not(.fa):not(.ag-icon):not(.pi):not(.material-icons):not(.mat-icon) {
    font-family: 'Source Sans Pro' !important;
}

//menu css
.tps-header-wrapper {
    height: 64px !important;
    width: 100%;
    background: #fff !important;
    box-shadow: 0 1px 20px 0 rgba(0, 0, 0, .08);
    position: sticky !important;
    top: 0;
    z-index: 999
}

//label
label {
    font-family: Source Sans Pro !important;
    font-size: 16px !important;
    font-weight: 400 !important;
    line-height: 20px !important;
    letter-spacing: 0em !important;
    text-align: left !important;
    color: #384353 !important;
    opacity: 1 !important;
    white-space: break-spaces !important;
}

//buttons

.tps-button-wrapper {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 32px;
    padding: 10px 24px;
    font-family: 'Source Sans Pro';
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    line-height: 14px;
    text-align: center;
    color: #FFFFFF;
    width: auto;
    background-color: #517EBC;
    border-radius: 6px;
    gap: 8px;
    justify-content: center;

    mat-icon {
        color: white;
        width: 20px;
        height: 20px;
        font-size: 20px;
    }
}

.tps-button-wrapper:hover {
    background: #4075be;
}

.tps-button-wrapper.button_disabled {
    background-color: #A8AFBD;
    color: white !important;
    cursor: not-allowed;
}


.ag-cell {
    font-weight: 400;
    font-size: 14px;
    line-height: 15px;
    color: #0F1925;
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
}

.no-data_wrapper {
    width: 100%;
    background: white;
    // box-shadow: 0px 4px 4px rgb(123 134 153 / 30%);
    border-radius: 0px 0px 11px 11px;
    border: 1px solid #E7EAF0;
    border-top: 0px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.no-data-img {
    width: 100px;
    height: 100px;
}

.no-data-label {
    font-weight: 400;
    font-size: 18px;
    line-height: 23px;
    color: #7B8699
}

.tps-body-padding {
    padding: 8px 32px 8px 32px;
}

.tps-main-body-wrapper {}

.user_menu_menu_item {
    padding: 1rem 0.5rem;
}

.user_menu_menu_item:hover {
    background-color: #f8f9fa;
    border-radius: 2px;
}



.error-message {
    color: red;
}

.helping_text_icon {
    font-size: 1rem;
    color: #517EBC;
    margin-top: 3px;
}

.form-row {
    margin-bottom: 0.75rem;
}

.tps-dynamic-form-sub-title {
    font-family: Source Sans Pro;
    color: #7B8699;
    font-size: 18px;
    font-weight: 600;
    line-height: 18px;
    letter-spacing: 0em;
    text-align: left;
}

.dynamic-form-wrapper {
    display: flex;
    flex-wrap: wrap;
}

.table-heading {
    font-family: 'Source Sans Pro';
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 30px;
    color: #0F1925;
}

.required_fields_message {
    color: red;
    font-size: 16px !important;
}

.requiredField {
    color: red;
    font-size: 20px !important;
}


.tps-full-screen-header {
    width: 100%;
    padding: 0.35rem 1rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tps-full-screen-content {
    width: 100%;
    height: 80vh;
    padding: 0.75rem 1.5rem;
    display: flex;
    flex-direction: column;
    overflow-y: scroll;
}

.tps-full-screen-footer {
    width: 100%;
    padding: 0.35rem 1rem;
    border-top: 1px solid #e2e8f0;
}

.tps-stepper-screen-content {
    width: 100%;
    height: 70vh;
    padding: 0.75rem 1.5rem;
    display: flex;
    flex-direction: column;
    overflow-y: scroll;
}

.tps-stepper-screen-footer {
    width: 100%;
    padding: 0.5rem 0px;
    border-top: 1px solid #e2e8f0;
}



.card-title {
    font-size: 16px;
    font-weight: 700;
    line-height: 22px;
    letter-spacing: 0em;
    text-align: left;
    color: #FFFFFF;
}

.card-value {
    font-size: 40px;
    font-weight: 600;
    line-height: 55px;
    letter-spacing: 0em;
    text-align: left;
    color: #FFFFFF;
}

.blue-card {
    background: conic-gradient(from 22.75deg at 66.04% 84.5%, #5FA7D3 0deg, #2973A0 360deg);
    box-shadow: 0px 13px 20px 0px #E7EAF080;
    border-radius: 10px;
    width: auto;
}

.green-card {
    background: conic-gradient(from 22.75deg at 66.04% 84.5%, #69CEA0 0deg, #24A268 360deg);
    box-shadow: 0px 13px 20px 0px #E7EAF080;
    border-radius: 10px;
    width: auto;
}

.red-card {
    background: conic-gradient(from 22.75deg at 66.04% 84.5%, #69CEA0 0deg, #24A268 360deg);
    box-shadow: 0px 13px 20px 0px #E7EAF080;
    border-radius: 10px;
    width: auto;
}

.orange-card {
    background: conic-gradient(from 22.75deg at 66.04% 84.5%, #FFD4A2 0deg, #FCA33B 360deg);
    box-shadow: 0px 13px 20px 0px #E7EAF080;
    border-radius: 10px;
    width: auto;
}

.yellow-orange-card {
    background: conic-gradient(from 22.75deg at 66.04% 84.5%, #F0B2AE 0deg, #ED736B 360deg);
    box-shadow: 0px 13px 20px 0px #E7EAF080;
    border-radius: 10px;
    width: auto;
}

.brown-card {
    background: conic-gradient(from 22.75deg at 66.04% 84.5%, #EC8F5E 0deg, #934922 360deg);
    box-shadow: 0px 13px 20px 0px #E7EAF080;
    border-radius: 10px;
    width: auto;
}

.black-card {
    background: conic-gradient(from 22.75deg at 66.04% 84.5%, #7A8EAC 0deg, #384353 360deg);
    box-shadow: 0px 13px 20px 0px #E7EAF080;
    border-radius: 10px;
    width: auto;
}


.dark-red-card {
    background: conic-gradient(from 22.75deg at 66.04% 84.5%, #E2697F 0deg, #892235 360deg);
    box-shadow: 0px 13px 20px 0px #E7EAF080;
    border-radius: 10px;
    width: auto;
}


.dark-blue-card {
    background: conic-gradient(from 22.75deg at 66.04% 84.5%, #788BD8 0deg, #1939B7 360deg);
    box-shadow: 0px 13px 20px 0px #E7EAF080;
    border-radius: 10px;
    width: auto;
}


.mat-mdc-menu-panel {
    box-shadow: rgba(17, 17, 26, 0.1) 0px 4px 16px, rgba(17, 17, 26, 0.1) 0px 8px 24px, rgba(17, 17, 26, 0.1) 0px 16px 56px !important;
}

/* width */
::-webkit-scrollbar {
    width: 8px;
    border-radius: 8px;
}

//   /* Track */
//   ::-webkit-scrollbar-track {
//     background: #f1f1f1; 
//   }

//   /* Handle */
//   ::-webkit-scrollbar-thumb {
//     background: #888; 
//   }

//   /* Handle on hover */
//   ::-webkit-scrollbar-thumb:hover {
//     background: #555; 
//   }


.card-title {
    font-size: 16px;
    font-weight: 700;
    line-height: 22px;
    letter-spacing: 0em;
    text-align: left;
    color: #FFFFFF;
}

.card-value {
    font-size: 40px;
    font-weight: 600;
    line-height: 55px;
    letter-spacing: 0em;
    text-align: left;
    color: #FFFFFF;
}

.visibilityHidden {
    visibility: hidden;
}

.unread-content {
    background-color: #FFFAEF;
}

.read-content {
    background-color: rgba(241, 245, 249, 1) !important;
}


.unread-dart-circle {
    height: 0.6rem;
    width: 0.6rem;
    border-radius: 50%;
    background-color: #f97316;
}

.custom-scrolltop {
    display: flex;
    flex-direction: row-reverse;
    margin-top: 0.5rem;
}

//Media Queries

/* Custom, iPhone Retina */
@media only screen and (max-width : 320px) {

    .action_button_wrapper,
    button,
    tps-primary-button,
    tps-secondary-button {
        width: 100%;
    }
}

/* Extra Small Devices, Phones */
@media only screen and (max-width : 480px) {

    .action_button_wrapper,
    button,
    tps-primary-button,
    tps-secondary-button {
        width: 100%;
    }
}

/* Small Devices, Tablets */
@media only screen and (min-width : 768px) {

    .action_button_wrapper,
    button,
    tps-primary-button,
    tps-secondary-button {
        width: auto;
    }
}

/* Medium Devices, Desktops */
@media only screen and (min-width : 992px) {}

/* Large Devices, Wide Screens */
@media only screen and (min-width : 1200px) {}



.border-t {
    border-top: 1px solid #E7EAF0;
}

.border-b {
    border-bottom: 1px solid #E7EAF0;
}

.border-l {
    border-left: 1px solid #E7EAF0;
}

.border-r {
    border-right: 1px solid #E7EAF0;
}

.spinner-text{
    opacity:1 !important;
    color:white !important;
}