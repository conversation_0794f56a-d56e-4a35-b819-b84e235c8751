<form [formGroup]="formName" autocomplete="off">
    <div class="row mb-4">
        <div class="col-md-5">
            <div class="flex flex-row items-center gap-2">
                <label for="{{field.label}}">{{field.label}}
                    <span *ngIf="field?.rules?.required && !formName.controls[field.fieldName].disabled"
                        class="requiredField">*</span>
                </label>
                <i *ngIf="field?.tooltip" [pTooltip]="field?.tooltip" tooltipPosition="bottom"
                    class="cursor-pointer pi pi-question-circle helping_text_icon"></i>
            </div>
        </div>
        <div class="col-md-7">
            <div class="w-full flex flex-column gap-1">
                <p-inputnumber class="w-full tps-input" [useGrouping]="false" [name]="field.fieldName"
                    [formControlName]="field.fieldName" mode="decimal" id="{{field.label}}"
                    [minFractionDigits]="field?.rules?.minFractionDigits"
                    [maxFractionDigits]="field?.rules?.maxFractionDigits" [required]="field?.rules?.required"
                    [min]="field?.rules?.min" [max]="field?.rules?.max" [minlength]="field?.rules?.minLength"
                    [maxlength]="field?.rules?.maxLength" [placeholder]="field.placeholder" [prefix]="field.prefix"
                    [suffix]="field.suffix" [class.ng-invalid]="formName.controls[field.fieldName].invalid"
                    [class.ng-dirty]="(formName.controls[field.fieldName].dirty || formName.controls[field.fieldName].touched)"
                    (onInput)="field.onChange(field);onInputChange($event)" />

                <small *ngIf="field?.hint">{{field?.hint}}</small>
                <tps-error [fieldName]="field.fieldName" [label]="field.label" [formName]="formName"></tps-error>
            </div>
        </div>
    </div>
</form>