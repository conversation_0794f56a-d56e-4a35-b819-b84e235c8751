import { CommonModule, NgF<PERSON>, NgIf } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormGroup, FormGroupDirective, ReactiveFormsModule } from '@angular/forms';
import { InputSwitchModule } from 'primeng/inputswitch';
import { TooltipModule } from 'primeng/tooltip';

import { FORM_CONTROL_TYPES } from '../../constants/tps-enum-constants';
import { TpsErrorComponent } from '../tps-error/tps-error.component';



class tpsToggleSwitch {
  type: string
  fieldName: string
  tooltip?: string
  hint?: string
  value: any
  label: string
  placeholder: string
  show: boolean
  onChange: (any) => void
  rules: {
    required?: boolean,
  }
}

@Component({
  selector: 'tps-toggle-switch',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, NgIf, TpsErrorComponent, InputSwitchModule, TooltipModule],
  templateUrl: './tps-toggle-switch.component.html'
})
export class TpsToggleSwitchComponent implements OnInit {
  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  @Input() field: tpsToggleSwitch;
  @Input() formName: FormGroup;
  @Output() onChange: EventEmitter<any> = new EventEmitter();

  constructor(private formGroupDirective: FormGroupDirective) {
    this.formName = formGroupDirective.control;
  }

  public ngOnInit(): void {
  }

}
