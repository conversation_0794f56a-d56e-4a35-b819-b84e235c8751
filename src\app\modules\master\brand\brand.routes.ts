import { Routes } from '@angular/router';
import { permissionGuard } from 'app/core/auth/guards/permission.guard';

import { BrandComponent } from './brand.component';

export default [
    {
        path: '',
        component: BrandComponent,
        canActivate: [permissionGuard],
        data: { permission: "BRAND_MASTER" },
    },
    { path: 'not-found', loadChildren: () => import('app/modules/page-not-found/page-not-found.routes') },
    { path: '**', pathMatch: 'full', redirectTo: 'not-found' },
] as Routes;