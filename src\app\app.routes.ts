import { Route } from '@angular/router';
import { AuthGuard } from 'app/core/auth/guards/auth.guard';
import { NoAuthGuard } from 'app/core/auth/guards/noAuth.guard';
import { LayoutComponent } from 'app/layout/layout.component';



// @formatter:off
/* eslint-disable max-len */
/* eslint-disable @typescript-eslint/explicit-function-return-type */
export const appRoutes: Route[] = [
    // Redirect modern path to '/example'
    { path: '', pathMatch: 'full', redirectTo: 'sign-in' },

    // Auth routes for guests
    {
        path: '',
        canActivate: [NoAuthGuard],
        component: LayoutComponent,
        data: { layout: 'empty' },
        children: [
            { path: 'forgot-password', loadChildren: () => import('app/modules/auth/forgot-password/forgot-password.routes') },
            { path: 'reset-password', loadChildren: () => import('app/modules/auth/reset-password/reset-password.routes') },
            { path: 'sign-in', loadChildren: () => import('app/modules/auth/sign-in/sign-in.routes') },
        ]
    },
    {
        path: '',
        canActivate: [AuthGuard],
        canActivateChild: [AuthGuard],
        component: LayoutComponent,
        data: {
            layout: 'empty'
        },
        children: [
            { path: 'sign-out', loadChildren: () => import('app/modules/auth/sign-out/sign-out.routes') },
        ]
    },


    // Auth routes for authenticated users
    {
        path: '',
        canActivate: [AuthGuard],
        component: LayoutComponent,
        data: { layout: 'modern' },
        children: [
            { path: 'user-profile', loadChildren: () => import('app/modules/user-profile/user-profile.routes') },
            { path: 'home', loadChildren: () => import('app/modules/landing/home.routes') },
            { path: 'document', loadChildren: () => import('app/modules/document/document.routes') },
            { path: 'admin', loadChildren: () => import('app/modules/admin/admin.routes') },
            { path: 'master', loadChildren: () => import('app/modules/master/master.routes') },
            { path: 'help-center', loadChildren: () => import('app/modules/help-center/help-center.routes') },
            { path: 'not-found', loadChildren: () => import('app/modules/page-not-found/page-not-found.routes') },

        ]
    },

    // Wildcard route
    { path: '**', pathMatch: 'full', redirectTo: 'sign-in' }
];
