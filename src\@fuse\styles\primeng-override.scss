:root {
    --fuse-text-default: #334155;
    --fuse-text-disabled: #94A3B8;
    --fuse-bg-default: #ffffff;
}

.p-component {
    font-family: 'Source Sans Pro' !important;
}

.p-inputtext,
.p-select,
.p-password,
.p-multiselect,
.p-autocomplete,
.p-inputwrapper,
.p-datepicker,
.p-inputnumber-input,
.p-inputnumber,
textarea {
    width: 100% !important;
}

.p-inputtext:not(textarea),
.p-select,
.p-password,
.p-multiselect,
.p-autocomplete,
.p-inputwrapper,
.p-datepicker,
.p-inputnumber-input {
    min-height: 32px !important;
    max-height: 32px !important;
}

.p-inputtext:disabled,
.p-password-input.p-disabled,
.p-select.p-disabled,
.p-multiselect.p-disabled,
.p-autocomplete.p-disabled {
    // background: aliceblue !important;
    color: black !important;
}

.p-select.p-disabled .p-select-label,
.p-textarea:disabled {
    color: black !important;
}

.p-message-wrapper {
    padding: 0px 0.5rem;
    display: flex;
    flex-direction: row;
    align-items: center;

    .p-message-detail {
        font-size: 16px;
    }

    .p-message-icon {
        margin-top: 8px !important;
    }
}

.p-dialog {
    padding-bottom: 0.5rem !important;
    background-color: #FFFFFF;
}

.p-dialog .p-dialog-header {
    padding: 1rem;
}

.p-dialog-header {
    border-bottom: 1px solid #e2e8f0;
}

.modal-header {
    width: 100%;
}

.p-dialog .p-dialog-content {
    padding: 0rem !important;
}

.full-page-content {
    width: 100%;
    padding: 0.5rem 1.5rem;
}

.modal-content {
    width: 100%;
    max-height: 70vh;
    overflow-y: auto;
    overflow-x: hidden !important;
    padding: 1.5rem;
}

.modal-footer {
    width: 100%;
    padding: 1rem 1.5rem;
    border-top: 1px solid #e2e8f0;
}

.p-dialog-title {
    font-family: 'Source Sans Pro' !important;
    font-style: normal !important;
    font-weight: 600 !important;
    color: #0F1925 !important;
    font-size: 1.25rem !important;
    padding-left: 8px;
}

// .p-toast {
//     z-index: 9999999;
// }

// .p-toast-detail {
//     font-size: 16px !important
// }

// .p-toast-summary {
//     font-size: 20px !important;
// }

// .p-toast .p-toast-message.p-toast-message-error {
//     background: rgba(255, 231, 230, 1) !important
// }

// .p-toast .p-toast-message.p-toast-message-success {
//     background: rgba(240, 253, 244, 1) !important
// }

// .p-toast .p-toast-message.p-toast-message-success {
//     background: rgba(234, 179, 8, 1) !important
// }

// .p-toast .p-toast-message.p-toast-message-success {
//     background: rgba(239, 246, 255, 1) !important
// }

// .p-component:disabled,
// .p-disabled {
//     opacity: 1 !important;
//     background: #f2f2f2;
//     border: 1px solid #D0D7E2;
//     color: #0F1925 !important;
//     font-weight: 500;
//     cursor: not-allowed;
// }


.p-stepper .p-stepper-header .p-stepper-number {
    align-items: center;
    background-color: #fff;
    border: 3px solid #e0e0e0;
    border-radius: 50%;
    color: #999;
    display: flex;
    justify-content: center;
}

.p-stepper .p-stepper-header .p-stepper-title {
    color: #999;
}

.p-stepper .p-stepper-header.p-highlight .p-stepper-number {
    border: 2px solid #517EBC;
    color: #517EBC;
}

.p-stepper .p-stepper-header.p-highlight .p-stepper-title {
    color: #517EBC;
}

.p-image-action {
    color: white;
}


.p-carousel,
.p-carousel-content {
    width: 100%;
}

.p-carousel-items-content {
    display: inline-block;
}

.p-carousel-items-container {
    display: inline-flex;
}

.p-image-preview-indicator {
    width: 100% !important;
}

.p-button.p-button-icon-only {
    width: 2rem !important;
    padding: 0.5rem 0 !important;
}

.p-button.p-button-icon-only .p-button-icon-left,
.p-button.p-button-icon-only .p-button-icon-right {
    margin: 0 !important;
}

.p-button.p-button-icon-only.p-button-rounded {
    border-radius: 50% !important;
    height: 2rem !important;
}

//Table CSS
.p-datatable-table {
    border: 1px solid #e2e8f0;
}

.p-datatable-thead {
    font-family: "Source Sans Pro";

    tr {
        max-height: 42px !important;
        background: #f8f9fd !important;
        border-left: 0px;
        border-right: 0px !important;
        border-top: 0px !important;

        th {
            font-family: "Source Sans Pro";
            font-weight: 600 !important;
            font-size: 16px !important;
            color: #7B8699 !important;
            white-space: nowrap;
            /* Prevents the text from wrapping */
            overflow: hidden;
            /* Hides overflowed content */
            text-overflow: ellipsis;
            background: #f8f9fd !important;
        }
    }

}

.p-datatable-tbody {
    font-family: "Source Sans Pro";

    tr.tps-table-row {
        max-height: 40px !important;
        border-color: inherit;
        border-style: solid;
        border-width: 1px !important;

        td {
            font-family: "Source Sans Pro";
            font-weight: 400 !important;
            font-size: 14px !important;
            color: #0F1925 !important;
            white-space: nowrap;
            /* Prevents the text from wrapping */
            overflow: hidden;
            /* Hides overflowed content */
            text-overflow: ellipsis;
        }
    }
}

.p-datatable-gridlines .p-datatable-tbody>tr>td:last-child {
    border-width: 1px 1px 1px 1px !important;
}


//Tag

.p-tag {
    background: #3B82F6 !important;
    color: #ffffff !important;
    font-size: 0.75rem !important;
    font-weight: 700 !important;
    padding: 0.25rem 0.4rem !important;
    border-radius: 6px !important;
}

.p-tag.p-tag-success {
    background-color: #22c55e !important;
    color: #ffffff !important;
}

.p-tag.p-tag-info {
    background-color: #0ea5e9 !important;
    color: #ffffff !important;
}

.p-tag.p-tag-warning {
    background-color: #f97316 !important;
    color: #ffffff !important;
}

.p-tag.p-tag-danger {
    background-color: #ef4444 !important;
    color: #ffffff !important;
}

.p-tag .p-tag-icon {
    margin-right: 0.25rem !important;
    font-size: 0.75rem !important;
}

.p-tag .p-icon {
    width: 0.75rem !important;
    height: 0.75rem !important;
}

.p-tag.p-tag-secondary {
    background-color: #d1d5da !important;
    color: #475569 !important;
}

.p-tag.p-tag-contrast {
    background-color: #020617 !important;
    color: #ffffff !important;
}




.p-toast .p-toast-message {
    backdrop-filter: blur(1.5px) !important;
}

.p-toast .p-toast-message .p-toast-message-content .p-toast-detail {
    font-size: 16px !important;
}

.p-toast .p-toast-message .p-toast-message-content .p-toast-icon-close {
    width: 1.75rem !important;
    height: 1.75rem !important;
    position: relative !important;
}

.p-toast .p-toast-message.p-toast-message-info {
    box-shadow: 0px 4px 8px 0px rgba(59, 130, 246, 0.04) !important;
}

.p-toast .p-toast-message.p-toast-message-info .p-toast-detail {
    color: #334155 !important;
}

.p-toast .p-toast-message.p-toast-message-info .p-toast-icon-close {
    outline-color: #2563eb !important;
}

.p-toast .p-toast-message.p-toast-message-info .p-toast-icon-close:hover {
    background: #dbeafe !important;
}

.p-toast .p-toast-message.p-toast-message-success {
    box-shadow: 0px 4px 8px 0px rgba(34, 197, 94, 0.04) !important;
}

.p-toast .p-toast-message.p-toast-message-success .p-toast-detail {
    color: #334155 !important;
}

.p-toast .p-toast-message.p-toast-message-success .p-toast-icon-close {
    outline-color: #16a34a !important;
}

.p-toast .p-toast-message.p-toast-message-success .p-toast-icon-close:hover {
    background: #dcfce7 !important;
}

.p-toast .p-toast-message.p-toast-message-warn {
    box-shadow: 0px 4px 8px 0px rgba(234, 179, 8, 0.04) !important;
}

.p-toast .p-toast-message.p-toast-message-warn .p-toast-detail {
    color: #334155 !important;
}

.p-toast .p-toast-message.p-toast-message-warn .p-toast-icon-close {
    outline-color: #ca8a04 !important;
}

.p-toast .p-toast-message.p-toast-message-warn .p-toast-icon-close:hover {
    background: #fef9c3 !important;
}

.p-toast .p-toast-message.p-toast-message-error {
    box-shadow: 0px 4px 8px 0px rgba(239, 68, 68, 0.04) !important;
}

.p-toast .p-toast-message.p-toast-message-error .p-toast-detail {
    color: #334155 !important;
}

.p-toast .p-toast-message.p-toast-message-error .p-toast-icon-close {
    outline-color: #dc2626 !important;
}

.p-toast .p-toast-message.p-toast-message-error .p-toast-icon-close:hover {
    background: #fee2e2 !important;
}

.p-toast .p-toast-message.p-toast-message-secondary {
    box-shadow: 0px 4px 8px 0px rgba(74, 85, 103, 0.04) !important;
}

.p-toast .p-toast-message.p-toast-message-secondary .p-toast-icon-close {
    outline-color: #dc2626 !important;
}

.p-toast .p-toast-message.p-toast-message-secondary .p-toast-icon-close:hover {
    background: #e2e8f0 !important;
}

.p-toast .p-toast-message.p-toast-message-contrast {
    box-shadow: 0px 4px 8px 0px rgba(2, 6, 23, 0.04) !important;
}

.p-toast .p-toast-message.p-toast-message-contrast .p-toast-icon-close {
    outline-color: #dc2626 !important;
}

.p-toast .p-toast-message.p-toast-message-contrast .p-toast-icon-close:hover {
    background: #1e293b !important;
}


.p-fileupload .p-fileupload-buttonbar {
    background: #ffffff !important;
    padding: 1.125rem !important;
    border: 1px solid #e2e8f0 !important;
    color: #334155 !important;
    border-bottom: 0 none !important;
    border-top-right-radius: 6px !important;
    border-top-left-radius: 6px !important;
}

.p-fileupload .p-fileupload-buttonbar .p-button {
    margin-right: 0.5rem !important;
}

.p-fileupload .p-fileupload-buttonbar .p-button.p-fileupload-choose.p-focus {
    outline: 1px solid var(--p-focus-ring-color) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
}

.p-fileupload .p-fileupload-content {
    background: #ffffff !important;
    padding: 0 1.125rem 1.125rem 1.125rem !important;
    border: 1px solid #e2e8f0 !important;
    color: #334155 !important;
    border-bottom-right-radius: 6px !important;
    border-bottom-left-radius: 6px !important;
}

.p-fileupload .p-fileupload-content.p-fileupload-highlight {
    border-color: 1px dashed #3B82F6 !important;
    border-style: dashed !important;
    background-color: #EFF6FF !important;
}

.p-fileupload .p-progressbar {
    height: 3px !important;
}

.p-fileupload .p-fileupload-row>div {
    padding: 0.75rem 1rem !important;
}

.p-fileupload.p-fileupload-advanced .p-message {
    margin-top: 0 !important;
}

.p-fileupload-choose:not(.p-disabled):hover {
    background: #2563eb !important;
    color: #ffffff !important;
    border-color: #2563eb !important;
}

.p-fileupload-choose:not(.p-disabled):active {
    background: #1D4ED8 !important;
    color: #ffffff !important;
    border-color: #1D4ED8 !important;
}

.p-fileupload-file-thumbnail {
    max-width: 50px;
    max-height: 50px;
}

.p-multiselect-label {
    padding: 3px 8px !important;
}

.form-check-input {
    border: 1px solid #517EBC;
}