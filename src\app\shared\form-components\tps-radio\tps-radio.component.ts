import { CommonModule, NgF<PERSON>, NgIf } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormGroup, FormGroupDirective, ReactiveFormsModule } from '@angular/forms';
import { RadioButtonModule } from 'primeng/radiobutton';
import { TooltipModule } from 'primeng/tooltip';

import { FORM_CONTROL_TYPES } from '../../constants/tps-enum-constants';
import { TpsErrorComponent } from '../tps-error/tps-error.component';


class tpsRadio {
  type: string
  fieldName: string
  tooltip?: string
  options?: []
  hint?: string
  value: any
  label: string
  placeholder: string
  show: boolean;
  onChange?: (field) => void
  rules: {
    required?: boolean,
  }
}

@Component({
  selector: 'tps-radio',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, NgIf, NgFor, TpsErrorComponent, RadioButtonModule, TooltipModule],
  templateUrl: './tps-radio.component.html'
})
export class TpsRadioComponent implements OnInit {
  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  selectedOption: any;
  @Input() field: tpsRadio;
  @Input() formName: FormGroup;
  @Output() onChange: EventEmitter<any> = new EventEmitter();

  constructor(private formgroupDirective: FormGroupDirective) {
    this.formName = formgroupDirective.control;
  }

  ngOnInit(): void {

  }
  public onRadioClick(event): void {
    this.onChange.emit(event);
  }
}
