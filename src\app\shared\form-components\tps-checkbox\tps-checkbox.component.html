<form [formGroup]="formName" autocomplete="off">
    <div class="row mb-4">
        <div class="col-md-5" *ngIf="field.showLabel">
            <div class="flex flex-row items-center gap-2">
                <label for="{{field.label}}">{{field.label}}
                    <span *ngIf="field?.rules?.required && !formName.controls[field.fieldName].disabled"
                        class="requiredField">*</span>
                </label>
                <i *ngIf="field?.tooltip" [pTooltip]="field?.tooltip" tooltipPosition="bottom"
                    class="cursor-pointer pi pi-question-circle helping_text_icon"></i>
            </div>
        </div>
        <div [ngClass]="field.showLabel ? 'col-md-7' : 'col-md-12'">
            <div class="w-full flex flex-column  gap-1">
                <div class="flex flex-row items-center gap-2">
                    <p-checkbox inputId="{{field.label}}" [binary]="true" [name]="field.fieldName" id="{{field.label}}"
                        [formControlName]="field.fieldName" [required]="field?.rules?.required"
                        (onChange)="field.onChange(field);onChangeField($event,field)" />

                    <label [for]="field.label" style="margin-top: 5px;">{{field.label}}</label>
                </div>
                <small *ngIf="field?.hint">{{field?.hint}}</small>
                <tps-error [fieldName]="field.fieldName" [label]="field.label" [formName]="formName"></tps-error>
            </div>
        </div>
    </div>
</form>