<div class="w-full">
    <form [formGroup]="formGroup" autocomplete="off">
        <div class="w-full flex flex-col gap-4">
            <div *ngIf="status!=TABLE_ACTION_TYPES.VIEW" class="required_fields_message mb-2">
                Note: Fields marked with (*) are mandatory
            </div>
            <div class="dynamic-form-wrapper w-full min-w-0 form-row justify-between ">
                <ng-container *ngFor="let field of fields" [ngSwitch]="field.type">
                    <div *ngIf="field?.topGroupTitle" class="w-full tps-dynamic-form-sub-title my-2 mb-4">
                        <div class="flex flex-row  items-center gap-2">
                            <i *ngIf="field.topGroupTitleIcon" class="pi {{field.topGroupTitleIcon}} "
                                style="font-size: 1.25rem !important;color:#3B82F6"></i>
                            <div>{{field.topGroupTitle}}</div>
                        </div>

                    </div>
                    <ng-container *ngIf="field.show">
                        <tps-text class=" col-sm-12 col-12" [ngClass]="layout == 'row' ? 'col-md-5' : 'col-md-12'"
                            *ngSwitchCase="FORM_CONTROL_TYPES.TEXT" [field]="field" [formName]="formGroup"></tps-text>
                    </ng-container>
                    <ng-container *ngIf="field.show">
                        <tps-password class=" col-sm-12 col-12" [ngClass]="layout == 'row' ? 'col-md-5' : 'col-md-12'"
                            *ngSwitchCase="FORM_CONTROL_TYPES.PASSWORD" [field]="field"
                            [formName]="formGroup"></tps-password>
                    </ng-container>

                    <ng-container *ngIf="field.show">
                        <tps-number class=" col-sm-12 col-12" [ngClass]="layout == 'row' ? 'col-md-5' : 'col-md-12'"
                            *ngSwitchCase="FORM_CONTROL_TYPES.NUMBER" [field]="field"
                            [formName]="formGroup"></tps-number>
                    </ng-container>

                    <ng-container *ngIf="field.show">
                        <tps-text-area class=" col-sm-12 col-12" [ngClass]="layout == 'row' ? 'col-md-5' : 'col-md-12'"
                            *ngSwitchCase="FORM_CONTROL_TYPES.TEXTAREA" [field]="field"
                            [formName]="formGroup"></tps-text-area>
                    </ng-container>

                    <ng-container *ngIf="field.show">
                        <tps-single-select class="col-sm-12 col-12"
                            [ngClass]="layout == 'row' ? 'col-md-5' : 'col-md-12'"
                            *ngSwitchCase="FORM_CONTROL_TYPES.SINGLE_SELECT" [field]="field"
                            [formName]="formGroup"></tps-single-select>
                    </ng-container>

                    <ng-container *ngIf="field.show">
                        <tps-multi-select class="col-sm-12 col-12"
                            [ngClass]="layout == 'row' ? 'col-md-5' : 'col-md-12'"
                            *ngSwitchCase="FORM_CONTROL_TYPES.MULTI_SELECT" [field]="field"
                            [formName]="formGroup"></tps-multi-select>
                    </ng-container>

                    <ng-container *ngIf="field.show">
                        <tps-checkbox class="col-sm-12 col-12" [ngClass]="layout == 'row' ? 'col-md-5' : 'col-md-12'"
                            *ngSwitchCase="FORM_CONTROL_TYPES.CHECKBOX" [field]="field"
                            [formName]="formGroup"></tps-checkbox>
                    </ng-container>

                    <ng-container *ngIf="field.show">
                        <tps-date-picker class="col-sm-12 col-12" [ngClass]="layout == 'row' ? 'col-md-5' : 'col-md-12'"
                            *ngSwitchCase="FORM_CONTROL_TYPES.DATE" [field]="field"
                            [formName]="formGroup"></tps-date-picker>
                    </ng-container>
                    <ng-container *ngIf="field.show">
                        <tps-radio class="col-sm-12 col-12" [ngClass]="layout == 'row' ? 'col-md-5' : 'col-md-12'"
                            *ngSwitchCase="FORM_CONTROL_TYPES.RADIO" [field]="field" [formName]="formGroup"></tps-radio>
                    </ng-container>

                    <ng-container *ngIf="field.show">
                        <tps-text-search class="col-sm-12 col-12" [ngClass]="layout == 'row' ? 'col-md-5' : 'col-md-12'"
                            *ngSwitchCase="FORM_CONTROL_TYPES.TEXT_SEARCH" [field]="field" [formName]="formGroup"
                            (onClick)="onSearch($event)"></tps-text-search>
                    </ng-container>

                    <ng-container *ngIf="field.show">
                        <tps-toggle-switch class="col-sm-12 col-12"
                            [ngClass]="layout == 'row' ? 'col-md-5' : 'col-md-12'"
                            *ngSwitchCase="FORM_CONTROL_TYPES.TOGGLE_SWITCH" [field]="field"
                            [formName]="formGroup"></tps-toggle-switch>
                    </ng-container>

                    <ng-container *ngIf="field.show">
                        <tps-slider class="col-sm-12 col-12" [ngClass]="layout == 'row' ? 'col-md-5' : 'col-md-12'"
                            *ngSwitchCase="FORM_CONTROL_TYPES.SLIDER" [field]="field"
                            [formName]="formGroup"></tps-slider>
                    </ng-container>

                    <ng-container *ngIf="field.show">
                        <tps-toggle-button class="col-sm-12 col-12"
                            [ngClass]="layout == 'row' ? 'col-md-5' : 'col-md-12'"
                            *ngSwitchCase="FORM_CONTROL_TYPES.TOGGLE_BUTTON" [field]="field"
                            [formName]="formGroup"></tps-toggle-button>
                    </ng-container>

                    <ng-container *ngIf="field.show">
                        <tps-selected-button class="col-sm-12 col-12"
                            [ngClass]="layout == 'row' ? 'col-md-5' : 'col-md-12'"
                            *ngSwitchCase="FORM_CONTROL_TYPES.SELECTED_BUTTON" [field]="field"
                            [formName]="formGroup"></tps-selected-button>
                    </ng-container>

                    <ng-container *ngIf="field.show">
                        <tps-multi-form-field class="col-sm-12 col-12"
                            *ngSwitchCase="FORM_CONTROL_TYPES.MULTI_FORM_FIELD" [field]="field" [status]="status"
                            [formName]="formGroup"></tps-multi-form-field>
                    </ng-container>

                    <ng-container *ngIf="field.rightSideGap">
                        <div class="col-sm-12 col-12" [ngClass]="layout == 'row' ? 'col-md-5' : 'col-md-12'"></div>
                    </ng-container>
                </ng-container>
            </div>
        </div>
    </form>
</div>