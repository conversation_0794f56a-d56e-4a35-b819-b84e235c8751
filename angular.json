{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"tapas": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "tapas_build", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "allowedCommonJsDependencies": ["highlight.js", "crypto-js/enc-utf8", "crypto-js/hmac-sha256", "crypto-js/enc-base64", "flat", "moment", "mqtt-browser", "file-saver", "d3-voronoi-treemap", "seedrandom", "polylabel", "pdfmake/build/pdfmake.js"], "assets": ["src/favicon-16x16.png", "src/favicon-32x32.png", "src/assets", {"glob": "_redirects", "input": "src", "output": "/"}], "stylePreprocessorOptions": {"includePaths": ["src/@fuse/styles"]}, "styles": ["node_modules/bootstrap/dist/css/bootstrap.min.css", "src/styles/vendors.scss", "node_modules/primeicons/primeicons.css", "src/@fuse/styles/main.scss", "src/styles/styles.scss", "src/@fuse/styles/ag-grid-override.scss"]}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": {"scripts": true, "styles": true, "fonts": {"inline": true}}, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "serviceWorker": true, "aot": true, "ngswConfigPath": "ngsw-config.json", "budgets": [{"type": "initial", "maximumWarning": "10mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "75kb", "maximumError": "90kb"}]}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "tapas:build:production"}, "development": {"buildTarget": "tapas:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "tapas:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon-16x16.png", "src/favicon-32x32.png", "src/assets"], "styles": ["src/styles/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": false}}