import { CommonModule, NgIf } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormGroup, FormGroupDirective, ReactiveFormsModule } from '@angular/forms';
import { InputNumberModule } from 'primeng/inputnumber';
import { TooltipModule } from 'primeng/tooltip';

import { FORM_CONTROL_TYPES } from '../../constants/tps-enum-constants';
import { TpsErrorComponent } from '../tps-error/tps-error.component';

@Component({
  selector: 'tps-number',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, NgIf, TpsErrorComponent, InputNumberModule, TooltipModule],
  templateUrl: './tps-number.component.html'
})
export class TpsNumberComponent implements OnInit {
  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  @Input() field: any;
  @Input() formName: FormGroup;
  @Output() onChange: EventEmitter<any> = new EventEmitter();

  constructor(private formgroupDirective: FormGroupDirective) {
    this.formName = formgroupDirective.control;
  }

  ngOnInit(): void {

  }
  public onInputChange(event): void {
    this.formName.controls[this.field.fieldName].setValue(event.value);
    this.formName.updateValueAndValidity();
    this.onChange.emit(event.value)
  }
}
