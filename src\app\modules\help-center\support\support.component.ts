import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { SharedModule } from 'app/shared/shared.module';
import {
  CommonService,
  FORM_CONTROL_TYPES,
  FormFactoryService,
  InvokeService,
  TABLE_ACTION_TYPES,
} from 'app/shared/tapas-ui';
import { ReplaySubject } from 'rxjs';

import { SUPPORT_FORM_MODEL } from './support-form.mode';

@Component({
  selector: 'app-support',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './support.component.html'
})
export class SupportComponent implements OnInit, OnDestroy {
  supportForm: FormGroup;
  fields: any[] = [];
  zones: any[] = []
  private rec: any;
  //constants
  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  public TABLE_ACTION_TYPES = TABLE_ACTION_TYPES;
  public status: string = TABLE_ACTION_TYPES.CREATE;
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);
  constructor(
    private _commonService: CommonService,
    private _formFactoryService: FormFactoryService,
    private _invokeService: InvokeService,
  ) { }

  public ngOnInit(): void {
    this.buildForm();
  }

  //build form
  private buildForm() {
    const formGroupFields = this._formFactoryService.getFormControlsFields(SUPPORT_FORM_MODEL);
    this.fields = this._formFactoryService.getFieldsList(SUPPORT_FORM_MODEL);
    this.supportForm = new FormGroup(formGroupFields);
  }




  public onSubmit(): void {

  }

  public close(): void {
    this._commonService.navigate("help-center")
  }


  public ngOnDestroy(): void {

  }

}
