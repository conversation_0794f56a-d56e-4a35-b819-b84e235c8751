<form [formGroup]="formName" autocomplete="off">
    <div class="row mb-4" *ngIf="layout=='row'">
        <div class="col-md-5">
            <div class="flex flex-row items-center gap-2">
                <label for="{{field.label}}">{{field.label}}
                    <span *ngIf="field?.rules?.required && !formName.controls[field.fieldName].disabled"
                        class="requiredField">*</span>
                </label>
                <i *ngIf="field?.tooltip" [pTooltip]="field?.tooltip" tooltipPosition="bottom"
                    class="cursor-pointer pi pi-question-circle helping_text_icon"></i>
            </div>
        </div>
        <div class="col-md-7">
            <div class="w-full flex flex-column gap-1">
                <p-datepicker class="w-full tps-input" appendTo="body" showClear="false" dateFormat="dd-mm-yy"
                    id="{{field.label}}" [name]="field.fieldName" [placeholder]="field.placeholder"
                    [formControlName]="field.fieldName" [required]="field?.rules?.required"
                    [minDate]="field?.rules?.minDate" [hourFormat]="12" [maxDate]="field?.rules?.maxDate"
                    [showTime]="field.showTime" [showIcon]="true" [showButtonBar]="true" [iconDisplay]="'input'"
                    [readonlyInput]="true" (onSelect)="field.onChange(field);onChangeField($event,field)"
                    [class.ng-invalid]="formName.controls[field.fieldName].invalid" appendTo="body"
                    [class.ng-dirty]="(formName.controls[field.fieldName].dirty || formName.controls[field.fieldName].touched)" />
                <small *ngIf="field?.hint">{{field?.hint}}</small>
                <tps-error [fieldName]="field.fieldName" [label]="field.label" [formName]="formName"></tps-error>
            </div>
        </div>
    </div>
    <div class="flex flex-col gap-1" *ngIf="layout=='column'">
        <div class="flex flex-row items-center gap-2" *ngIf="showLabel">
            <label for="{{field.label}}">{{field.label}}
                <span *ngIf="field?.rules?.required && !formName.controls[field.fieldName].disabled"
                    class="requiredField">*</span>
            </label>
            <i *ngIf="field?.tooltip" [pTooltip]="field?.tooltip" tooltipPosition="bottom"
                class="cursor-pointer pi pi-question-circle helping_text_icon"></i>
        </div>
        <div class="w-full flex flex-column gap-1">
            <p-calendar class="w-full tps-input" appendTo="body" showClear="false" dateFormat="dd-mm-yy"
                id="{{field.label}}" [name]="field.fieldName" [placeholder]="field.placeholder"
                [formControlName]="field.fieldName" [required]="field?.rules?.required"
                [minDate]="field?.rules?.minDate" [hourFormat]="12" [maxDate]="field?.rules?.maxDate"
                [showTime]="field.showTime" [showIcon]="true" [showButtonBar]="true" [iconDisplay]="'input'"
                [readonlyInput]="true" (onSelect)="onChangeField($event,field)"
                [class.ng-invalid]="formName.controls[field.fieldName].invalid" appendTo="body"
                [class.ng-dirty]="(formName.controls[field.fieldName].dirty || formName.controls[field.fieldName].touched)"></p-calendar>
            <small *ngIf="field?.hint">{{field?.hint}}</small>
            <tps-error [fieldName]="field.fieldName" [label]="field.label" [formName]="formName"></tps-error>
        </div>
    </div>
</form>