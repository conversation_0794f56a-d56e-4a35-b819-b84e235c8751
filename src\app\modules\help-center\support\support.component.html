<div class="flex min-w-0 flex-auto flex-col ">
    <div class=" relative overflow-hidden  px-4 pb-28 pt-8  sm:px-16 sm:pb-48 sm:pt-20"
        style="color:white;background-image: linear-gradient(90deg, #265DAB 12.65%, #2B75BC 30.36%, #3F8CC6 48.58%)">
        <div class="relative z-10 flex flex-col items-center">
            <div class="mt-1 text-center text-4xl font-extrabold leading-tight tracking-tight sm:text-7xl">Contact For
                Support</div>
        </div>
    </div>
    <div class="flex flex-col items-center px-6 pb-6 sm:px-10 sm:pb-10">
        <div
            class="-mt-16 grid w-full max-w-sm grid-cols-1 gap-y-8 sm:-mt-24 md:max-w-4xl md:grid-cols-1 md:gap-x-4 md:gap-y-0">
            <div class="bg-card relative flex flex-col overflow-hidden rounded-2xl shadow transition-shadow duration-150 ease-in-out hover:shadow-lg justify-center"
                style="box-shadow: rgba(0, 0, 0, 0.07) 0px 1px 2px, rgba(0, 0, 0, 0.07) 0px 2px 4px, rgba(0, 0, 0, 0.07) 0px 4px 8px, rgba(0, 0, 0, 0.07) 0px 8px 16px, rgba(0, 0, 0, 0.07) 0px 16px 32px, rgba(0, 0, 0, 0.07) 0px 32px 64px !important;">
                <div class="flex justify-center p-8">
                    <address>
                        3Frames Software Labs Pvt Ltd,<br>
                        1st floor, No. 183, Double Road,<br>
                        10th Main Rd, 2nd Stage, Indiranagar,<br>
                        Bengaluru, Karanataka 560038,<br>
                        <!-- <div class="flex flex-row gap-2 items-center">
                            <label>E-mail:</label>
                            <a href="<EMAIL>">ramakrishna&#64;3frameslab.com</a>
                        </div> -->
                    </address>


                    <!-- <tps-form class="w-full" [formGroup]="supportForm" [fields]="fields" [status]="status"
                        [layout]="'column'"></tps-form> -->
                </div>
                <div class="tps-full-screen-footer">
                    <div class="w-full flex flex-row justify-end gap-6">
                        <tps-secondary-button [buttonName]="'Cancel'" (onClick)="close()"></tps-secondary-button>
                        <!-- <tps-primary-button *ngIf="status != TABLE_ACTION_TYPES.VIEW" [buttonName]="'Submit'"
                            [isDisabled]="supportForm.invalid" (onClick)="onSubmit()"></tps-primary-button> -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>