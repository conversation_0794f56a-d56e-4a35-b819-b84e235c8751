import { FORM_CONTROL_TYPES, FORM_FIELDS_CONSTANTS_VALUES, ICON_BUTTON_COLOR } from 'app/shared/tapas-ui';



export const USER_FORM_MODEL = {
    firstName: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "First Name",
        placeholder: 'Ex.Mick',
        topGroupTitleIcon: 'pi-user',
        severity: ICON_BUTTON_COLOR.WARNING,
        topGroupTitle: "User Details",
        show: true,
        rules: {
            required: true,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_NUMERIC_PATTERN_WITH_SPACE,
            maxLength: 250,
        }
    },
    lastName: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Last Name",
        placeholder: 'Ex.Morris',
        show: true,
        rules: {
            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_NUMERIC_PATTERN_WITH_SPACE,
            maxLength: 250,
        }
    },
    userName: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "User Name",
        placeholder: 'Ex.mick123',
        show: true,
        rules: {
            required: true,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_NUMERIC_PATTERN_NO_SPACE,
            minLength: 5,
            maxLength: 20,
        },
        tooltip: "User name should be alphabets and numbers(No space allowed) "
    },
    password: {
        type: FORM_CONTROL_TYPES.PASSWORD,
        value: "",
        label: "Password",
        placeholder: 'Ex.mick@123456',
        show: true,
        rules: {
            required: true,
            maxLength: 250,
        }
    },
    role: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        onChange: (event) => { },
        options: [],
        value: "",
        label: "Role",
        placeholder: 'Select role',
        show: true,
        rules: {
            required: true,
        }
    },
    email: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Email",
        placeholder: '<EMAIL>',
        topGroupTitle: 'Contact Details',
        topGroupTitleIcon: 'pi-phone',
        severity: ICON_BUTTON_COLOR.WARNING,
        show: true,
        rules: {
            required: true,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.EMAIL_PATTERN,
            maxLength: 250,
        }
    },
    contactNo: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Contact No.",
        placeholder: 'Ex.0803842942',
        show: true,
        rules: {
            // required: true,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.NUMERIC_PATTERN,
            minLength: FORM_FIELDS_CONSTANTS_VALUES.PHONE_NUMBER_LENGTH,
            maxLength: FORM_FIELDS_CONSTANTS_VALUES.PHONE_NUMBER_LENGTH,

        }
    },
    city: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "City",
        placeholder: 'Ex.Bangalore',
        topGroupTitle: 'Address',
        topGroupTitleIcon: 'pi-map-marker',
        severity: ICON_BUTTON_COLOR.HELP,
        show: true,
        rules: {
            // required: true,
            maxLength: 250,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_PATTERN,
        }
    },
    country: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Country",
        placeholder: 'Ex.India',
        show: true,
        rules: {
            // required: true,
            maxLength: 250,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_PATTERN,
        }
    },
    pinCode: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "PinCode",
        placeholder: 'Ex.123456',
        show: false,
        rules: {
            minLength: 6,
            maxLength: 6,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.NUMERIC_PATTERN,
        }
    },
}
