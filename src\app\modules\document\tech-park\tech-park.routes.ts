import { Routes } from '@angular/router';
import { permissionGuard } from 'app/core/auth/guards/permission.guard';

import { TechParkComponent } from './tech-park.component';

export default [
    {
        path: '',
        component: TechParkComponent,
        canActivate: [permissionGuard],
        data: { permission: "TECH_PARK" },
    },
    { path: 'not-found', loadChildren: () => import('app/modules/page-not-found/page-not-found.routes') },
    { path: '**', pathMatch: 'full', redirectTo: 'not-found' },
] as Routes;