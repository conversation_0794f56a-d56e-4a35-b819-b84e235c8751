import { NgIf } from '@angular/common';
import { Component, OnDestroy, OnInit } from '@angular/core';
import {
    NavigationCancel,
    NavigationEnd,
    NavigationError,
    NavigationStart,
    Router,
    RouterEvent,
    RouterOutlet,
} from '@angular/router';
import { SwPush, SwUpdate } from '@angular/service-worker';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ProgressBarModule } from 'primeng/progressbar';
import { RippleModule } from 'primeng/ripple';
import { ToastModule } from 'primeng/toast';
import { retry, Subject, takeUntil } from 'rxjs';

import { PushNotificationsService } from './shared/services/push-notifications.service';
import { CommonService } from './shared/tapas-ui';


@Component({
    selector: 'app-root',
    templateUrl: './app.component.html',
    styleUrls: ['./app.component.scss'],
    standalone: true,
    imports: [NgIf, RouterOutlet, ProgressBarModule, ToastModule, RippleModule, ConfirmDialogModule],
    providers: [MessageService, ConfirmationService,]
})
export class AppComponent implements OnInit, OnDestroy {
    /**
     * Constructor
     */
    deferredPrompt: any;
    private $destroyed: Subject<boolean> = new Subject();
    public loading: boolean = false;
    public isMobile: boolean = false;
    constructor(
        private _commonService: CommonService,
        private messageService: MessageService,
        private router: Router,
        private _swPush: SwPush,
        private _swUpdate: SwUpdate,
        private _pushNotificationsService: PushNotificationsService
    ) {
         // Handle PWA installation
    window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault();
        this.deferredPrompt = e;
      });

        this._commonService.messageNotification
            .pipe(takeUntil(this.$destroyed))
            .subscribe(response => {
                if (response) {
                    this.messageService.add(response)
                }
            })
        router.events.subscribe((event: any) => {
            this.navigationInterceptor(event);
        });
        if (this._swPush.isEnabled) {
            // Check for updates every minute instead of every second
            setInterval(() => {
                this._swUpdate.checkForUpdate();
            }, 60000);

            this._swPush.notificationClicks.subscribe(({ action, notification }) => {
                console.log("notification clicks", action, notification);
            });

            this._swPush.messages.subscribe(message => {
                console.log("message", message);
            });

            // Handle subscription errors
            this._swPush.subscription.subscribe(sub => {
                if (!sub) {
                    this._pushNotificationsService.requestSubscription();
                }
            });

            // Initial subscription request
            this._pushNotificationsService.requestSubscription();

            // Send test notification with retry logic
            this._pushNotificationsService.sendNotification()
                .pipe(retry(3))
                .subscribe({
                    error: (error) => console.error('Failed to send notification:', error)
                });
        }

    }
    async installPwa(): Promise<void> {
        if (this.deferredPrompt) {
          this.deferredPrompt.prompt();
          const { outcome } = await this.deferredPrompt.userChoice;
          console.log(`User response to the install prompt: ${outcome}`);
          this.deferredPrompt = null;
        }
      }

    // Shows and hides the loading spinner during RouterEvent changes
    private navigationInterceptor(event: RouterEvent): void {
        if (event instanceof NavigationStart) {
            this.loading = true;
        }
        if (event instanceof NavigationEnd) {
            setTimeout(() => { // here
                this.loading = false;
            }, 300);
        }

        // Set loading state to false in both of the below events to hide the spinner in case a request fails
        if (event instanceof NavigationCancel) {
            setTimeout(() => { // here
                this.loading = false;
            }, 300);
        }
        if (event instanceof NavigationError) {
            setTimeout(() => { // here
                this.loading = false;
            }, 300);
        }

    }

    public ngOnInit(): void {
    
    }



    public ngOnDestroy(): void {

    }
}
