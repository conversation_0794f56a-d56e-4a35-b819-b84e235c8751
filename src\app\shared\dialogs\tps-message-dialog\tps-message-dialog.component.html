<div class="flex flex-column align-items-center p-3 surface-overlay border-round">
    <p-message severity="{{messageConfig.messageType}}" variant="simple" size="large"
        [icon]="getIconByMessageType(messageConfig.messageType)">{{messageConfig.message}}</p-message>
    <div class="flex align-items-center gap-4 mt-4" *ngIf="messageConfig.messageType != 'in-progress'">
        <tps-primary-button [buttonName]="'OK'" (onClick)="close()"></tps-primary-button>
    </div>
</div>