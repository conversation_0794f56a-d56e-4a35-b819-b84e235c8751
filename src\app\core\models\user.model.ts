
export interface iUser {
    code?: string;
    id?: string;
    userName?: string;
    password?: string;
    firstName?: string;
    lastName?: string;
    fullName?: string;
    addressLine1?: string;
    addressLine2?: string;
    city?: string;
    country?: string;
    pincode?: string;
    contactNo?: string;
    email?: string;
    role?: string;
    createdDate?: string;
    modifiedDate?: string;
    status?: string;
    isActive?: string;
    loggedOn?: string;
    mappedWith?: string;
    mobileRegId?: string;
    token?: string;
    pic?: string;
    tenantUid?: string;
    uuid?: any;
    dashboardUrl?: string;
    departmentCode?: string
    departmentUid?: string
    name?: string
    designation?: string
    avatar?: string
    mfaEnable?: any
    fileIcon?:string
};

export class QiUser implements iUser {

    constructor(
        public code?,
        public id?,
        public userName?,
        public password?,
        public firstName?,
        public lastName?,
        public fullName?,
        public addressLine1?,
        public addressLine2?,
        public city?,
        public country?,
        public pincode?,
        public contactNo?,
        public email?,
        public role?,
        public createdDate?,
        public modifiedDate?,
        public status?,
        public isActive?,
        public loggedOn?,
        public mappedWith?,
        public mobileRegId?,
        public token?,
        public pic?,
        public tenantUid?,
        public uuid?,
        public dashboardUrl?,
        public departmentCode?,
        public departmentUid?,
        public name?,
        public designation?,
        public avatar?,
        public mfaEnable?
    ) { }
};
