import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { TooltipModule } from 'primeng/tooltip';

@Component({
  selector: 'tps-icon',
  standalone: true,
  imports: [CommonModule, TooltipModule],
  templateUrl: './tps-icon.component.html',
  styleUrls: ['./tps-icon.component.css']
})
export class TpsIconComponent {
  @Input() svgIcon: string;
  @Input() icon: string;
  @Input() name: string;
  @Input() tooltipPosition: string = 'bottom';
  @Output() onClick: EventEmitter<any> = new EventEmitter<any>();

  onIconClick(event: any): void {
    this.onClick.emit(event);
  }
}