import { FORM_CONTROL_TYPES, ICON_BUTTON_COLOR } from 'app/shared/tapas-ui';



export const SUPPORT_FORM_MODEL = {
    name: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Name",
        placeholder: 'Name',
        show: true,
        rules: {
            required: true,
            maxLength: 255,
        }
    },
    email: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Email",
        placeholder: 'Email',
        show: true,
        rules: {
            required: true,
            maxLength: 255,
        }
    },
    subject: {
        type: FORM_CONTROL_TYPES.TEXT,
        options: [],
        value: "",
        label: "Subject",
        placeholder: 'Subject',
        show: true,
        rules: {
            required: true,
            maxLength: 255,
        }
    },
    message: {
        type: FORM_CONTROL_TYPES.TEXTAREA,
        options: [],
        value: "",
        label: "Message",
        placeholder: 'Message',
        show: true,
        rules: {
            required: true,
            maxLength: 255,
        }
    }
}