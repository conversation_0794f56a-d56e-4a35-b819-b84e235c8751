export interface Factory {
  code: string;
  id?: number;
  name?: string;
  description?: string;
  establishedYear?: number;
  riskLevel?: number;
  cocRating?: string;
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  country?: string;
  pincode?: string;
  contactNo?: string;
  email?: string;
  altemail?: string;
  latitude?: number;
  longitude?: number;
  createdDate?: number;
  modifiedDate?: number;
  status?: number;
  isActive?: boolean;
  validUpto?: any;
  cvalidUpto?: number;
  vendorId?: number;
  scheduleCount?: number;
  rawMaterialQualityAssurance?: number;
  preProductionActivity?: number;
  productionAssembly?: number;
  markerSpreadingCutting?: number;
  finishingAndPackaging?: number;
  internalFinalQualityAudit?: number;
  qualityManagemnet?: number;
  basicConditionEvaluation?: number;
  productQualityAudit?: number;
  processAuditDate?: number;
  factoryAuditDate?: any;
  factoryAuditScore?: any;
  uuid?: string;
  createdDateText?: string;
  auditRating?:any;
  tenantId?:any;
  tenantUid?:string;
}

export class Factory implements Factory {
  public code: string;
  public id?: number;
  public name?: string;
  public description?: string;
  public establishedYear?: number;
  public riskLevel?: number;
  public cocRating?: string;
  public addressLine1?: string;
  public addressLine2?: string;
  public city?: string;
  public country?: string;
  public pincode?: string;
  public contactNo?: string;
  public email?: string;
  public altemail?: string;
  public latitude?: number;
  public longitude?: number;
  public createdDate?: number;
  public modifiedDate?: number;
  public status?: number;
  public isActive?: boolean;
  public validUpto?: any;
  public cvalidUpto?: number;
  public vendorId?: number;
  public scheduleCount?: number;
  public rawMaterialQualityAssurance?: number;
  public preProductionActivity?: number;
  public productionAssembly?: number;
  public markerSpreadingCutting?: number;
  public finishingAndPackaging?: number;
  public internalFinalQualityAudit?: number;
  public qualityManagemnet?: number;
  public basicConditionEvaluation?: number;
  public productQualityAudit?: number;
  public processAuditDate?: number;
  public factoryAuditDate?: any;
  public factoryAuditScore?: any;
  public uuid?: string;
  public createdDateText?: string;
  public auditRating?:any;
  public tenantId?:any
  public tenantUid?:string
}
