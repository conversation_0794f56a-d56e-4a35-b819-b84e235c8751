<div class="flex flex-col sm:flex-row items-center md:items-start sm:justify-center md:justify-start flex-auto min-w-0">
    <div class="md:flex md:items-center md:justify-end w-full sm:w-auto md:h-full md:w-1/2 py-8 px-4 sm:p-12 md:p-16 sm:rounded-2xl md:rounded-none sm:shadow md:shadow-none sm:bg-card">
        <div class="w-full max-w-80 sm:w-80 mx-auto sm:mx-0">
            <!-- Logo -->
            <div class="w-12">
                <img src="assets/images/logo/logo.svg">
            </div>

            <!-- Title -->
            <div class="mt-8 text-4xl font-extrabold tracking-tight leading-tight">Reset your password</div>
            <div class="mt-0.5 font-medium">Create a new password for your account</div>

        

            <!-- Reset password form -->
            <form
                class="mt-8"
                [formGroup]="resetPasswordForm"
                #resetPasswordNgForm="ngForm">

                <!-- Password field -->
                <mat-form-field class="w-full">
                    <mat-label>Password</mat-label>
                    <input
                        id="password"
                        matInput
                        type="password"
                        [formControlName]="'password'"
                        #passwordField>
                    <button
                        mat-icon-button
                        type="button"
                        (click)="passwordField.type === 'password' ? passwordField.type = 'text' : passwordField.type = 'password'"
                        matSuffix>
                        <mat-icon
                            class="icon-size-5"
                            *ngIf="passwordField.type === 'password'"
                            [svgIcon]="'heroicons_solid:eye'"></mat-icon>
                        <mat-icon
                            class="icon-size-5"
                            *ngIf="passwordField.type === 'text'"
                            [svgIcon]="'heroicons_solid:eye-slash'"></mat-icon>
                    </button>
                    <mat-error>
                        Password is required
                    </mat-error>
                </mat-form-field>

                <!-- Password confirm field -->
                <mat-form-field class="w-full">
                    <mat-label>Password (Confirm)</mat-label>
                    <input
                        id="password-confirm"
                        matInput
                        type="password"
                        [formControlName]="'passwordConfirm'"
                        #passwordConfirmField>
                    <button
                        mat-icon-button
                        type="button"
                        (click)="passwordConfirmField.type === 'password' ? passwordConfirmField.type = 'text' : passwordConfirmField.type = 'password'"
                        matSuffix>
                        <mat-icon
                            class="icon-size-5"
                            *ngIf="passwordConfirmField.type === 'password'"
                            [svgIcon]="'heroicons_solid:eye'"></mat-icon>
                        <mat-icon
                            class="icon-size-5"
                            *ngIf="passwordConfirmField.type === 'text'"
                            [svgIcon]="'heroicons_solid:eye-slash'"></mat-icon>
                    </button>
                    <mat-error *ngIf="resetPasswordForm.get('passwordConfirm').hasError('required')">
                        Password confirmation is required
                    </mat-error>
                    <mat-error *ngIf="resetPasswordForm.get('passwordConfirm').hasError('mustMatch')">
                        Passwords must match
                    </mat-error>
                </mat-form-field>

                <!-- Submit button -->
                <button
                    class="fuse-mat-button-large w-full mt-3"
                    mat-flat-button
                    [color]="'primary'"
                    [disabled]="resetPasswordForm.disabled"
                    (click)="resetPassword()">
                    <span *ngIf="!resetPasswordForm.disabled">
                        Reset your password
                    </span>
                    <mat-progress-spinner
                        *ngIf="resetPasswordForm.disabled"
                        [diameter]="24"
                        [mode]="'indeterminate'"></mat-progress-spinner>
                </button>

                <!-- Form footer -->
                <div class="mt-8 text-md font-medium text-secondary">
                    <span>Return to</span>
                    <a
                        class="ml-1 text-primary-500 hover:underline"
                        [routerLink]="['/sign-in']">sign in
                    </a>
                </div>

            </form>
        </div>
    </div>
    <div class="relative hidden md:flex flex-auto items-center justify-center w-1/2 h-full p-16 lg:px-28 overflow-hidden bg-gray-800 dark:border-l">
        <!-- Background - @formatter:off -->
        <!-- Rings -->
        <svg class="absolute inset-0 pointer-events-none"
             viewBox="0 0 960 540" width="100%" height="100%" preserveAspectRatio="xMidYMax slice" xmlns="http://www.w3.org/2000/svg">
            <g class="text-gray-700 opacity-25" fill="none" stroke="currentColor" stroke-width="100">
                <circle r="234" cx="196" cy="23"></circle>
                <circle r="234" cx="790" cy="491"></circle>
            </g>
        </svg>
        <!-- Dots -->
        <svg class="absolute -top-16 -right-16 text-gray-700"
             viewBox="0 0 220 192" width="220" height="192" fill="none">
            <defs>
                <pattern id="837c3e70-6c3a-44e6-8854-cc48c737b659" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
                    <rect x="0" y="0" width="4" height="4" fill="currentColor"></rect>
                </pattern>
            </defs>
            <rect width="220" height="192" fill="url(#837c3e70-6c3a-44e6-8854-cc48c737b659)"></rect>
        </svg>
        <!-- @formatter:on -->
        <!-- Content -->
        <div class="z-10 relative w-full max-w-2xl">
            <div class="text-7xl font-bold leading-none text-gray-100">
                <div>Welcome to</div>
                <div>our community</div>
            </div>
            <div class="mt-6 text-lg tracking-tight leading-6 text-gray-400">
                Fuse helps developers to build organized and well coded dashboards full of beautiful and rich modules.
                Join us and start building your application today.
            </div>
            <div class="flex items-center mt-8">
                <div class="flex flex-0 items-center -space-x-1.5">
                    <img
                        class="flex-0 w-10 h-10 rounded-full ring-4 ring-offset-1 ring-gray-800 ring-offset-gray-800 object-cover"
                        src="assets/images/avatars/female-18.jpg">
                    <img
                        class="flex-0 w-10 h-10 rounded-full ring-4 ring-offset-1 ring-gray-800 ring-offset-gray-800 object-cover"
                        src="assets/images/avatars/female-11.jpg">
                    <img
                        class="flex-0 w-10 h-10 rounded-full ring-4 ring-offset-1 ring-gray-800 ring-offset-gray-800 object-cover"
                        src="assets/images/avatars/male-09.jpg">
                    <img
                        class="flex-0 w-10 h-10 rounded-full ring-4 ring-offset-1 ring-gray-800 ring-offset-gray-800 object-cover"
                        src="assets/images/avatars/male-16.jpg">
                </div>
                <div class="ml-4 font-medium tracking-tight text-gray-400">More than 17k people joined us, it's your turn</div>
            </div>
        </div>
    </div>
</div>
