<p-toast appendTo="body"/>
<div class="overlay" *ngIf="loading">
    <div class="overlay__inner">
        <div class="overlay__content">
            <span class="spinner"></span>
            <div class="spinner-text mt-4">Loading...</div>
        </div>
    </div>
</div>

<button *ngIf="deferredPrompt" 
        pButton 
        type="button" 
        (click)="installPwa()" 
        label="Install App"
        icon="pi pi-download"
        class="p-button-secondary">
</button>

<router-outlet></router-outlet>
