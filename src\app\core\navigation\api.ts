import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { FuseNavigationItem } from '@fuse/components/navigation';
import { AuthService } from 'app/core/auth/auth.service';
import { QiUser } from 'app/core/models/user.model';
import { environment, PARTNERS } from 'environments/environment';
import { cloneDeep } from 'lodash-es';

import { ACL } from '../acl/acl';
import { UI_CONFIGURATION } from './navigation';
import { UI_CONFIGURATION_MOBILE } from './navigation-mobile';

@Injectable({ providedIn: 'root' })
export class NavigationMockApi {
    private _navigation: any[] = [];
    isMobile: boolean = false; //false for desktop and true for mobile
    /**
     * Constructor
     */
    constructor(
        private _authService: AuthService,
        private _httpClient: HttpClient, private breakpointObserver: BreakpointObserver
    ) {
        this.loadConfig(environment.Partner);
        // Register Mock API handlers

    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Register Mock API handlers
     */
    private loadConfig(client: string) {
        this.breakpointObserver.observe([
            Breakpoints.XSmall,
            Breakpoints.Small
        ]).subscribe(result => {
            this.isMobile = result.matches;
            if (this.isMobile) {
                this.getNavigationByClient(UI_CONFIGURATION_MOBILE, client);
            } else {
                this.getNavigationByClient(UI_CONFIGURATION, client);
            }
        });



        this.registerHandlers();
    }

    public getNavigationByClient(configuration, client): any {
        if (configuration[client]) {
            this._navigation = configuration[client].menu;
        } else {
            this._navigation = configuration[PARTNERS.STAGE].menu;
        }
    }

    public registerHandlers(): any {
        return {
            navigation: cloneDeep(this.configureMenu()),
        }
    }
    public configureMenu(): any[] {
        if (this._authService.getLoggedInUser()) {
            let navigation: any[] = this.prepareMenu();
            return navigation;
        } else {
            return [];
        }
    }

    public prepareMenu(): any[] {
        let menus: any[] = this.prepareChildren(this._navigation);
        menus.forEach((item, index) => {
            if (menus[index].children) {
                menus[index].children = this.prepareChildren(menus[index].children);
                menus[index].children.forEach((item1, index1) => {
                    if (menus[index].children[index1].children) {
                        menus[index].children[index1].children = this.prepareChildren(menus[index].children[index1].children);
                    }
                });
            }
        });
        return menus;
    }
    private prepareChildren(array: any[]): any[] {
        let user: QiUser = this._authService.getLoggedInUser();
        return array.filter(item => this.isACL(user.role, item.ACL_CODE))

    }
    private isACL(role, ACL_CODE): boolean {
        let getACLByPartner: any = ACL[environment.Partner]
        let roleList: any = getACLByPartner[role];
        return roleList[ACL_CODE]
    }

}
