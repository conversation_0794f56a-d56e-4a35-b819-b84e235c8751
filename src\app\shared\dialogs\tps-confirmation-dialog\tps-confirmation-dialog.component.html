<div #cd>
    <div class="w-full flex flex-column items-center p-5 surface-overlay border-round">
        <div class="inline-flex justify-content-center align-items-center h-6rem w-6rem"
            style="padding: 2rem;border-radius: 50%;color: #517EBC;">
            <i class="pi pi-question-circle" style="font-size: 5rem !important;"></i>
        </div>
        <div class="w-full flex flex-row gap-2 text-2xl mb-2 font-bold mt-4">
            <div>{{message}}</div>
            <div *ngIf="extraString">"{{extraString}}"</div>
        </div>

        <div class="mb-0 sm:text-sm md:text-base" style="font-size: 16px;">Please confirm to proceed.</div>
        <div class="flex align-items-center gap-4 mt-4">
            <tps-primary-button [buttonName]="'Proceed'" (onClick)="accept()"></tps-primary-button>
            <tps-secondary-button [buttonName]="'Cancel'" (onClick)="reject()"></tps-secondary-button>
        </div>
    </div>
</div>