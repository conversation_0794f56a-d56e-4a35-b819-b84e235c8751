import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { SharedModule } from 'app/shared/shared.module';
import { CommonService } from 'app/shared/tapas-ui';

@Component({
  selector: 'app-help-center-home',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './help-center-home.component.html',
  styleUrl: './help-center-home.component.scss'
})
export class HelpCenterHomeComponent implements OnInit, OnDestroy {


  constructor(
    private _commonService: CommonService,
  ) { }

  public ngOnInit(): void {

  }

  public onNavigate(routerLink: any): void {
    this._commonService.navigate(routerLink);
  }

  public ngOnDestroy(): void {

  }
}
