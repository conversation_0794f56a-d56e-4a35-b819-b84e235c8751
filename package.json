{"name": "tapas.ai", "version": "1.0.0", "description": "Tapas.ai", "main": "electron.main.js", "author": "3Frames Software Labs", "private": true, "scripts": {"ng": "ng", "start": "node --max_old_space_size=8192 node_modules/@angular/cli/bin/ng serve --host 0.0.0.0", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "tapas-prod": "node --max_old_space_size=150124 node_modules/@angular/cli/bin/ng build --configuration production --optimization --aot --build-optimizer --output-hashing=all"}, "dependencies": {"@amcharts/amcharts5": "^5.11.2", "@amcharts/amcharts5-geodata": "^5.1.4", "@angular-material-components/datetime-picker": "^16.0.1", "@angular-material-components/moment-adapter": "^16.0.1", "@angular/animations": "19.2.3", "@angular/cdk": "19.2.6", "@angular/common": "^19.2.3", "@angular/compiler": "19.2.3", "@angular/core": "19.2.3", "@angular/forms": "19.2.3", "@angular/material": "19.2.6", "@angular/platform-browser": "19.2.3", "@angular/platform-browser-dynamic": "19.2.3", "@angular/router": "19.2.3", "@angular/service-worker": "^19.2.3", "@primeng/themes": "^19.0.10", "@tailwindcss/postcss": "^4.0.16", "@types/xlsx": "^0.0.35", "bootstrap": "^5.3.3", "crypto-js": "4.2.0", "d3": "^7.9.0", "d3-selection": "^3.0.0", "d3-x3d": "^2.1.5", "file-saver": "^2.0.5", "highlight.js": "11.11.1", "js-sha512": "^0.9.0", "lodash-es": "4.17.21", "moment": "^2.30.1", "ngx-extended-pdf-viewer": "^23.0.0-alpha.6", "ngx-mqtt": "^17.0.0", "perfect-scrollbar": "1.5.6", "primeicons": "^7.0.0", "primeng": "^19.0.10", "rxjs": "7.8.2", "tslib": "2.8.1", "xlsx": "^0.18.5", "zone.js": "0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "19.2.4", "@angular/cli": "19.2.4", "@angular/compiler-cli": "19.2.3", "@types/chroma-js": "3.1.1", "@types/crypto-js": "4.2.2", "@types/d3": "^7.4.3", "@types/highlight.js": "10.1.0", "@types/jasmine": "5.1.7", "@types/lodash": "4.17.16", "@types/lodash-es": "4.17.12", "@types/node": "^22.13.13", "autoprefixer": "^10.4.21", "chroma-js": "3.1.2", "jasmine-core": "5.6.0", "karma": "6.4.4", "karma-chrome-launcher": "3.2.0", "karma-coverage": "2.2.1", "karma-jasmine": "5.1.0", "karma-jasmine-html-reporter": "2.1.0", "lodash": "4.17.21", "postcss": "^8.5.3", "tailwindcss": "^3.4.1", "typescript": "5.8.2"}}