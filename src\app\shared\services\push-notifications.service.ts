import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SwPush, SwUpdate } from '@angular/service-worker';
import { from, Observable, throwError } from 'rxjs';
import { catchError, retry, switchMap } from 'rxjs/operators';

const VALID_KEY = "BLJgjfdm0lbLeEpy9WLLCY51Z_GLLLn0wu8zi_7sQHeilmotbMN1IpEkess_s2hTykIOsfQZdQz7N5Re-oNehDI";

@Injectable({
  providedIn: 'root'
})
export class PushNotificationsService {
  private subscription: PushSubscription | null = null;

  constructor(
    private _httpClient: HttpClient,
    private _swPush: SwPush,
    private _swUpdate: SwUpdate
  ) {
    // Check for service worker updates
    if (this._swUpdate.isEnabled) {
      this._swUpdate.versionUpdates.subscribe(event => {
        if (event.type === 'VERSION_READY' && confirm('New version available. Load new version?')) {
          window.location.reload();
        }
      });
    }
  }

  public checkNetworkStatus(): Observable<boolean> {
    return new Observable(observer => {
      observer.next(navigator.onLine);
      window.addEventListener('online', () => observer.next(true));
      window.addEventListener('offline', () => observer.next(false));
    });
  }

  public async cacheForOffline() {
    if ('caches' in window) {
      const cache = await caches.open('app-static');
      const urls = [
        '/',
        '/index.html',
        '/manifest.webmanifest',
        // Add other important URLs
      ];
      await cache.addAll(urls);
    }
  }

  public requestSubscription() {
    if (!this._swPush.isEnabled) {
      console.warn('Push notifications are not enabled');
      return;
    }

    this._swPush.subscription.subscribe(sub => {
      this.subscription = sub;
    });

    this._swPush.requestSubscription({
      serverPublicKey: VALID_KEY,
    }).then((notification) => {
      this.addPushSubscriber(notification).pipe(
        retry(3) // Retry failed requests up to 3 times
      ).subscribe({
        next: response => {
          console.log('Sent push subscription object to server.');
          this.subscription = notification;
        },
        error: error => {
          console.error('Could not send subscription object to server, reason: ', error);
          this.handleSubscriptionError(error);
        }
      });
    }).catch((error) => {
      console.error('Could not subscribe to notifications', error);
      this.handleSubscriptionError(error);
    });
  }

  private handleSubscriptionError(error: any) {
    if (error?.statusCode === 410 || // Gone - subscription expired
        error?.message?.includes('expired') ||
        error?.message?.includes('unsubscribed')) {
      console.log('Subscription expired or invalid, resubscribing...');
      
      // Unsubscribe from current subscription if it exists
      if (this.subscription) {
        this.subscription.unsubscribe().then(() => {
          this.subscription = null;
          // Retry subscription after a short delay
          setTimeout(() => this.requestSubscription(), 1000);
        });
      } else {
        // Retry subscription immediately if no existing subscription
        this.requestSubscription();
      }
    }
  }

  public addPushSubscriber(sub: any) {
    return this._httpClient.post('/api/notifications', sub).pipe(
      catchError(error => {
        console.error('Error adding push subscriber:', error);
        return throwError(() => error);
      })
    );
  }

  public sendNotification() {
    return this._httpClient.post('/api/newsletter', null).pipe(
      catchError(error => {
        if (error?.status === 410) {
          // Handle expired subscription
          this.handleSubscriptionError(error);
        }
        return throwError(() => error);
      })
    );
  }
}
