<form [formGroup]="formName" autocomplete="off">
    <div class="row mb-4">
        <div class="col-md-5">
            <div class="flex flex-row items-center gap-2">
                <label for="{{field.label}}">{{field.label}}
                    <span *ngIf="field?.rules?.required && !formName.controls[field.fieldName].disabled"
                        class="requiredField">*</span>
                </label>
                <i *ngIf="field?.tooltip" [pTooltip]="field?.tooltip" tooltipPosition="bottom"
                    class="cursor-pointer pi pi-question-circle helping_text_icon"></i>
            </div>
        </div>
        <div class="col-md-7">
            <div class="w-full flex flex-column  gap-1">
                <div class="flex flex-row align-items-center gap-1 mt-4">
                    <p-slider class="w-full" id="{{field.label}}" [formControlName]="field.fieldName" animate="true"
                        [step]="field.rules.step ? field.rules.step :1" [required]="field?.rules?.required"
                        styleClass="w-full" (onChange)="field.onChange($event)" />
                </div>
                <small *ngIf="field?.hint">{{field?.hint}}</small>
                <tps-error [fieldName]="field.fieldName" [label]="field.label" [formName]="formName"></tps-error>
            </div>
        </div>
    </div>
</form>