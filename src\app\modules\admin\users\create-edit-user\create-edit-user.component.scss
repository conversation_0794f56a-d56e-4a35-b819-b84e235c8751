// Base styles
:host {
    display: block;
    width: 100%;
}

// Mobile specific styles
@media (max-width: 640px) {
    .modal-content {
        margin-bottom: 120px; // Space for fixed footer        padding-bottom: 1rem;
    }

    .modal-footer {
        border-top: 1px solid #e5e7eb;
        z-index: 50;
    }

    // Adjust form fields for mobile
    ::ng-deep {
        .form-field {
            margin-bottom: 1rem;
        }

        // Make inputs larger and more touch-friendly
        input,
        select,
        textarea {
            height: 48px;
            font-size: 16px; // Prevents zoom on iOS        }

            // Increase label visibility
            label {
                font-size: 14px;
                margin-bottom: 0.5rem;
            }
        }
    }

    // Tablet specific styles
    @media (min-width: 641px) and (max-width: 1024px) {
        .modal-content {
            padding: 1.5rem;
            // Ensure proper spacing and alignment across all devices.modal-content {
            min-height: 200px;
        }

        .modal-footer {
            padding: 1rem 1.5rem;
        }
    }


}

.modal-footer {
    background-color: white;
}

// Animation for mobile footer
@keyframes slideUp {
    from {
        transform: translateY(100%);
    }

    to {
        transform: translateY(0);
    }
}

.modal-footer.fixed {
    animation: slideUp 0.3s ease-out;
}