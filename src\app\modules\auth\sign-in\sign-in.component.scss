// Base styles
* {
    font-family: 'Source Sans Pro';
    font-style: normal;
}

// Responsive container
.sing_in_box {
    position: relative;
    max-width: 450px;
    width: 90%;
    margin: auto;
    padding: 20px;
}

// Responsive logo
.responsive-logo {
    width: 200px;
    max-width: 80%;
    height: auto;
}

// Right panel styling
.sign-in-right-panel {
    background-color: #FFFFFF;
    box-shadow: 0px 4px 10px rgba(123, 134, 153, 0.1);
    z-index: 99;
    min-height: 100vh;
}

// Form inputs responsive styling
.input-height {
    height: 40px !important;
    max-height: 40px !important;

    .p-inputtext,
    .p-password,
    .p-password-input {
        height: 40px !important;
        max-height: 40px !important;
        width: 100%;
    }
}

// OTP wrapper responsive
.opt-wrapper {
    width: 100%;
    max-width: 400px;
    margin: 2rem auto;
    padding: 1.5rem;
    border: 1px solid lightgray;
    border-radius: 10px;
}

// Media queries for different screen sizes
@media (max-width: 768px) {
    .sing_in_box {
        width: 95%;
        padding: 15px;
    }

    .signIn-text {
        font-size: 1.5rem;
    }

    .opt-wrapper {
        width: 95%;
        padding: 1rem;
    }
}

@media (max-width: 480px) {
    .sing_in_box {
        width: 100%;
        padding: 10px;
    }

    .signIn-text {
        font-size: 1.25rem;
    }

    .title-lable {
        font-size: 40px;
        line-height: 50px;
    }

    .portal_title {
        font-size: 24px;
    }
}

// Left side styling (visible only on larger screens)
.left_side_sign_in_wrapper {
    background: linear-gradient(90deg, #265DAB 12.65%, #2B75BC 30.36%, #3F8CC6 48.58%);

    @media (max-width: 768px) {
        display: none;
    }
}

// Button styling
.tps-button-wrapper {
    width: 100%;
    height: 40px;
    border-radius: 4px;
    background-color: #517EBC;
    color: white;
    border: none;
    cursor: pointer;

    &:disabled {
        background-color: #A8AFBD;
        cursor: not-allowed;
    }
}

// Messages and alerts
p-message {
    width: 100%;
    margin-bottom: 1rem;
}

.signIn-label {
    font-family: 'Source Sans Pro' !important;
    font-style: normal;
    font-weight: 600;
    font-size: 32px;
    color: #E7EAF0;
}

.signIn-text {
    font-family: 'Source Sans Pro';
    font-style: normal;
    font-weight: 600;
    font-size: 2rem;
    color: #000000;
}

.signIn_text_sub_title {
    font-family: 'Source Sans Pro';
    font-style: normal;
    font-weight: 600;
    font-size: 1rem;
    color: #000000;
}

.main-label {
    font-family: 'Source Sans Pro';
    font-style: normal;
    font-weight: 600;
    font-size: 4.5rem;
    color: #E7EAF0;
}

.main-label_slogan {
    font-family: 'Source Sans Pro';
    font-style: normal;
    font-weight: 600;
    font-size: 1rem;
    // line-height: 101px;
    // color: #E7EAF0;
    color: #000000;
    white-space: pre-line;
    text-align: center;
}

.main-label1 {
    font-family: 'Source Sans Pro';
    font-style: normal;
    font-weight: 600;
    font-size: 5rem;
    // line-height: 101px;
    color: #E7EAF0;
    margin-left: 6rem;
}

.copy-right {
    font-weight: 600;
    font-size: 1.25rem;
    color: #000;
    text-align: center;
    // color: #E7EAF0;

}

.sing_in_wrapper {
    border: 1px solid lightpink;
    border-radius: 20px;
    box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
    padding: 10px;
}

.sign-title-hint {
    font-family: 'Source Sans Pro';
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 20px;
    /* Primary Red/Neutrals/500 */
    color: #7B8699;
    margin-bottom: 1.8rem;
}

.keep {
    color: #7B8699;
}

.text-white {
    color: #FFF;
}

.title-lable {
    font-family: 'Source Sans Pro';
    font-style: normal;
    font-weight: 600;
    font-size: 80px;
    line-height: 101px;
}



.sing_in_box {
    position: relative;
    max-width: 80%;
    min-width: 65%;
    margin: auto;
}

.left_side_top_pattern {
    width: 168px;
    height: 168px;
    position: absolute;
    top: 5%;
    left: 4%;
}

.right_side_bottom_pattern {
    width: 168px;
    height: 168px;
    position: absolute;
    bottom: 5%;
    right: 43%;
}

.welcome-text {
    font-weight: 600;
    font-size: 32px;
    line-height: 40px;
    color: #E7EAF0;
}

.portal_title {
    font-weight: 600;
    font-size: 36px;
    // line-height: 101px;
    color: #E7EAF0;

}

.hyperlinks {
    position: absolute;
    left: 6%;
    bottom: 7%;
    font-weight: 400;
    font-size: 16px;
    line-height: 20px;
    color: #F8F9FD;
}

.sign_main_title {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-left: 6%;
    height: 100%;

    .slogan_text {
        font-weight: 600;
        font-size: 24px;
        line-height: 40px;
        color: #E7EAF0;
        margin-left: 10%;
    }
}

.right_middle_side_top_pattern {
    position: absolute;
    width: 402px;
    height: 373px;
    left: 42%;
    top: -8%;
}

.sign-check-box {
    box-sizing: border-box;
    position: absolute;
    width: 20px;
    height: 20px;
    background: #FFFFFF;
    border: 1px solid #7B8699;
    border-radius: 4px;
}

.keep-me-text {
    font-weight: 400;
    font-size: 16px;
    line-height: 20px;
    color: #7B8699;
    position: absolute;
}




.login-btn-color {
    background-color: #517EBC;
}

.contact-us {
    list-style-type: disc;
    padding-left: 0px !important;
}

.copyright-symbol {
    font-size: 26px;
}

.p-password {
    width: 100%;
}

.opt-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 12px;
    width: 400px;
    margin: auto;
    margin-top: 4rem;
    // background-color: rgba(241,245,249,1);
    border: 1px solid lightgray;
    padding: 2rem;
    border-radius: 10px;

    .otp-register_title {
        font-size: 18px;
        font-weight: bold;
        text-align: center;
    }

    .otp-title-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: center;

        .otp-title {
            font-size: 20px;
            font-weight: bold;
        }
    }


    .backToLoginPage {
        display: flex;
        flex-direction: row;
        justify-content: center;
        cursor: pointer;
    }
}


button.button_disabled {
    background-color: #A8AFBD;
    color: white !important;
    cursor: not-allowed;
}

.p-password {
    .p-password-input {
        border-radius: 0px 6px 6px 0px;
    }
}

.input-height {
    height: 40px !important;
    max-height: 40px !important;

    .p-inputtext {
        height: 40px !important;
        max-height: 40px !important;

    }

    .p-inputtext:enabled:focus {
        height: 40px !important;
        max-height: 40px !important;
    }

    .p-password {
        height: 40px;
        max-height: 40px;
    }

    .p-password:enabled:focus {
        height: 40px;
        max-height: 40px;
    }
}

.opt-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 12px;
    width: 400px;
    margin: 0 auto;
    margin-top: 4rem;
    // background-color: rgba(241,245,249,1);
    border: 1px solid lightgray;
    padding: 2rem;
    border-radius: 10px;

    .otp-register_title {
        font-size: 18px;
        font-weight: bold;
        text-align: center;
    }

    .otp-title-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: center;

        .otp-title {
            font-size: 20px;
            font-weight: bold;
        }
    }


    .backToLoginPage {
        display: flex;
        flex-direction: row;
        justify-content: center;
        cursor: pointer;
    }
}


.left_side_top_partner {
    // position: absolute;
    // top: 2rem;
    // left: 2rem;
    width: 10rem;
    //height: 5rem;
}

.right_side_top_company {
    // position: absolute;
    // top: 2rem;
    // left: 50%;
    width: 9rem;
    height: 3.5rem;
    margin-right: 1rem;
    background-color: white;
    padding: 2px;
    border-radius: 5px;
}

.signin_footer {
    position: absolute;
    bottom: -40%;
    font-weight: 600;
}


////////


.copy-right {
    font-weight: 400;
    font-size: 16px;
    color: #E7EAF0;
}

// Mobile specific styles
@media (max-width: 767px) {
    .welcome-text {
        background: linear-gradient(90deg, #265DAB 12.65%, #2B75BC 30.36%, #3F8CC6 48.58%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
    }

    .project-title {
        h1 {
            color: #265DAB;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        }
    }

    .project-subtitle {
        p {
            background: linear-gradient(90deg, #265DAB 12.65%, #2B75BC 30.36%, #3F8CC6 48.58%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
            font-weight: 500;
        }
    }

    .sing_in_box {
        background: white;
        border-radius: 16px;
        padding: 2rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
            0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .responsive-logo {
        max-width: 180px;
        height: auto;
    }
}