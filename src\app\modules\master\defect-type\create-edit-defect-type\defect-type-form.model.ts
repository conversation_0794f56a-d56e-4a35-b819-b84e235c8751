import { FORM_CONTROL_TYPES, FORM_FIELDS_CONSTANTS_VALUES, ICON_BUTTON_COLOR } from 'app/shared/tapas-ui';

export const DEFECT_TYPE_FORM_MODEL = {
    code: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Code",
        placeholder: 'Ex. AUD001',
        topGroupTitleIcon: 'pi-user',
        severity: ICON_BUTTON_COLOR.WARNING,
        topGroupTitle: "Auditor Details",
        show: true,
        rules: {
            required: true,
            maxLength: 250,
        }
    },
    name: {
       type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Name",
        placeholder: 'Ex. John',
        show: true,
        rules: {
            required: true,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_NUMERIC_PATTERN_WITH_SPACE,
            maxLength: 250,
        }
    },
    question: {
       type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Question",
        placeholder: 'Ex. is defect present?',
        show: true,
        rules: {
            required: false,
            maxLength: 250,
        }
    },
    shortQuestion: {
       type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Short Question",
        placeholder: 'Ex. is defect present?',
        show: true,
        rules: {
            required: false,
            maxLength: 250,
        }
    },
    minLength: {
       type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Min Length",
        placeholder: '',
        show: true,
        rules: {
            required: false,
            maxLength: 250,
        }
    },
    maxLength: {
       type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Max Length",
        placeholder: '',
        show: true,
        rules: {
            required: false,
            maxLength: 250,
        }
    },
    description: {
       type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Description",
        placeholder: '',
        show: true,
        rules: {
            required: false,
            maxLength: 250,
        }
    }
}