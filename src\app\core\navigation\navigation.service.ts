import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Navigation } from 'app/core/navigation/navigation.types';
import { BehaviorSubject, Observable, ReplaySubject, Subject, tap } from 'rxjs';

import { NavigationMockApi } from './api';

@Injectable({ providedIn: 'root' })
export class NavigationService {
    private _httpClient = inject(HttpClient);
    private _navigation: ReplaySubject<Navigation> = new ReplaySubject<Navigation>(1);
    constructor(private navigationMockApi: NavigationMockApi,
        public _router: Router,
    ) { }

    // -----------------------------------------------------------------------------------------------------
    // @ Accessors
    // -----------------------------------------------------------------------------------------------------

    /**
     * Getter for navigation
     */
    get navigation$(): Observable<Navigation> {
        this._navigation.next(this.navigationMockApi.registerHandlers()?.navigation);
        return this._navigation.asObservable()
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Get all navigation data
     */
    get(): Observable<Navigation> {
        return this._httpClient.get<Navigation>('api/common/navigation').pipe(
            tap((navigation) => {
                this._navigation.next(this.navigationMockApi.registerHandlers());
            }),
        );
    }
    menuItem: any;
    navigation: any[] = [];
    public prepareRoutes(url?: string): void {
        this.navigation = this.navigationMockApi.registerHandlers()?.navigation;
        if (!url) {
            this.navigation.forEach(item => {
                if (item.children) {
                    item.children.forEach(child => {
                        if (child.children) {
                            child.children.forEach(innerChild => {
                                if (innerChild.link == this._router.url) {
                                    this.menuItem = [];
                                    this.menuItem.push({ type: item.type, id: item.id, label: item.title });
                                    this.menuItem.push({ type: child.type, id: child.id, label: child.title });
                                    this.menuItem.push({ type: innerChild.type, id: innerChild.id, label: innerChild.title, routerLink: this._router.url });
                                    return;
                                }
                            })
                        }
                        if (child.link == this._router.url) {
                            this.menuItem = [];
                            this.menuItem.push({ type: item.type, id: item.id, label: item.title });
                            this.menuItem.push({ type: child.type, id: child.id, label: child.title, routerLink: this._router.url });
                            return;
                        }
                    })
                } else {
                    if (item.link == this._router.url) {
                        this.menuItem = [];
                        this.menuItem.push({ type: item.type, id: item.id, label: item.title, routerLink: this._router.url });
                        return;
                    }
                }
            });
        } else {
            this.navigation.forEach(item => {
                if (item.children) {
                    item.children.forEach(child => {
                        if (child.children) {
                            child.children.forEach(innerChild => {
                                if (innerChild.link == url) {
                                    this.menuItem = [];
                                    this.menuItem.push({ type: item.type, id: item.id, label: item.title });
                                    this.menuItem.push({ type: child.type, id: child.id, label: child.title });
                                    this.menuItem.push({ type: innerChild.type, id: innerChild.id, label: innerChild.title, routerLink: url });
                                    return;
                                }
                            })
                        }
                        if (child.link == url) {
                            this.menuItem = [];
                            this.menuItem.push({ type: item.type, id: item.id, label: item.title });
                            this.menuItem.push({ type: child.type, id: child.id, label: child.title, routerLink: url });
                            return;
                        }
                    })
                } else {
                    if (item.link == url) {
                        this.menuItem = [];
                        this.menuItem.push({ type: item.type, id: item.id, label: item.title, routerLink: url });
                        return;
                    }
                }
            });
        }
    }

    menuActivated: Subject<any> = new Subject()
    public setAtiveMenu(item): void {
        this.menuActivated.next(item);
    }
}
