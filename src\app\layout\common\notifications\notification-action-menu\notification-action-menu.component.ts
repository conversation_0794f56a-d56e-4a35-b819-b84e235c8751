import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>plateOutlet } from '@angular/common';
import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { CommonService, InvokeService } from 'app/shared/tapas-ui';
import { ButtonModule } from 'primeng/button';
import { MenuModule } from 'primeng/menu';
import { Subject } from 'rxjs';

@Component({
  selector: 'notification-action-menu',
  standalone: true,
  imports: [MenuModule, ButtonModule],
  templateUrl: './notification-action-menu.component.html',
})
export class NotificationActionMenuComponent implements OnInit, OnDestroy {
  @Input() notification: any;
  @Output() onClickNotificationUpdate: EventEmitter<any> = new EventEmitter();
  private _unsubscribeAll: Subject<any> = new Subject<any>();
  notificationActionMenuItems = [];

  constructor(
    private _commonService: CommonService,
    private _invokeService: InvokeService,
  ) {
  }


  public ngOnInit(): void {
    if (this.notification.readStatus == 0) {
      this.notificationActionMenuItems = [
        {
          label: 'Mark as a read', icon: 'pi pi-check-circle',
          command: (event) => {
            this.onUpdateNotification();
          }
        },
        // { label: 'Search', icon: 'pi pi-search' }
      ];
    } else {
      this.notificationActionMenuItems = [
        {
          label: 'Mark as a unread', icon: 'pi pi-times-circle',
          command: (event) => {
            this.onUpdateNotification();
          }
        },
        // { label: 'Search', icon: 'pi pi-search' }
      ];
    }
  }

  public onUpdateNotification(): void {
    this.onClickNotificationUpdate.emit(this.notification);
  }
  /**
     * On destroy
     */
  public ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this._unsubscribeAll.next(null);
    this._unsubscribeAll.complete();
  }
}

