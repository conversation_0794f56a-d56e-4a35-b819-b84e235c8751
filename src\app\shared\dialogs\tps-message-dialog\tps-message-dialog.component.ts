import { CommonModule, NgIf } from '@angular/common';
import { Component } from '@angular/core';
import { TpsPrimaryButtonComponent } from 'app/shared/tapas-ui';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { MessageModule } from 'primeng/message';

export class MessageConfig {
    title: string;
    message: string;
    messageType: string;
}
@Component({
    selector: 'tps-message-dialog',
    standalone: true,
    imports: [TpsPrimaryButtonComponent, MessageModule, NgIf, CommonModule],
    templateUrl: './tps-message-dialog.component.html'
})
export class TpsMessageDialogComponent {

    public messageConfig: MessageConfig;
    constructor(
        private _dialogConfig: DynamicDialogConfig,
        public _config: DynamicDialogRef
    ) {
        this.messageConfig = this._dialogConfig.data;
    }

    public getIconByMessageType(messageType: string): string {
        switch (messageType?.toLowerCase()) {
            case 'success':
                return 'pi pi-check-circle';
            case 'error':
                return 'pi pi-times-circle';
            case 'warning':
                return 'pi pi-exclamation-triangle';
            case 'info':
                return 'pi pi-info-circle';
            case 'in-progress':
                return 'pi pi-spinner pi-spin';
            default:
                return 'pi pi-info-circle';
        }
    }

    public close(): void {
        this._config.close(false);
    }
}
