import { CommonModule, <PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormGroup, FormGroupDirective, ReactiveFormsModule } from '@angular/forms';
import { CommonService } from 'app/shared/tapas-ui';
import { SelectModule } from 'primeng/select';
import { TooltipModule } from 'primeng/tooltip';

import { FORM_CONTROL_TYPES } from '../../constants/tps-enum-constants';
import { TpsErrorComponent } from '../tps-error/tps-error.component';

class tpsSingleSelect {
  type: string
  fieldName: string
  tooltip?: string
  options?: []
  hint?: string
  value: any
  label: string
  placeholder: string
  show: boolean;
  onChange?: (field) => void
  rules: {
    required?: boolean,
  }
}

@Component({
  selector: 'tps-single-select',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, NgIf, TpsErrorComponent, SelectModule, TooltipModule],
  templateUrl: './tps-single-select.component.html'
})
export class TpsSingleSelectComponent implements OnInit {
  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  @Input() field: tpsSingleSelect;
  @Input() formName: FormGroup;
  @Input() layout: string = 'row';
  @Input() showLabel: boolean = true;
  @Output() onChange: EventEmitter<any> = new EventEmitter();

  constructor(
    private _commonService: CommonService,
    private formgroupDirective: FormGroupDirective) {
    this.formName = formgroupDirective.control;
  }

  ngOnInit(): void {

  }
  public sortDropDownOptions(options: any[]): any[] {
    return options?.sort((a, b) => {
      return a?.label?.localeCompare(b?.label, undefined, { numeric: true });
    })
  }


  public onChangeField(event, field): void {
    this.onChange.emit(event);
  }
}
