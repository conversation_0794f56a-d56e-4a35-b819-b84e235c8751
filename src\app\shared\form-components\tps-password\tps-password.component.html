<form [formGroup]="formName" autocomplete="off">
    <div class="row mb-2">
        <div class="col-md-5">
            <div class="flex flex-row items-center gap-2">
                <label for="{{field.label}}">{{field.label}}
                    <span *ngIf="field?.rules?.required && !formName.controls[field.fieldName].disabled"
                        class="requiredField">*</span>
                </label>
                <i *ngIf="field?.tooltip" [pTooltip]="field?.tooltip" tooltipPosition="bottom"
                    class="cursor-pointer pi pi-question-circle helping_text_icon"></i>
            </div>
        </div>
        <div class="col-md-7">
            <div class="w-full flex flex-column gap-1">
                <p-password class="w-full tps-input" variant="outlined" id="{{field.label}}"
                    [placeholder]="field.placeholder" [formControlName]="field.fieldName"
                    [required]="field?.rules?.required" [minlength]="field?.rules?.minLength"
                    [maxlength]="maxLength" [pattern]="field?.rules?.pattern"
                    [disabled]="formName.controls[field.fieldName].disabled"
                    [class.ng-invalid]="formName.controls[field.fieldName].invalid"
                    [class.ng-dirty]="(formName.controls[field.fieldName].dirty || formName.controls[field.fieldName].touched)"
                    [feedback]="false" [toggleMask]="formName.controls[field.fieldName].value" required />
                <small *ngIf="field?.hint">{{field?.hint}}</small>
                <tps-error [fieldName]="field.fieldName" [label]="field.label" [formName]="formName"></tps-error>
            </div>
        </div>
    </div>
</form>