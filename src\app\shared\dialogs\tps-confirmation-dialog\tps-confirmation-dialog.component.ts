import { NgIf } from '@angular/common';
import { Component } from '@angular/core';
import { TpsPrimaryButtonComponent, TpsSecondaryButtonComponent } from 'app/shared/tapas-ui';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';

@Component({
  selector: 'tps-confirmation-dialog',
  standalone: true,
  imports: [TpsPrimaryButtonComponent, TpsSecondaryButtonComponent, NgIf],
  templateUrl: './tps-confirmation-dialog.component.html',
  styleUrl: './tps-confirmation-dialog.component.scss',
})
export class TpsConfirmationDialogComponent {

  message: string;
  extraString: string;
  constructor(
    private _dialogConfig: DynamicDialogConfig,
    public _config: DynamicDialogRef
  ) {
    this.message = this._dialogConfig.data.message;
    this.extraString = this._dialogConfig.data.extraString;
  }
  public accept(): void {
    this._config.close(true)
  }
  public reject(): void {
    this._config.close(false)
  }
}
