export class actionType {
    type: string
    icon: string
    title: string
    severity: string
    data: any
}

export class buttonActions {
    name: string
    icon: string
    primary?: boolean
    secondary?: boolean
    isSvg: boolean
    show: boolean
    command: (action) => void
}

export class toolbarActions {
    name: string
    icon: string
    primary?: boolean
    secondary?: boolean
    show: boolean
    command: (action) => void
}
export class QuickFilter {
    type: string
    fieldStatus: string
    value: any
    label: string
    placeholder: string
    command: (action) => void
}
