import { FORM_CONTROL_TYPES } from '../../constants/tps-enum-constants';


export const DISCOVER_CAMERA_FORM_MODEL = {

    facilityUid: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
         onChange: (event) => { },
        options: [],
        value: "",
        label: "Facility",
        placeholder: 'Facility',
        show: false,
        rules: {

        },
    },
    userName: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "User Name",
        placeholder: 'User Name',
        show: false,
        tooltip: 'Camera Username',
        rules: {
            maxLength: 255,
        },
    },
    password: {
        type: FORM_CONTROL_TYPES.PASSWORD,
        value: "",
        label: "Password",
        placeholder: 'Password',
        show: false,
        tooltip: 'Camera Password',
        rules: {
            maxLength: 255,
        },

    },

}
