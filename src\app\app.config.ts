import { provideHttpClient } from '@angular/common/http';
import { ApplicationConfig, importProvidersFrom, isDevMode } from '@angular/core';
import { provideAnimations } from '@angular/platform-browser/animations';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { PreloadAllModules, provideRouter, withInMemoryScrolling, withPreloading } from '@angular/router';
import { provideServiceWorker } from '@angular/service-worker';
import { provideFuse } from '@fuse';
import Aura from '@primeng/themes/aura';
import { appRoutes } from 'app/app.routes';
import { provideAuth } from 'app/core/auth/auth.provider';
import { MQTT_SERVICE_OPTIONS, MqttModule } from 'ngx-mqtt';
import { ConfirmationService } from 'primeng/api';
import { providePrimeNG } from 'primeng/config';
import { DialogService } from 'primeng/dynamicdialog';

import { PushNotificationsService } from './shared/services/push-notifications.service';

export const MQTT_TOPIC_NAMES = {
    DEDUP_FRAME: 'DEDUP_FRAME',
    THREAT_DETECT: 'mqtt-topic',
    FIRE_DETECT: 'FIRE_DETECT',
    ANPR_NEW_VEHICLE: 'mqtt-topic',
    INBOX: 'INBOX',
    HUMAN_ACTIVITY_DASHBOARD: 'HUMAN_ACTIVITY_DASHBOARD',
    THREAT_DETECT_HISTORICAL: 'THREAT_DETECT_HISTORICAL'
}

export const appConfig: ApplicationConfig = {
    providers: [
        //primeng service
        provideAnimationsAsync(),
        providePrimeNG({
            theme: {
                preset: Aura
            }
        }),
        provideServiceWorker('ngsw-worker.js', {
            enabled: !isDevMode(),
            registrationStrategy: 'registerImmediately'
        }),
        provideAnimations(),
        provideHttpClient(),
        provideRouter(appRoutes,
            withPreloading(PreloadAllModules),
            withInMemoryScrolling({ scrollPositionRestoration: 'enabled' }),
        ),
        PushNotificationsService,

        ConfirmationService,
        importProvidersFrom(MqttModule.forRoot(MQTT_SERVICE_OPTIONS)),
        DialogService,
        // Fuse
        provideAuth(),
        // provideIcons(),
        provideFuse({
            fuse: {
                layout: 'modern',
                scheme: 'light',
                screens: {
                    sm: '600px',
                    md: '960px',
                    lg: '1280px',
                    xl: '1440px',
                },
                theme: 'theme-default',
                themes: [
                    {
                        id: 'theme-default',
                        name: 'Default',
                    },
                    {
                        id: 'theme-brand',
                        name: 'Brand',
                    },
                    {
                        id: 'theme-teal',
                        name: 'Teal',
                    },
                    {
                        id: 'theme-rose',
                        name: 'Rose',
                    },
                    {
                        id: 'theme-purple',
                        name: 'Purple',
                    },
                    {
                        id: 'theme-amber',
                        name: 'Amber',
                    },
                ],
            },
        }),
    ],
};
