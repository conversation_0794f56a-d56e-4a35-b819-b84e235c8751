import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Ng<PERSON><PERSON><PERSON><PERSON> } from '@angular/common';
import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';

import {
  FORM_CONTROL_TYPES,
  TABLE_ACTION_TYPES,
  TpsCheckboxComponent,
  TpsDatePickerComponent,
  TpsMultiFormFieldComponent,
  TpsMultiSelectComponent,
  TpsNumberComponent,
  TpsPasswordComponent,
  TpsRadioComponent,
  TpsSelectedButtonComponent,
  TpsSingleSelectComponent,
  TpsSliderComponent,
  TpsTextAreaComponent,
  TpsTextComponent,
  TpsTextSearchComponent,
  TpsToggleButtonComponent,
  TpsToggleSwitchComponent,
} from '../../tapas-ui';

@Component({
  selector: 'tps-form',
  standalone: true,
  imports: [
    <PERSON><PERSON><PERSON>,
    NgI<PERSON>,
    Ng<PERSON><PERSON>,
    Ng<PERSON>witchCase,
    Ng<PERSON>lass,
    FormsModule,
    ReactiveFormsModule,
    TpsCheckboxComponent,
    TpsDatePickerComponent,
    TpsMultiSelectComponent,
    TpsNumberComponent,
    TpsRadioComponent,
    TpsSingleSelectComponent,
    TpsTextComponent,
    TpsTextAreaComponent,
    TpsTextSearchComponent,
    TpsPasswordComponent,
    TpsMultiFormFieldComponent,
    TpsToggleSwitchComponent,
    TpsSliderComponent,
    TpsToggleButtonComponent,
    TpsSelectedButtonComponent,
    ButtonModule
  ],
  templateUrl: './tps-form-layout.component.html',
  styleUrl: './tps-form-layout.component.scss'
})
export class TpsFormLayoutComponent implements OnInit, OnDestroy {
  @Input() formGroup: FormGroup;
  @Input() fields: any[] = [];
  @Input() status: string;
  @Input() layout: 'column' | 'row' = 'row';
  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  public TABLE_ACTION_TYPES = TABLE_ACTION_TYPES;


  constructor() { }

  public ngOnInit(): void {

  }

  public ngOnDestroy(): void {

  }
}
