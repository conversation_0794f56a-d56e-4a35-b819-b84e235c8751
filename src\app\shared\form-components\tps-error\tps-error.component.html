<div class="flex flex-col gap-1"
    *ngIf="formName.controls[fieldName].invalid && (formName.controls[fieldName].dirty || formName.controls[fieldName].touched)">
    <small *ngIf="formName.controls[fieldName].errors?.required" class="error-message">{{label}} is
        required
    </small>
    <small *ngIf="formName.controls[fieldName].errors?.minlength" class="error-message">
        {{label}} should be {{formName.controls[fieldName].errors?.minlength?.requiredLength}} characters/digits
    </small>
    <small *ngIf="formName.controls[fieldName].errors?.maxlength" class="error-message">
        {{label}} should not more than {{formName.controls[fieldName].errors?.maxlength?.requiredLength}}
        characters/digits
    </small>
    <small *ngIf="formName.controls[fieldName].errors?.min" class="error-message">
        {{label}} should be more than {{formName.controls[fieldName].errors?.min.min}}
    </small>
    <small *ngIf="formName.controls[fieldName].errors?.max" class="error-message">
        {{label}} should not more than {{formName.controls[fieldName].errors?.max.max}}
    </small>
    <small *ngIf="formName.controls[fieldName].errors?.pattern" class="error-message">
        {{label}} is invalid
    </small>
</div>