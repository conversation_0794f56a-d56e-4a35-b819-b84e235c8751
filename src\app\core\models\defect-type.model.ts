
export interface iDefectType {
  category?: string;
  defectNature?: string;
  createdDate?: number;  // 
  modifiedDate?: number;
  createdDateText?: string; // For display purposes
  code?: string;
  createdBy?: string;
  createdTime?: number;
  deleted?: number;
  description?: string;
  id?: number;
  modifiedBy?: string;
  modifiedTime?: number;
  name?: string;
  parentTenantUid?: string;
  status?: number;
  tenantId?: number;
  tenantUid?: string;
  uuid?: string;
  criticalDefect?: Defect;
  majorDefect?: Defect;
  minorDefect?: Defect;
  response?: Defect[];
  question?: string;
  shortQuestion?: string;
  defectTypeCategoryUid?: string;
  maxLength?: any;
  minLength?: any;
  mandatory: number;
};

export class DefectType implements iDefectType {
  public code?: string;
  public id?: number;
  public category?: string;
  public name?: string;
  public description?: string;
  public defectNature?: string;
  public createDate?: number;
  public modifiedDate?: number;
  public status?: number;
  public uuid?: string;
  public createdBy?: string;
  public createdTime?: number;
  public deleted?: number;
  public modifiedBy?: string;
  public modifiedTime?: number;
  public parentTenantUid?: string;
  public tenantId?: number;
  public tenantUid?: string;
  public criticalDefect?: Defect;
  public majorDefect?: Defect;
  public minorDefect?: Defect;
  public response?: Defect[];
  public question?: string;
  public shortQuestion?: string;
  public defectTypeCategoryUid?: string;
  public maxLength?: any;
  public minLength?: any;
  public mandatory: number
};

export class Defect {
  auditId?: string;
  category?: string;
  categoryCode?: string;
  comments?: string;
  count?: number;
  createdBy?: string;
  createdTime?: number;
  defectCode?: string;
  defectTypeCategoryName?: string;
  defectTypeCategoryUid?: string;
  defectTypeName?: string;
  defectTypeUid?: string;
  deleted?: number;
  designcode?: string;
  designcodeUid?: string;
  highlight?: true;
  id?: number;
  imageUrl?: string;
  modifiedBy?: string;
  modifiedTime?: number;
  parentTenantUid?: string;
  sampleId?: number;
  severity?: string;
  tenantId?: number;
  tenantUid?: string;
  uuid?: string;
}

export const FORM_IDS = {
  "PACAUDIT": {
    PACAUDIT_NO_OF_PACKAGES_ID: "1.2.1.1",
    SAMPLE_SIZE_ID: "1.2.1.2",
    ALLOWED_CRITICAL_ID: "1.2.1.3",
    ALLOWED_MAJOR_ID: "1.2.1.4",
    ALLOWED_MINOR_ID: "1.2.1.5",
    FOUND_CRITICAL_ID: "1.2.1.6",
    FOUND_MAJOR_ID: "1.2.1.7",
    FOUND_MINOR_ID: "1.2.1.8",
    RESULT_ID: "1.2.1.10",
  },
  "MEASUREMENT": {
    MEASUREMENT_NO_OF_SKU_ID: "1.2.2.1",
    SAMPLE_SIZE_ID: "1.2.2.2",
    ALLOWED_CRITICAL_ID: "1.2.2.3",
    ALLOWED_MAJOR_ID: "1.2.2.4",
    ALLOWED_MINOR_ID: "1.2.2.5",
    FOUND_CRITICAL_ID: "1.2.2.6",
    FOUND_MAJOR_ID: "1.2.2.7",
    FOUND_MINOR_ID: "1.2.2.8",
    RESULT_ID: "1.2.2.10",
  },
  "VISUALQUALITY": {
    VISUAL_QUALITY_QUANTITY_ID: "1.2.3.1",
    SAMPLE_SIZE_ID: "1.2.3.2",
    ALLOWED_CRITICAL_ID: "1.2.3.3",
    ALLOWED_MAJOR_ID: "1.2.3.4",
    ALLOWED_MINOR_ID: "1.2.3.5",
    FOUND_CRITICAL_ID: "1.2.3.6",
    FOUND_MAJOR_ID: "1.2.3.7",
    FOUND_MINOR_ID: "1.2.3.8",
    RESULT_ID: "1.2.3.10",
    SUMMARY_VISUAL_AQL_ID: "1.2.3.11",
  }
}

