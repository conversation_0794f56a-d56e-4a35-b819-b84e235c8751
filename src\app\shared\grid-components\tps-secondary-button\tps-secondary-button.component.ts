import { Ng<PERSON><PERSON>, NgIf } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'tps-secondary-button',
  standalone: true,
  imports: [NgIf],
  templateUrl: './tps-secondary-button.component.html',
  styleUrl: './tps-secondary-button.component.css'
})
export class TpsSecondaryButtonComponent implements OnInit {
  @Input() isDisabled: boolean = false
  @Input() buttonName: string = '';
  @Input() icon: string = '';
  @Input() isSvg: boolean = false;

  @Output() onClick: EventEmitter<any> = new EventEmitter();

  constructor() { }
  public ngOnInit(): void {
    
  }

  public onButtonClick(): void {
    this.onClick.emit(true);
  }
}
