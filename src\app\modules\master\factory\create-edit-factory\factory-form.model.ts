import { FORM_CONTROL_TYPES, FORM_FIELDS_CONSTANTS_VALUES, ICON_BUTTON_COLOR } from 'app/shared/tapas-ui';

export const FACTORY_FORM_MODEL = {
    code: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Code",
        placeholder: 'Ex. FCT001',
        topGroupTitleIcon: 'pi-building',
        severity: ICON_BUTTON_COLOR.WARNING,
        topGroupTitle: "Factory Details",
        show: true,
        rules: {
            required: true,
            maxLength: 250,
        }
    },
    name: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Name",
        placeholder: 'Ex. Factory Name',
        show: true,
        rules: {
            required: true,
            maxLength: 250,
        }
    },
    email: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Email",
        placeholder: 'Ex. <EMAIL>',
        topGroupTitle: 'Contact Details',
        topGroupTitleIcon: 'pi-phone',
        severity: ICON_BUTTON_COLOR.WARNING,
        show: true,
        rules: {
            required: true,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.EMAIL_PATTERN,
            maxLength: 250,
        }
    },
    altemail: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Alternative Email",
        placeholder: 'Ex. <EMAIL>',
        show: true,
        rules: {
            pattern: FORM_FIELDS_CONSTANTS_VALUES.EMAIL_PATTERN,
            maxLength: 250,
        }
    },
    contactNo: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Contact No.",
        placeholder: 'Ex. 1234567890',
        show: true,
        rules: {
            required: true,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.NUMERIC_PATTERN,
        }
    },
    addressLine1: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Address",
        placeholder: 'Ex. 123 Main St',
        topGroupTitle: 'Address',
        topGroupTitleIcon: 'pi-map-marker',
        severity: ICON_BUTTON_COLOR.HELP,
        show: true,
        rules: {
            maxLength: 250,
            required: true,
        }
    },
    city: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "City",
        placeholder: 'Ex. New York',
        show: true,
        rules: {
            maxLength: 250,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_PATTERN,
            required: true,
        }
    },
    pincode: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Pincode",
        placeholder: 'Ex. 123456',
        show: true,
        rules: {
            pattern: FORM_FIELDS_CONSTANTS_VALUES.NUMERIC_PATTERN,
            maxLength: 10,
            required: true,
        }
    },
    country: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Country",
        placeholder: 'Ex. USA',
        show: true,
        rules: {
            maxLength: 250,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_PATTERN,
            required: true,
        }
    },
    factoryAuditScore: {
        type: FORM_CONTROL_TYPES.NUMBER,
        value: "",
        label: "Audit Score",
        placeholder: 'Ex. 100',
        topGroupTitleIcon: 'pi-align-left',
        severity: ICON_BUTTON_COLOR.WARNING,
        topGroupTitle: "Other Information",
        show: true,
        onChange: (event) => { },
        rules: {
            pattern: FORM_FIELDS_CONSTANTS_VALUES.NUMERIC_PATTERN,
            max:100,
            min:0,
            required: true,
        }
    },
    factoryAuditDate: {
        type: FORM_CONTROL_TYPES.DATE,
        value: "",
        label: "Audit Date",
        placeholder: 'Ex. 10/10/2021',
        show: true,
        onChange: (event) => { },
        rules: {
            required: true,
        }
    },
    auditRating: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        onChange: (event) => { },
        options: [],
        value: "",
        label: "Audit Rating",
        placeholder: 'Select Audit Rating',
        show: true,
        rules: {
            required: true,
        }
    },
    validUpto: {
        type: FORM_CONTROL_TYPES.DATE,
        fieldStatus: FORM_CONTROL_TYPES.TEXT_SEARCH,
        value: "",
        label: "Valid Upto",
        placeholder: "DD/MM/YYYY",
        show: true,
        showTime: false,
         onChange: (event) => { },
        rules: {
            required: true,
        },
    },
    rawMaterialQualityAssurance: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Raw Material Quality Assurance",
        placeholder: '',
        show: true,
        rules: {
            required: true,
            maxLength: 250,
        }
    },
    preProductionActivity: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Pre Production Activity",
        placeholder: '',
        show: true,
        rules: {
            required: true,
            maxLength: 250,
        }
    },
    markerSpreadingCutting: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Marker / Spreading / Cutting",
        placeholder: '',
        show: true,
        rules: {
            required: true,
            maxLength: 250,
        }
    },
    productionAssembly: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Production / Assembly",
        placeholder: '',
        show: true,
        rules: {
            required: true,
            maxLength: 250,
        }
    },
    finishingAndPackaging: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Finishing & Packaging",
        placeholder: '',
        show: true,
        rules: {
            required: true,
            maxLength: 250,
        }
    },
    internalFinalQualityAudit: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Internal Final Quality Audit",
        placeholder: '',
        show: true,
        rules: {
            required: true,
            maxLength: 250,
        }
    },
    qualityManagemnet: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Quality Managemnet",
        placeholder: '',
        show: true,
        rules: {
            required: true,
            maxLength: 250,
        }
    },
    basicConditionEvaluation: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Basic Condition Evaluation",
        placeholder: '',
        show: true,
        rules: {
            required: true,
            maxLength: 250,
        }
    },
    productQualityAudit: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Product Quality Audit",
        placeholder: '',
        show: true,
        rules: {
            required: true,
            maxLength: 250,
        }
    }




}