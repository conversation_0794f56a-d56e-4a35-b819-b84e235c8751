import { CommonModule, NgF<PERSON>, NgIf } from '@angular/common';
import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { ConfirmationService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { ImageModule } from 'primeng/image';
import { TooltipModule } from 'primeng/tooltip';

import { ICON_BUTTON_SEVERITY, TABLE_ACTION_TYPES } from '../../constants/tps-enum-constants';
import {
  actionType,
  ICON_PRIMENG_LIST,
  TpsCellActionComponent,
  TpsHeaderComponent,
  TpsPrimaryButtonComponent,
  TpsSecondaryButtonComponent,
  TpsTableComponent,
  TpsViewImageComponent,
} from '../../tapas-ui';
import { TpsFileIconComponent } from './tps-file-icon/tps-file-icon.component';

@Component({
  selector: 'tps-file-list',
  standalone: true,
  imports: [NgFor, NgIf, ButtonModule, TooltipModule, TpsHeaderComponent, TpsTableComponent, TpsPrimaryButtonComponent, TpsSecondaryButtonComponent, ImageModule, ConfirmDialogModule, CommonModule],
  templateUrl: './tps-file-list.component.html',
  styleUrl: './tps-file-list.component.scss',
  providers: [ConfirmationService, DynamicDialogRef]
})
export class TpsFileListComponent implements OnInit, OnDestroy {
  hdr: string = 'File List';
  showGridView: boolean = true;
  @Input() fileList: any[] = [];
  @Input() colDefs: any[] = [
    {
      headerName: "Type", 
      field: "name", 
      width: '70px',
      template: TpsFileIconComponent,
      templateParams: (params: any) => params
    },
    { headerName: "Name", field: "name", sortable: true, width: '100px' },
    {
      headerName: "Created On/Modified On", 
      field: "modifiedOnText", 
      sortable: true, 
      width: '100px',
    },
    {
      headerName: "File Size", 
      field: "size", 
      sortable: true,
      width: '100px'
    },
    {
      headerName: 'Actions',
      field: 'actions',
      width: '150px',
      template: TpsCellActionComponent,
      templateParams: (params) => {
        return { 
          actions: this.prepareTableActionIcons(params), 
          onAction: (action) => this.onAction(action) 
        }
      }
    }
  ]
  @Output() onDelete: EventEmitter<any> = new EventEmitter();

  //
  files: any[] = [];
  constructor(
    private confirmationService: ConfirmationService,
    public _config: DynamicDialogRef,
    private _dialogService: DialogService,

  ) { }


  public ngOnInit(): void {
  }

  public prepareTableActionIcons(row): actionType[] {
    let icons: actionType[] = [];
    let type = row.name?.split('.').pop();
    if (type?.includes("jpg") || type?.includes("jpeg") || type?.includes("png")) {
      icons.push({ type: TABLE_ACTION_TYPES.VIEW, icon: ICON_PRIMENG_LIST.PI_EYE, title: 'View', data: row, severity: ICON_BUTTON_SEVERITY.SECONDARY });
    }
    icons.push({ type: TABLE_ACTION_TYPES.DELETE, icon: ICON_PRIMENG_LIST.PI_TRASH, title: 'Delete', data: row, severity: ICON_BUTTON_SEVERITY.DANGER })
    return icons;
  }
  public onAction(action): void {
    switch (action.type) {
      case TABLE_ACTION_TYPES.VIEW:
        this.onViewImageFile(action.data.src);
        break;
      case TABLE_ACTION_TYPES.DELETE:
        this.onDeleteFile(action.data);
        break;
    }
  }

  public getIcon(file): string {
    let icon: string;
    if (file) {
      let type = file.name?.split('.').pop();
      switch (type?.toLowerCase()) {
        case 'pdf':
          icon = 'pdf';
          break;
        case 'jpg':
        case 'jpeg':
        case 'png':
          icon = 'image';
          break;
        default:
          icon = 'file';
          break;
      }
    }
    return `./assets/svg/${icon}.svg`;
  }


  public onLayoutChange(): void {

  }
  public isFileisImageType(file): boolean {
    return file.name?.split('.').pop().toLowerCase() == 'jpg' || file.name?.split('.').pop().toLowerCase()  == 'jpeg' || file.name?.split('.').pop().toLowerCase()  == 'png';
  }

  public onView(file): void {
    let type = file.name?.split('.').pop();
    switch (type.toLowerCase() ) {
      case 'pdf':

        break;
      case 'jpg':
      case 'jpeg':
      case 'png':
        this.onViewImageFile(file.src);
        break;
      default:

        break;
    }
  }

  private onViewImageFile(file): void {
    const ref: DynamicDialogRef = this._dialogService.open(TpsViewImageComponent, {
      data: file,
      header: 'View Image',
      modal: true,
      width: 'auto',
      closeOnEscape: false,
      dismissableMask: false,
      transitionOptions: "300ms",
      baseZIndex: 10000,
    });
    ref.onClose.subscribe((response: any) => {
      if (response) {

      }
    });
  }
  public onDeleteFile(file): void {
    this.confirmationService.confirm({
      header: 'Are you sure?',
      message: 'Please confirm to proceed.',
      accept: () => {
        this.onDelete.emit(file);
      },
      reject: () => {
        this._config.close(false);
      }
    })

  }
  public ngOnDestroy(): void {

  }
}
