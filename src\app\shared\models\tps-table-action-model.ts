export class actionType {
    type: string
    icon: string
    title: string
    severity: string
    data: any
    isSvg?: boolean
    onAction?: (action) => void
    
}

export class buttonActions {
    name: string
    icon: string
    primary?: boolean
    secondary?: boolean
    isSvg: boolean
    show: boolean
    command: (action) => void
}

export class toolbarActions {
    name: string
    icon: string
    svgIcon?: string
    primary?: boolean
    secondary?: boolean
    show: boolean
    command: (action) => void
}
export class QuickFilter {
    type: string
    fieldStatus: string
    value: any
    label: string
    placeholder: string
    command: (action) => void
}
