import { CommonModule, NgF<PERSON>, NgIf } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormGroup, FormGroupDirective, ReactiveFormsModule } from '@angular/forms';
import { FORM_CONTROL_TYPES } from 'app/shared/tapas-ui';
import { SliderModule } from 'primeng/slider';
import { TooltipModule } from 'primeng/tooltip';

import { TpsErrorComponent } from '../tps-error/tps-error.component';


class tpsSlider {
  type: string
  fieldName: string
  tooltip?: string
  hint?: string
  value: any
  label: string
  placeholder: string
  show: boolean
  onChange: (any) => void
  rules: {
    required?: boolean,
    step: 1
  }
}

@Component({
  selector: 'tps-slider',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, NgIf, TpsErrorComponent, SliderModule, TooltipModule],
  templateUrl: './tps-slider.component.html',
})
export class TpsSliderComponent implements OnInit {
  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  @Input() field: tpsSlider;
  @Input() formName: FormGroup;

  constructor(private formGroupDirective: FormGroupDirective) {
    this.formName = formGroupDirective.control;
  }

  public ngOnInit(): void {
  }

}
