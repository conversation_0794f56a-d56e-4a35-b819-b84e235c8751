import { FORM_CONTROL_TYPES, FORM_FIELDS_CONSTANTS_VALUES, ICON_BUTTON_COLOR } from 'app/shared/tapas-ui';

export const CATEGORY_FORM_MODEL = {
    code: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Code",
        placeholder: 'Ex. CAT001',
        topGroupTitleIcon: 'pi-tag',
        severity: ICON_BUTTON_COLOR.WARNING,
        topGroupTitle: "Category Details",
        show: true,
        rules: {
            required: true,
            maxLength: 250,
        }
    },
    category: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Category",
        placeholder: 'Ex. Category Name',
        show: true,
        rules: {
            required: true,
            maxLength: 250,
        }
    },
    description: {
        type: FORM_CONTROL_TYPES.TEXTAREA,
        value: "",
        label: "Description",
        placeholder: 'Ex. Category Description',
        show: true,
        rules: {
            maxLength: 500,
        }
    },
    contactNo: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Contact No",
        placeholder: 'Ex. 1234567890',
        topGroupTitle: 'Contact Details',
        topGroupTitleIcon: 'pi-phone',
        severity: ICON_BUTTON_COLOR.WARNING,
        show: true,
        rules: {
            pattern: FORM_FIELDS_CONSTANTS_VALUES.NUMERIC_PATTERN,
            maxLength: 15,
        }
    },
    email: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Email",
        placeholder: 'Ex. <EMAIL>',
        show: true,
        rules: {
            pattern: FORM_FIELDS_CONSTANTS_VALUES.EMAIL_PATTERN,
            maxLength: 250,
        }
    }
}