<div class="w-full flex flex-row flex-wrap gap-6" *ngIf="mainFilterFields.length>0">
    <form [formGroup]="mainFilterFormGroup" autocomplete="off">
        <div class="w-full flex flex-row flex-wrap  gap-4">
            <ng-container *ngFor="let field of mainFilterFields" [ngSwitch]="field.type">
                <ng-container *ngIf="field.show">
                    <tps-text class="w-auto" style="max-width: 11rem;min-width: 11rem;" *ngSwitchCase="FORM_CONTROL_TYPES.TEXT" [field]="field"
                        [formName]="mainFilterFormGroup" [layout]="'column'"></tps-text>
                </ng-container>

                <ng-container *ngIf="field.show">
                    <tps-number class="w-auto" style="max-width: 11rem;min-width: 11rem;" *ngSwitchCase="FORM_CONTROL_TYPES.NUMBER" [field]="field"
                        [formName]="mainFilterFormGroup" [layout]="'column'"></tps-number>
                </ng-container>

                <ng-container *ngIf="field.show">
                    <tps-text-area class="w-auto" style="max-width: 11rem;min-width: 11rem;"*ngSwitchCase="FORM_CONTROL_TYPES.TEXTAREA" [field]="field"
                        [formName]="mainFilterFormGroup" [layout]="'column'"></tps-text-area>
                </ng-container>

                <ng-container *ngIf="field.show">
                    <tps-single-select class="w-auto" style="max-width: 11rem;min-width: 11rem;" *ngSwitchCase="FORM_CONTROL_TYPES.SINGLE_SELECT"
                        [field]="field" [formName]="mainFilterFormGroup" [layout]="'column'"></tps-single-select>
                </ng-container>

                <ng-container *ngIf="field.show">
                    <tps-multi-select class="w-auto" style="max-width: 11rem;min-width: 11rem;" *ngSwitchCase="FORM_CONTROL_TYPES.MULTI_SELECT" [field]="field"
                        [formName]="mainFilterFormGroup" [layout]="'column'"></tps-multi-select>
                </ng-container>

                <ng-container *ngIf="field.show">
                    <tps-checkbox class="w-auto" style="max-width: 11rem;min-width: 11rem;" *ngSwitchCase="FORM_CONTROL_TYPES.CHECKBOX" [field]="field"
                        [formName]="mainFilterFormGroup" [layout]="'column'"></tps-checkbox>
                </ng-container>

                <ng-container *ngIf="field.show">
                    <tps-date-picker class="w-auto" style="max-width: 11rem;min-width: 11rem;" *ngSwitchCase="FORM_CONTROL_TYPES.DATE" [field]="field"
                        [formName]="mainFilterFormGroup" [layout]="'column'"></tps-date-picker>
                </ng-container>
                <ng-container *ngIf="field.show">
                    <tps-radio class="w-auto" style="max-width: 11rem;min-width: 11rem;" *ngSwitchCase="FORM_CONTROL_TYPES.RADIO" [field]="field"
                        [formName]="mainFilterFormGroup" [layout]="'column'"></tps-radio>
                </ng-container>

                <ng-container *ngIf="field.rightSideGap">
                    <div class="w-auto">
                    </div>
                </ng-container>
            </ng-container>
        </div>
    </form>
    <div class="flex flex-col gap-1" style="margin-top: -5px;">
        <div class="visibilityHidden">Filter</div>
        <tps-primary-button [isDisabled]="mainFilterFormGroup.invalid" [buttonName]="'GO'"
            (onClick)="onMainFilterChange()  "></tps-primary-button>
    </div>
</div>
