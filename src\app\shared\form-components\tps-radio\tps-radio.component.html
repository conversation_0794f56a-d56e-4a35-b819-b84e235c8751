<form [formGroup]="formName" autocomplete="off">
    <div class="row mb-4">
        <div class="col-md-5">
            <div class="flex flex-row items-center gap-2">
                <label for="{{field.label}}">{{field.label}}
                    <span *ngIf="field?.rules?.required && !formName.controls[field.fieldName].disabled"
                        class="requiredField">*</span>
                </label>
                <i *ngIf="field?.tooltip" [pTooltip]="field?.tooltip" tooltipPosition="bottom"
                    class="cursor-pointer pi pi-question-circle helping_text_icon"></i>
            </div>
        </div>
        <div class="col-md-7">
            <div class="w-full flex align-items-center gap-6">
                <div class="flex align-items-center" *ngFor="let option of field?.options">
                    <p-radioButton [name]="field.fieldName" [value]="option.value" [formControlName]="field.fieldName"
                        id="{{field.label}}" [inputId]="option.label"
                        (onClick)="field.onChange($event);onRadioClick($event)" />
                    <label [for]="option.label" class="ml-2">{{option.label}}</label>
                </div>
            </div>
            <small *ngIf="field?.hint">{{field?.hint}}</small>
            <tps-error [fieldName]="field.fieldName" [label]="field.label" [formName]="formName"></tps-error>
        </div>
    </div>
</form>