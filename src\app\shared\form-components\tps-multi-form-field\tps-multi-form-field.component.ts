import { CommonModule, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Ng<PERSON><PERSON><PERSON><PERSON>, NgS<PERSON>Default } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { FormArray, FormBuilder, FormGroup, FormGroupDirective, ReactiveFormsModule } from '@angular/forms';
import {
  FORM_CONTROL_TYPES,
  FormFactoryService,
  TABLE_ACTION_TYPES,
  TpsErrorComponent,
  TpsSingleSelectComponent,
  TpsTextComponent,
} from 'app/shared/tapas-ui';
import { ButtonModule } from 'primeng/button';
import { TooltipModule } from 'primeng/tooltip';
import { distinctUntilChanged } from 'rxjs';

@Component({
  selector: 'tps-multi-form-field',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, NgIf, NgFor, NgSwitch,
    NgSwitchCase,
    TpsTextComponent, ButtonModule, TpsSingleSelectComponent, TooltipModule],
  templateUrl: './tps-multi-form-field.component.html',
  styleUrl: './tps-multi-form-field.component.scss'
})
export class TpsMultiFormFieldComponent implements OnInit {
  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  public TABLE_ACTION_TYPES = TABLE_ACTION_TYPES;
  @Input() status: any;
  @Input() field: any;
  @Input() formName: FormGroup;

  dynamicForm: FormGroup;
  dynamicListFormArray: FormArray;
  fields: any[] = [];
  existingFieldsData: any[] = [];

  constructor(
    private formgroupDirective: FormGroupDirective,
    private _formBuilder: FormBuilder,
    private _formFactoryService: FormFactoryService,
  ) {
    this.formName = formgroupDirective.control;

  }

  public ngOnInit(): void {
    this.existingFieldsData = this.formName.get(this.field.fieldName).value;
    this.formInitialization();
    this.dynamicListFormArray = this.dynamicForm.get('dynamicFormArray') as FormArray;
    if (this.existingFieldsData && this.existingFieldsData.length > 0) {
      this.existingFieldsData.forEach(row => {
        this.setExistingData(row);
      })
    } else {
      this.dynamicListFormArray.push(this.prepareDynamicForm());
    }
    if (this.status == TABLE_ACTION_TYPES.VIEW) {
      this.dynamicForm.disable();
    }
    this.onFormChanges();
  }
  private formInitialization(): void {
    this.dynamicForm = this._formBuilder.group({
      dynamicFormArray: this._formBuilder.array([])
    })
  }
  public getDynamicFormControls(): FormArray {
    return this.dynamicForm.get("dynamicFormArray") as FormArray
  }
  public prepareDynamicForm() {
    let formFields = this._formFactoryService.getFormControlsFields(this.field?.controls);
    this.fields = this._formFactoryService.getFieldsList(this.field?.controls);
    return this._formBuilder.group(formFields);
  }

  public addNewRow(): void {
    this.dynamicListFormArray = this.dynamicForm.get('dynamicFormArray') as FormArray;
    this.dynamicListFormArray.push(this.prepareDynamicForm());
  }
  public setExistingData(row): void {
    this.dynamicListFormArray = this.dynamicForm.get('dynamicFormArray') as FormArray;
    this.dynamicListFormArray.push(this.prepareDynamicFormForExistingData(row));
  }
  public prepareDynamicFormForExistingData(row) {
    let controls: any = this.field?.controls;
    for (var key in controls) {
      if (controls.hasOwnProperty(key) && row.hasOwnProperty(key)) {
        controls[key]['value'] = row[key];
      }
    }

    let formFields = this._formFactoryService.getFormControlsFields(controls);
    this.fields = this._formFactoryService.getFieldsList(controls);
    return this._formBuilder.group(formFields);
  }


  public deleteRow(index): void {
    this.dynamicListFormArray.removeAt(index);
  }

  public onFormChanges(): void {
    this.dynamicForm.valueChanges
      .pipe((distinctUntilChanged()))
      .subscribe({
        next: response => {
          this.formName.get(this.field.fieldName).setValue(response?.dynamicFormArray);
        }
      });
  }
}

