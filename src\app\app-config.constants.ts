import { InjectionToken } from '@angular/core';

import { IAppConfig } from './app-config.interface';
import { API_TYPES } from './core/constants/enum-constants';

export const APP_UI_CONFIG: IAppConfig = {
    configuration: {
        getTenant: {
            url: "user/getTenant",
            type: API_TYPES.GET
        },
        getTenantConfig: {
            url: "config/tenantConfig/{tenantUid}",
            type: API_TYPES.GET,
            paramList: { tenantUid: null }
        }
    },
    auth: {
        login: {
            url: 'user',
            type: API_TYPES.GET,
        },
        mfaRegistration: {
            url: "mfa/qrcode",
            type: API_TYPES.GET,
        },
        mfaValidation: {
            url: 'mfa/code/',
            type: API_TYPES.POST,
            paramList: { mfaCode: '' }
        }
    },
    master: {
        auditor: {
            get: {
                url: 'auditor',
                type: API_TYPES.GET,
            },
            getAllAuditors: {
                url: 'auditor/all',
                type: API_TYPES.GET,
            },
            create: {
                url: 'auditor',
                type: API_TYPES.POST
            },
            update: {
                url: 'users/{uuid}/update',
                type: API_TYPES.PUT,
                paramList: { uuid: '' }
            },
            activate: {
                url: 'auditor/{uuid}/activate',
                type: API_TYPES.PUT,
                paramList: { uuid: '' }
            },
            deActivate: {
                url: 'auditor/{uuid}/deactivate',
                type: API_TYPES.DELETE,
                paramList: { uuid: '' }
            },
            delete: {
                url: 'auditor/{uuid}/deactivate',
                type: API_TYPES.DELETE,
                paramList: { uuid: '' }
            }
        },
        auditorInCharge: {
            get: {
                url: 'incharge',
                type: API_TYPES.GET,
            },
            getAllAuditorInCharge: {
                url: 'incharge/all',
                type: API_TYPES.GET,
            },
            create: {
                url: 'incharge/create',
                type: API_TYPES.POST
            },
            update: {
                url: 'incharge/{uuid}/update',
                type: API_TYPES.PUT,
                paramList: { uuid: '' }
            },
            delete: {
                url: 'incharge/{uuid}/deactivate',
                type: 'DELETE',
                paramList: { uuid: '' }
            },

        },
        brand: {
            get: {
                url: 'brands/all',
                type: API_TYPES.GET,
            },
            create: {
                url: 'brands',
                type: API_TYPES.POST
            },
            update: {
                url: 'brands/{brandUid}',
                type: API_TYPES.PUT,
                paramList: { brandUid: '' }
            },
            delete: {
                url: 'brands/{brandUid}',
                type: API_TYPES.DELETE,
                paramList: { brandUid: '' }
            },
            activate: {
                url: 'brands/{brandUid}/activate',
                type: API_TYPES.PUT,
                paramList: { brandUid: '' }
            },
            deactivate: {
                url: 'brands/{brandUid}/deactivate',
                type: API_TYPES.DELETE,
                paramList: { brandUid: '' }
            }
        },
        categories: {
            get: {
                url: 'category',
                type: API_TYPES.GET,

            },
            create: {
                url: '/api/categories',
                type: API_TYPES.POST,
            },
            update: {
                url: '/api/categories/{uuid}',
                type: API_TYPES.PUT,
                paramList: {
                    uuid: ''
                }
            },
            delete: {
                url: '/api/categories/{uuid}',
                type: API_TYPES.DELETE,
                paramList: {
                    uuid: ''
                }
            }
        },
        defectType: {
            get: {
                url: 'config/defectType/',
                type: API_TYPES.GET,
            },
            create: {
                url: 'config/defectType/',
                type: API_TYPES.POST
            },
            update: {
                url: 'config/defectType/',
                type: API_TYPES.PUT,
            },
            activate: {
                url: 'config/defectType/{defectUid}/activate',
                type: API_TYPES.PUT,
                payload: null,
                paramList: { defectUid: null }
            },
            deactivate: {
                url: 'config/defectType/{defectUid}',
                type: API_TYPES.DELETE,
                paramList: { defectUid: null }
            },
        },
        productType: {
            get: {
                url: 'config/productType/all',
                type: API_TYPES.GET,
            },
            create: {
                url: 'config/productType/',
                type: API_TYPES.POST
            },
            update: {
                url: 'config/productType/',
                type: API_TYPES.PUT,
            },
            delete: {
                url: 'config/productType/{productTypeUid}',
                type: API_TYPES.DELETE,
                paramList: { productTypeUid: '' }
            },
            activate: {
                url: 'config/productType/{productTypeUid}/activate',
                type: API_TYPES.PUT,
                paramList: { productTypeUid: null }
            },
            deactivate: {
                url: 'config/productType/{productTypeUid}/deactivate',
                type: API_TYPES.DELETE,
                paramList: { productTypeUid: null }
            },
        },
        factory: {
            get: {
                url: 'factory/all',
                type: API_TYPES.GET,
                param: null
            },
            getByUid: {
                url: 'factory/{uuid}',
                type: API_TYPES.GET,
                paramList: { uuid: null }
            },
            activate: {
                url: 'factory/{id}/activate',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            deactivate: {
                url: 'factory/{id}/deactivate',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            },
            add: {
                url: 'factory',
                type: API_TYPES.POST,
                payload: null
            },
            update: {
                url: 'factory/{id}',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            getByVendor: {
                url: 'factory/{id}/vendor',
                type: API_TYPES.GET,
                id: null,
                paramList: { id: null }
            },
            addFactory: {
                url: 'vendor/saveAssociatedFactory',
                type: API_TYPES.POST,
                payload: null
            },
            delete: {
                url: 'factory/{uuid}',
                type: API_TYPES.DELETE,
                paramList: { uuid: '' }
            }
        },
          
    },
    admin: {
        roles: {
            getUserRoleList: {
                url: 'userRole',
                type: API_TYPES.GET,
            },
            createORUpdate: {
                url: 'roles',
                type: API_TYPES.POST
            },
            update: {
                url: 'roles/{uuid}/update',
                type: API_TYPES.PUT,
                paramList: { uuid: '' }
            },
            delete: {
                url: 'roles',
                type: API_TYPES.DELETE,
            }

        },
        users: {
            get: {
                url: 'user/role/{role}',
                type: API_TYPES.GET,
                paramList: { role: '' }
            },
            create: {
                url: 'users/create',
                type: API_TYPES.POST
            },
            update: {
                url: 'users/{uuid}/update',
                type: API_TYPES.PUT,
                paramList: { uuid: '' }
            },
            delete: {
                url: 'users/{uuid}/deactivate',
                type: 'DELETE',
                paramList: { uuid: '' }
            },
            activateUser: {
                url: 'users/{uuid}/activate',
                type: API_TYPES.PUT,
                paramList: { uuid: '' }
            },
            deActivateUser: {
                url: 'users/{uuid}/deactivate',
                type: API_TYPES.DELETE,
                paramList: { uuid: '' }
            }
        },
         technician: {
            get: {
                url: 'techpack/technician',
                type: API_TYPES.GET,
            },
            create: {
                url: 'techpack/technician',
                type: API_TYPES.POST
            },
            update: {
                url: 'techpack/technician',
                type: API_TYPES.PUT,
                paramList: { technicianUid: '' }
            },
            delete: {
                url: 'technician',
                type: 'DELETE',
                paramList: { technicianUid: '' }
            }
        },
        vendors: {
            get: {
                url: 'vendor/all',
                type: API_TYPES.GET,

            },
            create: {
                url: 'vendor',
                type: API_TYPES.POST,
            },
            update: {
                url: 'vendor/{id}',
                type: API_TYPES.PUT,
                paramList: {
                    vendorUid: ''
                }
            },
            delete: {
                url: 'vendor/{uuid}',
                type: API_TYPES.DELETE,
                paramList: {
                    vendorUid: ''
                }
            },
            activate: {
                url: 'vendor/{uuid}/activate',
                type: API_TYPES.PUT,
                paramList: {
                    vendorUid: ''
                }
            },
            deactivate: {
                url: 'vendor/{uuid}/deactivate',
                type: API_TYPES.DELETE,
                paramList: {
                    vendorUid: ''
                }
            }
        },
    },
    document: {
        techPark: {
            get: {
                url: 'doc',
                type: API_TYPES.GET,
            },
            create: {
                url: 'doc',
                type: API_TYPES.POST
            },
            update: {
                url: 'doc/{uuid}',
                type: API_TYPES.PUT,
                paramList: { uuid: '' }
            },
            delete: {
                url: 'doc/{uuid}',
                type: API_TYPES.DELETE,
                paramList: { uuid: '' }
            }
        }
    },

    notification: {
        get: {
            url: 'notification/',
            type: API_TYPES.GET
        },
        getLatestNotification: {
            url: 'notification/latest',
            type: API_TYPES.GET
        },
        updateNotificationStatusByUid: {
            url: 'notification/{uuid}/status/{status}',
            type: API_TYPES.PUT,
            paramList: { uuid: '', status: '1' },
        },
        getNotificationHistory: {
            url: 'notification/{from}/notifications/{to}',
            type: API_TYPES.GET,
            paramList: { from: '', to: '' },
        },
        readAllNotifications: {
            url: 'notification/read/all',
            type: API_TYPES.POST,
        }
    },


}

export let APP_CONFIG = new InjectionToken<IAppConfig>('app.config');
