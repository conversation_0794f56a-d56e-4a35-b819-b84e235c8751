import { <PERSON><PERSON><PERSON>, NgI<PERSON> } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { ButtonModule } from 'primeng/button';
import { MenuModule } from 'primeng/menu';
import { TooltipModule } from 'primeng/tooltip';

class actionType {
  type: string
  icon: string
  title: string
  color: string
  data: any
}

@Component({
  selector: 'tps-cell-action',
  standalone: true,
  imports: [NgIf, NgFor, MatMenuModule, MenuModule, MatButtonModule, ButtonModule, TooltipModule],
  templateUrl: './tps-cell-action.component.html',
  styleUrl: './tps-cell-action.component.css'
})
export class TpsCellActionComponent implements OnInit {
  constructor() { }
  public iconName: string = './assets/svg/';
  @Input() public actions: any;
  @Input() public isMobile: boolean = false;
  public isShowMenuIconAction: boolean = false;
  mainMenuAction: any[] = [];
  extraMenuAction: any[] = [];
  public ngOnInit() {
    if (this.isMobile) {
      this.mainMenuAction = this.actions;
      this.extraMenuAction=[];
    } else {
      this.mainMenuAction = this.actions.slice(0, 3);
      this.extraMenuAction = this.actions.slice(3);
    }
  }
  public getIcon(action): string {
    return `./assets/svg/${action.icon}.svg`;
  }

  public onClickAction(action): void {
    if (action?.onAction) {
      action?.onAction(action);
    }
  }
}
