import { Routes } from '@angular/router';
import { permissionGuard } from 'app/core/auth/guards/permission.guard';
import { FactoryComponent } from './factory.component';

export default [
    {
        path: '',
        component: FactoryComponent,
        canActivate: [permissionGuard],
        data: { permission: "FACTORY_MASTER" },
    },
    { path: 'not-found', loadChildren: () => import('app/modules/page-not-found/page-not-found.routes') },
    { path: '**', pathMatch: 'full', redirectTo: 'not-found' },
] as Routes;