import { CommonModule, <PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { CommonService } from 'app/shared/tapas-ui';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { ImageModule } from 'primeng/image';
import { TooltipModule } from 'primeng/tooltip';

import { AnnotationConfiguration } from '../../../models/tps-annotation-configuration.mode';
import { TpsImageAnnotationModalComponent } from '../tps-image-annotation/tps-image-annotation-modal.component';


@Component({
  selector: 'tps-image-annotation',
  standalone: true,
  imports: [CommonModule, NgFor, ImageModule, TooltipModule],
  templateUrl: './tps-image-annotation.component.html',
  styleUrl: './tps-image-annotation.component.scss',
  providers: [DialogService]
})
export class TpsImageAnnotationComponent implements <PERSON><PERSON>nit, OnD<PERSON>roy {

  @Input() title: string = 'Label(s)';
  @Input() header: string = 'Label(s)';
  @Input() files: any[] = [];
  @Input() labelList: any[] = [];
  @Input() annotationConfiguration: AnnotationConfiguration;
  @Input() showDirection:boolean;
  @Output() onChanges: EventEmitter<any> = new EventEmitter();
  constructor(
    private _commonService: CommonService,
    public _dialogService: DialogService
  ) { }
  public ngOnInit(): void {

  }

  public onCreateAnnotateImage(image): void {
    const ref: DynamicDialogRef = this._dialogService.open(TpsImageAnnotationModalComponent, {
      data: { header: this.header, title: this.title,imageUid:image?.imageUid, imageBlob: this._commonService.convertBase64ToBlob(image.src), labelList: this.labelList, imageName: image.name, shapes: image.shapes, annotationConfiguration: this.annotationConfiguration,showDirection:this.showDirection },
      header: image?.name,
      modal: true,
      width: '98%',
      height: '100vh',
      closeOnEscape: false,
      dismissableMask: false,
      maximizable: true,
      transitionOptions: "300ms",
      baseZIndex: 10000,
    });
    ref.onClose.subscribe((response: any) => {
      if (response) {
        let index = this.files.findIndex(item => item.name == response?.shapes[0]?.imageName);
        // this.files[index]['shapes'] = response;
        this.onChanges.emit(response);
      }
    });

  }

  public ngOnDestroy(): void {

  }

}
