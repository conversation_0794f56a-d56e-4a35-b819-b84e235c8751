import { CommonModule, NgF<PERSON>, NgIf } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormGroup, FormGroupDirective, ReactiveFormsModule } from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { TooltipModule } from 'primeng/tooltip';

import { FORM_CONTROL_TYPES } from '../../constants/tps-enum-constants';
import { TpsSecondaryButtonComponent } from '../../tapas-ui';
import { TpsErrorComponent } from '../tps-error/tps-error.component';

@Component({
  selector: 'tps-text-search',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, NgIf, TpsErrorComponent, InputTextModule, TpsSecondaryButtonComponent, TooltipModule],
  templateUrl: './tps-text-search.component.html'
})
export class TpsTextSearchComponent implements OnInit {
  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  @Input() field: any;
  @Input() formName: FormGroup;

  @Output() onClick: EventEmitter<any> = new EventEmitter();

  constructor(private formgroupDirective: FormGroupDirective) {
    this.formName = formgroupDirective.control;
  }

  ngOnInit(): void {

  }
  public onInput(): void {
    this.onClick.emit(this.formName)
  }
}
