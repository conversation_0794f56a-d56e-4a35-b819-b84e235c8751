import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { SharedModule } from 'app/shared/shared.module';
import { CommonService, HTTP_STATUS, InvokeService } from 'app/shared/tapas-ui';
import { ReplaySubject } from 'rxjs';



interface Activity {
  title: string;
  description: string;
  timestamp: Date;
  icon: string;
}

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
  standalone: true,
  imports: [SharedModule]
})
export class HomeComponent implements OnInit, OnDestroy {
  title = 'Dashboard';
  subTitle = 'AI Powered Warehouse Monitoring System';

  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);



  constructor(
    private _commonService: CommonService,
    private _invokeService: InvokeService) { }

  public ngOnInit(): void {

  }



  public ngOnDestroy(): void {
    this.$destroyed.next(true);
    this.$destroyed.complete();
  }
}
