<div class="flex flex-col w-full h-full">
    <tps-table class="w-full h-full" [buttonActions]="buttonActions" [toolBarActions]="toolBarActions"
        [tableHeader]="hdr" [colDefs]="columnDefs" [data]="userRoles" (onRefresh)="onRefresh()"></tps-table>
</div>

<ng-container *ngIf="showDialog">
    <p-dialog [header]="dialogHeader" [modal]="true" [(visible)]="showDialog" [style]="{ width: '40rem' }"
        (onHide)="showDialog=false;">
        <div class="w-full flex flex-col gap-1">
            <div class="modal-content">
                <tps-form class="w-full" [formGroup]="roleForm" [fields]="fields" [status]="status"
                    [layout]="'column'"></tps-form>
            </div>
            <div class="modal-footer">
                <div class="w-full flex flex-row justify-end gap-6">
                    <tps-secondary-button [buttonName]="'Cancel'"
                        (onClick)="onDialogClose(false)"></tps-secondary-button>
                    <tps-primary-button *ngIf="status != TABLE_ACTION_TYPES.VIEW" [buttonName]="'Submit'"
                        [isDisabled]="roleForm.invalid" (onClick)="onSubmit()"></tps-primary-button>
                </div>
            </div>
        </div>
    </p-dialog>

</ng-container>