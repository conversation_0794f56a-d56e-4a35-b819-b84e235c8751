import { environment, PARTNERS } from 'environments/environment';

export const ACL = {
    'madura': {
        ADMIN: {
            //admin role
            //top menu ACL
            HELP_CENTER: true,
            HOME: true,
            DOCUMENT: true,
            MASTER: true,
            ADMIN: true,


            //Master Menu
            // Auditor
            AUDITOR_MASTER: true,         // Main auditor menu access
            AUDITOR_CREATE: true,
            AUDITOR_UPDATE: true,
            AUDITOR_DEACTIVATE: true,
            AUDITOR_ACTIVATE: true,
            AUDITOR_VIEW: true,
            AUDITOR_DELETE: true,

            //Brand
            BRAND_MASTER: true,
            BRAND_MASTER_CREATE: true,
            BRAND_MASTER_EDIT: true,
            BRAND_MASTER_VIEW: true,
            BRAND_MASTER_DELETE: true,
            BRAND_MASTER_ACTIVATE: true,
            BRAND_MASTER_DEACTIVATE: true,

            //Category
            CATEGORY_MASTER: true,
            CATEGORY_MASTER_CREATE: true,
            CATEGORY_MASTER_EDIT: true,
            CATEGORY_MASTER_VIEW: true,
            CATEGORY_MASTER_DELETE: true,

            //Defect Type
            DEFECT_TYPE_MASTER: true,
            DEFECT_TYPE_MASTER_CREATE: true,
            DEFECT_TYPE_MASTER_EDIT: true,
            DEFECT_TYPE_MASTER_VIEW: true,
            DEFECT_TYPE_MASTER_DELETE: true,
            DEFECT_TYPE_MASTER_ACTIVATE: true,
            DEFECT_TYPE_MASTER_DEACTIVATE: true,

            //Product Type
            PRODUCT_TYPE_MASTER: true,
            PRODUCT_TYPE_MASTER_CREATE: true,
            PRODUCT_TYPE_MASTER_EDIT: true,
            PRODUCT_TYPE_MASTER_VIEW: true,
            PRODUCT_TYPE_MASTER_DELETE: true,
            PRODUCT_TYPE_MASTER_ACTIVATE: true,
            PRODUCT_TYPE_MASTER_DEACTIVATE: true,

            //Factory
            FACTORY_MASTER: true,
            FACTORY_MASTER_CREATE: true,
            FACTORY_MASTER_EDIT: true,
            FACTORY_MASTER_VIEW: true,
            FACTORY_MASTER_DELETE: true,
            FACTORY_MASTER_ACTIVATE: true,
            FACTORY_MASTER_DEACTIVATE: true,


            // Technician
            TECHNICIAN_MASTER: true,       // Main technician menu access
            TECHNICIAN_CREATE: true,
            TECHNICIAN_UPDATE: true,
            TECHNICIAN_DELETE: true,
            TECHNICIAN_VIEW: true,

            // Vendor
            VENDOR_MASTER: true,                  // Main vendor menu access
            VENDOR_CREATE: true,
            VENDOR_UPDATE: true,
            VENDOR_DELETE: true,
            VENDOR_VIEW: true,
            VENDOR_DEACTIVATE : true,
            VENDOR_ACTIVATE: true,


            //Users
            USERS_MASTER: true,                 // Main users menu access
            USER_MASTER: true,                 // Users list view access
            USER_MASTER_CREATE: true,                 // Create new user
            USER_MASTER_UPDATE: true,                 // Update existing user
            USER_MASTER_DELETE: true,                 // Delete user
            USER_MASTER_VIEW: true,                   // View user details
            USER_MASTER_EXPORT: true,                 // Export users data
            USER_MASTER_IMPORT: true,                 // Import users data
            USER_MASTER_ACTIVATE: true,               // Activate user
            USER_MASTER_DEACTIVATE: true,             // Deactivate user
            USER_MASTER_ASSIGN_ROLE: true,            // Assign roles to users
            USER_MASTER_RESET_PASSWORD: true,         // Reset user password
            USER_MASTER_MANAGE_PERMISSIONS: true,     // Manage user permissions
            USER_MASTER_MANAGE_MFA: true,             // Manage MFA settings
            USER_MASTER_MANAGE_PROFILE: true,         // Manage user profile
            USER_MASTER_BULK_ACTIONS: true,           // Perform bulk actions on users

            // Roles
            ROLE_MASTER: true,         // Main roles menu access
            ROLE_MASTER_CREATE: true,
            ROLE_MASTER_UPDATE: true,
            ROLE_MASTER_DELETE: true,

            TECH_PARK: true,         // Main roles menu access
            TECH_PARK_CREATE: true,
            TECH_PARK_UPDATE: true,
            TECH_PARK_DELETE: true,
        },
    }
}
