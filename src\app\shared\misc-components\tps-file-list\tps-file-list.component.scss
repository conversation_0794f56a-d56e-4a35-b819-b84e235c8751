.file-wrapper {
    position: relative;
    box-shadow: rgba(0, 0, 0, 0.16) 0px 10px 36px 0px, rgba(0, 0, 0, 0.06) 0px 0px 0px 1px;

    .each-file-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .file-action {
        position: absolute;
        top: -16px;
        left: 0;
        bottom: 0;
        width: 100%;
        background: rgba(0, 0, 0, 0.2);
        color: #fff;
        opacity: 0;
        display: flex;
        flex-direction: row;
        align-items: center;
        border-radius: 1rem;
    }

    &:hover {
        .file-action {
            opacity: 1;
        }

        .file-wrapper {
            background-color: lightgray;
        }
    }
}