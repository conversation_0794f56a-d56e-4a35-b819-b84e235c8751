<p-toast appendTo="body" />

<div class="annotation_wrapper w-full flex flex-col gap-1">
    <div class="w-full h-full flex flex-row" style="border: 1px solid lightgray;">
        <div class=" w-full flex flex-col gap-1 p-2">
            <div class=" w-full flex flex-col" style="border-bottom: 1px solid lightgray;">
                <div class="w-full flex flex-row justify-center items-center gap-4 pb-2">
                    <div class="" *ngFor="let item of shapesList" [hidden]="!item.show">
                        <p-button icon="pi {{item.icon}}" [rounded]="true" severity="warning"
                            [outlined]="(item.id != currentDrawingContext.currentShape) && !(item?.type == selectedLane?.type)"
                            [pTooltip]="item.name" tooltipPosition="top" (click)="onSelectShape(item)" />
                    </div>
                </div>
            </div>
            <div class="annotation-preview-wrapper">
                <svg id="trainer_image_cropper" [attr.height]="imageHeight" [attr.width]="imageWidth"
                    (mouseup)="handleMouseUp($event)" (mousedown)="handleMouseDown($event)"
                    (mousemove)="handleMouseMove($event)">
                </svg>
            </div>
        </div>
        <!-- <div class=" w-full flex flex-col gap-1 p-2"
            style="max-width:50px;height: 73vh;border-left: 1px solid lightgray;">
            <div class="tps-dynamic-form-sub-title mb-4 m-2 text-center"></div>
            <div class="w-full flex flex-col justify-center items-center gap-4">
                <div class="" *ngFor="let item of shapesList" [hidden]="!item.show">
                    <p-button icon="pi {{item.icon}}" [rounded]="true" severity="warning"
                        [outlined]="item.id != currentDrawingContext.currentShape" [pTooltip]="item.name"
                        tooltipPosition="top" (click)="onSelectShape(item)" />
                </div>
            </div>
        </div> -->
        <div class="flex flex-col gap-1 p-4" style="width:200px;;height: 73vh;border-left: 1px solid lightgray;">
            <div class="tps-dynamic-form-sub-title mb-4 m-2 text-center">{{title}}</div>
            <!-- <p-divider></p-divider> -->
            <div class="flex flex-col gap-1 p-2" style="overflow-y: scroll;">
                <div *ngFor="let labelItem of labelList" class="flex flex-row gap-2 items-center each_label"
                    [class.active-label]="labelItem?.value == selectedLabelItem?.value"
                    (click)="onActiveLabel(labelItem)" [class.selected-label]="getAnnotatedLabel(labelItem)">
                    <i *ngIf="getAnnotatedLabel(labelItem)" class="pi pi-check-circle"
                        style="font-size: 1rem;color:white"></i>
                    <div>
                        {{labelItem.label}}
                    </div>
                </div>

            </div>
        </div>
    </div>

    <div class="w-full flex flex-row justify-between">
        <div class="w-full flex flex-row justify-end gap-4">
            <tps-primary-button [isDisabled]="shapes.length==0" [buttonName]="'Save & Close'"
                (onClick)="onSaveAnnotated()"></tps-primary-button>
            <!-- <tps-primary-button [isDisabled]="shapes.length==0" [buttonName]="'Save Annotated Image'"
                (onClick)="onSaveAnnotated()"></tps-primary-button> -->
        </div>
    </div>
</div>