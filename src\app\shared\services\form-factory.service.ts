import { Injectable } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';

import { FORM_CONTROL_TYPES, FORM_VALIDATION_TYPES } from '../constants/tps-enum-constants';



@Injectable({
  providedIn: 'root'
})
export class FormFactoryService {
  constructor() { }
  public getFormControlsFields(model) {
    const formGroupFields = {};
    for (const field of Object.keys(model)) {
      const fieldProps = model[field];
      const validators = this.addValidator(fieldProps.rules);
      
      // Create form control with disabled state in configuration
      formGroupFields[field] = new FormControl({
        value: fieldProps.value,
        disabled: fieldProps.disabled || false
      }, Validators.compose(validators));
    }
    return formGroupFields;
  }

  private addValidator(rules) {
    if (!rules) {
      return [];
    }
    let validators: any[] = [];
    Object.keys(rules).forEach((rule) => {
      switch (rule) {
        case FORM_VALIDATION_TYPES.REQUIRED:
          if (rules[rule]) {
            validators.push(Validators.required);
          }
          break;
        case FORM_VALIDATION_TYPES.MIN:
          validators.push(Validators.min(rules[rule]));
          break;
        case FORM_VALIDATION_TYPES.MAX:
          validators.push(Validators.max(rules[rule]));
          break;
        case FORM_VALIDATION_TYPES.MINLENGTH:
          validators.push(Validators.minLength(rules[rule]));
          break;
        case FORM_VALIDATION_TYPES.MAXLENGTH:
          validators.push(Validators.maxLength(rules[rule]));
          break;
        case FORM_VALIDATION_TYPES.PATTERN:
          validators.push(Validators.pattern(rules[rule]));
          break;
        //add more cases for the future.
      }
    });
    return validators;
  }
  public getFieldsList(model?): any[] {
    let fields = [];
    for (const field of Object.keys(model)) {
      const fieldProps = model[field];
      fields.push({ ...fieldProps, placeholder: fieldProps.placeholder ? fieldProps.placeholder : fieldProps.label, fieldName: field });
    }
    return fields;
  }

  public mapFormFieldsDataToModel(formGroup: FormGroup, model: any, fields: any[]): any {
    let controls = formGroup.controls;
    for (const field of Object.keys(controls)) {
      const dateField = fields.find(eachField => eachField.fieldName == field)
      const fieldProps = controls[field];
      if (model.hasOwnProperty(field)) {
        if (dateField?.type == FORM_CONTROL_TYPES.DATE) {
          model[field] = +new Date(fieldProps.value);
        } else {
          model[field] = fieldProps.value;
        }
      } else {
        if (dateField?.type == FORM_CONTROL_TYPES.DATE) {
          model[field] = +new Date(fieldProps.value);
        } else {
          model[field] = fieldProps.value;
        }
      }
    }
    return model;
  }

  public setFormControlsValues(formGroup: FormGroup, model: any, fields: any[]): FormGroup {
    for (const field of Object.keys(formGroup.controls)) {
      if (model.hasOwnProperty(field)) {
        const dateField = fields.find(eachField => eachField.fieldName == field)
        if (dateField?.type == FORM_CONTROL_TYPES.DATE) {
          formGroup.controls[field].setValue(new Date(model[field]));
        } else {
          formGroup.controls[field].setValue(model[field]);
        }
      }
    }
    return formGroup;
  }

  public setOptionsToDropDown(fields: any[] = [], field: string = "", dataList: any[] = []): any[] {
    let findIndex = fields.findIndex(item => item.fieldName == field);
    if (findIndex > -1) {
      fields[findIndex]['options'] = dataList
    }
    return fields;
  }

  public showHideField(fields: any[] = [], field: string = "", isShow, formName?: FormGroup): any[] {
    let findIndex = fields.findIndex(item => item.fieldName == field);
    if (findIndex > -1) {
      fields[findIndex]['show'] = isShow;
    }
    return fields;
  }

  public setFieldStatus(fields: any[] = [], field: string = "", fieldStatus: string,): any[] {
    let findIndex = fields.findIndex(item => item.fieldName == field);
    if (findIndex > -1) {
      fields[findIndex]['fieldStatus'] = fieldStatus;
    }
    return fields;
  }

  public setTypeStatus(fields: any[] = [], field: string = "", fieldStatus: string,): any[] {
    let findIndex = fields.findIndex(item => item.fieldName == field);
    if (findIndex > -1) {
      fields[findIndex]['type'] = fieldStatus;
    }
    return fields;
  }

  public setFieldsValidation(fields: any[] = [], field: string = "", rules): any[] {
    let findIndex = fields.findIndex(item => item.fieldName == field);
    if (findIndex > -1) {
      fields[findIndex]['rules'] = { ...fields[findIndex]['rules'], ...rules }
    }
    return fields;
  }

  public getOptionFieldForModel(fieldName: any, formValue, fields: any[], key: string): any {
    let options: any[] = fields.find(item => item.fieldName == fieldName)?.options;
    let option = options.find(option => option[key] == formValue)
    return option;
  }
}
