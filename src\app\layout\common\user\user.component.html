<div class="flex flex-row items-center gap-2 cursor-pointer" [matMenuTriggerFor]="userActions">
    <img src="assets/svg/user_blue.svg" alt="user icon" style="width:20px;height: 20px;" />
    <div class="flex flex-col gap-1">
        <div class="login_user_name"
            style="color:#192638;font-size: 12px;font-weight: 400; text-transform: capitalize;">
            {{user.userName}}
        </div>
        <div class="login_user_role" style="color:#ED736B;font-size: 10px;font-weight: 600;">
            {{user.role}}
        </div>
    </div>
</div>


<mat-menu [xPosition]="'before'" #userActions="matMenu" class="card">
    <button mat-menu-item class="pl-5 pr-4">
        <span class="flex flex-row gap-4 items-center">
            <img src="assets/images/avatars/profile_avatar.png" alt="user icon"
                style="width:50px;height: 50px;border-radius: 50%;" />
            <div class="flex flex-col gap-1" style="word-break: break-all;">
                <span><strong>{{user.fullName }}</strong></span>
                <div>{{user.email}}</div>
            </div>

        </span>
    </button>
    <p-divider style="margin: 0px !important; padding: 0px !important;"></p-divider>
    <button mat-menu-item (click)="onViewUserProfile()" class="pl-5 pr-4">
        <span class="flex flex-row gap-4 items-center">
            <img src="assets/svg/user_blue.svg" alt="user icon" style="width:20px;height: 20px;" />
            <div class="flex flex-col gap-1">
                View Profile
            </div>
        </span>
    </button>
    <!-- <button mat-menu-item (click)="onViewNotificationHistory()" class="pl-5 pr-4">
        <span class="flex flex-row gap-4 items-center">
            <img src="assets/svg/history.svg" alt="user icon" style="width:20px;height: 20px;" />
            <div class="flex flex-col gap-1">
                Notification History
            </div>
        </span>
    </button> -->
    <!-- <p-divider></p-divider>
    <button mat-menu-item (click)="onApplyChanges()" class="pl-5 pr-4 ">
        <span class="flex flex-row gap-4 items-center">
            <i class="pi pi-sync" style="font-size: 1.25rem;color: #a855f7;margin-top:5px"></i>
            <span>Apply Changes</span>
        </span>
    </button> -->
    <p-divider style="margin: 0px !important; padding: 0px !important;"></p-divider>
    <button mat-menu-item (click)="signOut()" class="pl-5 pr-4 ">
        <span class="flex flex-row gap-4 items-center">
            <i class="pi pi-sign-out" style="font-size: 1.25rem;color: #ED736B;margin-top:5px"></i>
            <span> Sign out</span>
        </span>
    </button>
</mat-menu>