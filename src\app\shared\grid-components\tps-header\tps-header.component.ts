import { NgIf } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { TooltipModule } from 'primeng/tooltip';

@Component({
  selector: 'tps-header',
  standalone: true,
  imports: [NgIf, TooltipModule],
  templateUrl: './tps-header.component.html',
  styleUrl: './tps-header.component.css'
})
export class TpsHeaderComponent {
  @Input() showBackIcon: boolean;
  @Input() headerName: string;
  @Input() showNoOfRows: boolean;
  @Input() totalNoOfRow: number = 0;
  @Input() tooltip: string = '';
  @Input() showRefreshIcon: boolean;
  //Event emitters
  @Output() onClick: EventEmitter<any> = new EventEmitter();
  @Output() onRefresh: EventEmitter<any> = new EventEmitter();
  //on click back

  constructor() {

  }

  public ngOnInit(): void {

  }
  public onClickBack(): void {
    this.onClick.emit(true);
  }

  public onViewRefresh(): void {
    this.onRefresh.emit(true);
  }
}
