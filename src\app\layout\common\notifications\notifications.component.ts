import { Overlay, OverlayRef } from '@angular/cdk/overlay';
import { TemplatePortal } from '@angular/cdk/portal';
import { Date<PERSON>ipe, <PERSON><PERSON><PERSON>, <PERSON>F<PERSON>, NgIf, NgTemplateOutlet } from '@angular/common';
import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    OnDestroy,
    OnInit,
    TemplateRef,
    ViewChild,
    ViewContainerRef,
    ViewEncapsulation,
} from '@angular/core';
import { MatButton, MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { RouterLink } from '@angular/router';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { Util } from 'app/core/common/util';
import { NotificationsService } from 'app/layout/common/notifications/notifications.service';
import { Notification } from 'app/layout/common/notifications/notifications.types';
import { CommonService, HTTP_STATUS, InvokeService } from 'app/shared/tapas-ui';
import { ButtonModule } from 'primeng/button';
import { MenuModule } from 'primeng/menu';
import { TooltipModule } from 'primeng/tooltip';
import { Subject, takeUntil } from 'rxjs';

import { NotificationActionMenuComponent } from './notification-action-menu/notification-action-menu.component';

@Component({
    selector: 'notifications',
    templateUrl: './notifications.component.html',
    exportAs: 'notifications',
    standalone: true,
    imports: [MatButtonModule, MenuModule, ButtonModule, NgIf, MatIconModule, TooltipModule, NgFor, NgClass, NgTemplateOutlet, RouterLink, DatePipe, NotificationActionMenuComponent],
})
export class NotificationsComponent implements OnInit, OnDestroy {
    @ViewChild('notificationsOrigin') private _notificationsOrigin: MatButton;
    @ViewChild('notificationsPanel') private _notificationsPanel: TemplateRef<any>;

    notifications: Notification[];
    unreadCount: number = 0;
    private _overlayRef: OverlayRef;
    private _unsubscribeAll: Subject<any> = new Subject<any>();

    /**
     * Constructor
     */
    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _notificationsService: NotificationsService,
        private _overlay: Overlay,
        private _viewContainerRef: ViewContainerRef,
        private _commonService: CommonService,
        private _invokeService: InvokeService,
    ) {
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    public ngOnInit(): void {
        this.onRefresh();
    }

    public onRefresh(): void {
        //this.getLatestNotifications();
    }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();

        // Dispose the overlay
        if (this._overlayRef) {
            this._overlayRef.dispose();
        }
    }

    private getLatestNotifications(): void {
        this._invokeService.serviceInvocation(APP_UI_CONFIG.notification.getLatestNotification)
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe({
                next: response => {
                    this.notifications = response;
                    this._calculateUnreadCount();
                    this._changeDetectorRef.detectChanges();
                },
                error: error => {
                    this._commonService.error(error);
                }
            })
    }

    private updateNotificationStatus(notification): void {
        APP_UI_CONFIG.notification.updateNotificationStatusByUid.paramList.uuid = notification.uuid;
        APP_UI_CONFIG.notification.updateNotificationStatusByUid.paramList.status = notification.readStatus;
        this._invokeService.serviceInvocation(APP_UI_CONFIG.notification.updateNotificationStatusByUid)
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe({
                next: response => {
                    this.getLatestNotifications();
                },
                error: error => {
                    this._commonService.error(error);
                }
            })
    }




    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Open the notifications panel
     */
    openPanel(): void {
        // Return if the notifications panel or its origin is not defined
        if (!this._notificationsPanel || !this._notificationsOrigin) {
            return;
        }

        // Create the overlay if it doesn't exist
        if (!this._overlayRef) {
            this._createOverlay();
        }

        // Attach the portal to the overlay
        this._overlayRef.attach(new TemplatePortal(this._notificationsPanel, this._viewContainerRef));
    }

    /**
     * Close the notifications panel
     */
    closePanel(): void {
        this._overlayRef.detach();
    }

    /**
     * Mark all notifications as read
     */
    markAllAsRead(): void {
        // Mark all as read
        let payload: any[] = this.notifications.filter(item => item.readStatus == 0).map(notification => ({ uuid: notification.uuid }))
        this._invokeService.serviceInvocation(APP_UI_CONFIG.notification.readAllNotifications, '', Util.clone(payload))
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe({
                next: response => {
                    if (response.code == HTTP_STATUS.SUCCESS) {
                        this._commonService.success('Notification Status updated successfully');
                        this.getLatestNotifications();
                    } else {
                        this._commonService.handleError(response);
                    }
                },
                error: error => {
                    this._commonService.handleError(error);
                }
            })
    }

    /**
     * Toggle read status of the given notification
     */
    toggleRead(notification: Notification): void {
        // Toggle the read status
        if (notification.readStatus == 1) {
            notification.readStatus = 0;
        } else {
            notification.readStatus = 1;
        }
        // Update the notification
        this.updateNotificationStatus(notification);
    }

    /**
     * Delete the given notification
     */
    delete(notification: Notification): void {
        // Delete the notification
        this._notificationsService.delete(notification.id).subscribe();
    }

    /**
     * Track by function for ngFor loops
     *
     * @param index
     * @param item
     */
    trackByFn(index: number, item: any): any {
        return item.id || index;
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Private methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Create the overlay
     */
    private _createOverlay(): void {
        // Create the overlay
        this._overlayRef = this._overlay.create({
            hasBackdrop: true,
            backdropClass: 'fuse-backdrop-on-mobile',
            scrollStrategy: this._overlay.scrollStrategies.block(),
            positionStrategy: this._overlay.position()
                .flexibleConnectedTo(this._notificationsOrigin._elementRef.nativeElement)
                .withLockedPosition(true)
                .withPush(true)
                .withPositions([
                    {
                        originX: 'start',
                        originY: 'bottom',
                        overlayX: 'start',
                        overlayY: 'top',
                    },
                    {
                        originX: 'start',
                        originY: 'top',
                        overlayX: 'start',
                        overlayY: 'bottom',
                    },
                    {
                        originX: 'end',
                        originY: 'bottom',
                        overlayX: 'end',
                        overlayY: 'top',
                    },
                    {
                        originX: 'end',
                        originY: 'top',
                        overlayX: 'end',
                        overlayY: 'bottom',
                    },
                ]),
        });

        // Detach the overlay from the portal on backdrop click
        this._overlayRef.backdropClick().subscribe(() => {
            this._overlayRef.detach();
        });
    }

    /**
     * Calculate the unread count
     *
     * @private
     */
    private _calculateUnreadCount(): void {
        let count = 0;

        if (this.notifications && this.notifications.length) {
            count = this.notifications.filter(notification => notification.readStatus == 0).length;
        }

        this.unreadCount = count;
        console.log("unreadCount:", this.unreadCount)
    }
}
