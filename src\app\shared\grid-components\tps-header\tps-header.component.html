<div class="w-full flex flex-row items-center justify-between">
    <div class="flex flex-row items-center gap-2">
        <i *ngIf="showBackIcon" class="cursor-pointer pi pi-arrow-left" style="font-size: 1rem"
            (click)="onClickBack()"></i>
        <div class="flex flex-row items-center gap-2">
            <div class="table-heading">{{headerName}}</div>
            <i *ngIf="tooltip" class="cursor-pointer pi pi-info-circle"
                style="font-size: 1.25rem !important;margin-top:5px" tooltipPosition="bottom" [pTooltip]="tooltip"></i>
            <div *ngIf="showNoOfRows" class="total-row-number">({{totalNoOfRow}})</div>
        </div>
    </div>
    <img *ngIf="showRefreshIcon" class="cursor-pointer" src="assets/svg/refresh_blue.svg" [pTooltip]="'Refresh'"
        tooltipPosition="bottom" style="width:20px;height: 20px;" (click)="onViewRefresh()" />
</div>