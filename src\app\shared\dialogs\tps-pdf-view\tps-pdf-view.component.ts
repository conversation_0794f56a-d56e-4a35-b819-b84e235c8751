import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { TpsHeaderComponent } from 'app/shared/tapas-ui';
import { NgxExtendedPdfViewerModule } from 'ngx-extended-pdf-viewer';
import { DialogService, DynamicDialogComponent, DynamicDialogRef } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-tps-pdf-view',
  standalone: true,
  imports: [NgxExtendedPdfViewerModule, TpsHeaderComponent],
  templateUrl: './tps-pdf-view.component.html',
  styleUrl: './tps-pdf-view.component.scss'
})
export class TpsPdfViewComponent implements OnInit, OnDestroy {

  hdr: string = '';
  data: any;
  instance: DynamicDialogComponent | undefined;
  constructor(
    private dialogService: DialogService,
    private _dynamicDialogRef: DynamicDialogRef) {
    this.instance = this.dialogService.getInstance(this._dynamicDialogRef);
  }
  public ngOnInit(): void {
    if (this.instance && this.instance.data) {
      this.data = this.instance.data;
    }
  }

  public onClosePdfViewer(): void {
    this._dynamicDialogRef.close();
  }

  public ngOnDestroy(): void {

  }

}
