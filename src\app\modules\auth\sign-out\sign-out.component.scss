:host {
    display: block;
    width: 100%;
    min-height: 100vh;
}

.bg-primary-500 {
    background-color: #517EBC;
}

.bg-primary-600:hover {
    background-color: #4169a3;
}

.text-primary-500 {
    color: #517EBC;
}

.shadow-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 
                0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.pi {
    &.pi-check-circle {
        color: #10B981;
    }
    
    &.pi-spinner {
        animation: spin 1s linear infinite;
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}