@use '@angular/material' as mat;

$theme: mat.define-theme((color: (theme-type: light,
        primary: mat.$azure-palette,
        tertiary: mat.$blue-palette,
      )));

@include mat.core();
@include mat.color-variants-backwards-compatibility($theme);

.mat-mdc-menu-panel {
  background-color: #FFFFFF !important;
  border: none !important;
}

.mat-mdc-menu-item:not([disabled]):hover {
  background-color: #E7EAF0 !important;
}
