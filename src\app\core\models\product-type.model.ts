export interface iProductType {
  aqlType?: string;
  reason?: string;
  category?: string;
  code?: string;
  createdBy?: string;
  createdTime?: number;
  deleted?: number;
  description?: string;
  family?: string;
  id?: number;
  modifiedBy?: string;
  modifiedTime?: number;
  name?: string;
  ordinal?: number;
  parentTenantUid?: string;
  sizeCode?: string;
  status?: number;
  tenantId?: number;
  tenantUid?: string;
  uuid?: string;
  amount?: number;
  createdDateText?: string;
};

export class ProductType implements iProductType {
  public aqlType?: string;
  public reason?: string;
  public category?: string;
  public code?: string;
  public createdBy?: string;
  public createdTime?: number;
  public deleted?: number;
  public product?: string;
  public description?: string;
  public family?: string;
  public id?: number;
  public modifiedBy?: string;
  public modifiedTime?: number;
  public name?: string;
  public ordinal?: number;
  public parentTenantUid?: string;
  public sizeCode?: string;
  public status?: number;
  public tenantId?: number;
  public tenantUid?: string;
  public uuid?: string;
  public amount?: number;
  public createdDateText?: string;
};