import { CommonModule, NgIf } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { FormGroup, FormGroupDirective, ReactiveFormsModule } from '@angular/forms';
import { FORM_CONTROL_TYPES } from 'app/shared/tapas-ui';
import { InputTextModule } from 'primeng/inputtext';
import { PasswordModule } from 'primeng/password';
import { TooltipModule } from 'primeng/tooltip';

import { TpsErrorComponent } from '../tps-error/tps-error.component';

@Component({
  selector: 'tps-password',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, NgIf, TpsErrorComponent, InputTextModule, PasswordModule, TooltipModule],
  templateUrl: './tps-password.component.html',
})
export class TpsPasswordComponent implements OnInit {
  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  @Input() field: any;
  @Input() formName: FormGroup;

  maxLength: number = 250;

  constructor(private formgroupDirective: FormGroupDirective) {
    this.formName = formgroupDirective.control;
  }

  ngOnInit(): void {
    if (this.field?.rules?.maxLength) {
      this.maxLength = this.field?.rules?.maxLength;
    }
  }
}
