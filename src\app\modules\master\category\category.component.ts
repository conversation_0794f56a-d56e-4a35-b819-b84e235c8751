import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { Category } from 'app/core/models/category-model';
import { SharedModule } from 'app/shared/shared.module';
import {
    actionType,
    buttonActions,
    CommonService,
    ICON_BUTTON_SEVERITY,
    ICON_PRIMENG_LIST,
    InvokeService,
    TABLE_ACTION_TYPES,
    toolbarActions,
} from 'app/shared/tapas-ui';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { finalize, ReplaySubject, takeUntil } from 'rxjs';

import { CreateEditCategoryComponent } from './create-edit-category/create-edit-category.component';

@Component({
  selector: 'tps-category',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './category.component.html'
})
export class CategoryComponent implements OnInit, OnDestroy {
  hdr: string = 'Categories';
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);
  columnDefs: any[] = [];
  categories: Category[] = [];
  ACL_LIST = this._commonService.getACLList();
  buttonActions: buttonActions[] = [];
  toolBarActions: toolbarActions[] = [];

  constructor(
    private _commonService: CommonService,
    private _invokeService: InvokeService,
    private _dialogService: DialogService,
  ) { }

  public ngOnInit(): void {
    this.prepareTableActions();
    this.onRefresh();
  }

  private prepareTableActions(): void {
    this.buttonActions = [
      {
        name: 'Create Category',
        icon: 'pi-plus',
        primary: true,
        isSvg: false,
        show: this.ACL_LIST.CATEGORY_CREATE,
        command: () => {
          this.onOpenCategoryDialog('Create Category', new Category(), TABLE_ACTION_TYPES.CREATE);
        }
      }
    ];
    this.toolBarActions = [];
  }

  public onRefresh(): void {
    this.getCategories();
    this.prepareTableColumns();
  }

  private prepareTableColumns(): void {
    this.columnDefs = [
      {
        headerName: "Code",
        field: "code",
        sortable: true,
      },
      {
        headerName: "Category",
        field: "category",
        sortable: true,
      },
      {
        headerName: "Description",
        field: "description",
        sortable: true,
      },
      {
        headerName: "Email",
        field: "email",
        sortable: true,
      },
      {
        headerName: "Contact No",
        field: "contactNo",
        sortable: true,
      },
      {
        headerName: "Created On",
        field: "createdTimeText",
        sortable: true,
      },
      {
        headerName: 'Actions',
        pinned: 'right',
        maxWidth: 100,
        sortable: false,
        isActionCol: true,
        actions: (params) => this.prepareTableActionIcons(params)
      }
    ];
  }

  public prepareTableActionIcons(row): actionType[] {
    let iconsList: actionType[] = [];
    iconsList.push({
      type: TABLE_ACTION_TYPES.VIEW,
      icon: ICON_PRIMENG_LIST.PI_EYE,
      title: "View",
      data: row,
      severity: ICON_BUTTON_SEVERITY.SECONDARY,
      onAction: (rowItem) => this.onOpenCategoryDialog('View Category', row, TABLE_ACTION_TYPES.VIEW)
    });

    if (this.ACL_LIST?.CATEGORY_UPDATE) {
      iconsList.push({
        type: TABLE_ACTION_TYPES.EDIT,
        icon: ICON_PRIMENG_LIST.PI_PENCIL,
        title: "Edit",
        data: row,
        severity: ICON_BUTTON_SEVERITY.INFO,
        onAction: (rowItem) => this.onOpenCategoryDialog('Edit Category', row, TABLE_ACTION_TYPES.EDIT)
      });
    }

    if (this.ACL_LIST?.CATEGORY_DELETE) {
      iconsList.push({
        type: TABLE_ACTION_TYPES.DELETE,
        icon: ICON_PRIMENG_LIST.PI_TRASH,
        title: "Delete",
        data: row,
        severity: ICON_BUTTON_SEVERITY.DANGER,
        onAction: (rowItem) => this.deleteCategory(row)
      });
    }

    return iconsList;
  }

  private getCategories(): void {
    this._commonService.loadingTableData.next(true);
    this._invokeService.serviceInvocation(APP_UI_CONFIG.master.categories.get)
      .pipe(
        takeUntil(this.$destroyed),
        finalize(() => {
          setTimeout(() => {
            this._commonService.loadingTableData.next(false);
          }, 500);
        })
      )
      .subscribe({
        next: response => {
          this.prepareCategoryData(this._commonService.sortByDateLatest(response, 'createdTime', 'createdTime'));
        },
        error: error => {
          this._commonService.handleError(error);
        }
      });
  }

  private prepareCategoryData(data: any[]): void {
    this.categories = data.map(item => ({
      createdTimeText: this._commonService.dateTimeFormat(item.createdTime),
      ...item
    }));
  }

  private deleteCategory(row: Category): void {
    this._commonService.confirm('Are you sure you want to delete this category?', row.category)
      .pipe(takeUntil(this.$destroyed))
      .subscribe(confirm => {
        if (confirm) {
          this._invokeService.serviceInvocation(APP_UI_CONFIG.master.categories.delete, row.uuid)
            .pipe(takeUntil(this.$destroyed))
            .subscribe({
              next: response => {
                this._commonService.success('Category deleted successfully');
                this.onRefresh();
              },
              error: error => {
                this._commonService.handleError(error);
              }
            });
        }
      });
  }

  private onOpenCategoryDialog(title, row, status): void {
    const ref: DynamicDialogRef = this._dialogService.open(CreateEditCategoryComponent, {
      data: { data: row, status: status },
      header: title,
      modal: true,
      width: '60%',
      closeOnEscape: false,
      dismissableMask: false,
      transitionOptions: "300ms",
      baseZIndex: 10000,
      closable: true,
      focusOnShow: false,
      style: {
        'max-height': '90vh',
        'margin-top': '2rem'
      },
      breakpoints: {
        '768px': '100vw',
        '576px': '100vw'
      }
    });
    ref.onClose.subscribe((response: any) => {
      if (response) {
        this.onRefresh();
      }
    });
  }

  public ngOnDestroy(): void {
    this.$destroyed.next(null);
    this.$destroyed.complete();
  }
}
