<div class="w-full flex flex-col gap-4 card rounded-2xl p-2 pl-4 pr-4">
    <div class="tps-file-header flex flex-row justify-between">
        <tps-header [headerName]="hdr" [showBackIcon]="false" [showNoOfRows]="true"
            [totalNoOfRow]="fileList.length"></tps-header>
        <div class="flex justify-content-center flex-wrap gap-3 mb-4">
            <div class="file-action justify-center flex-wrap gap-3 mt-4">
                <div class="tps-square-button tps-square-button-secondary" (click)="showGridView =!showGridView">
                    <i *ngIf="showGridView" class="pi pi-bars" pTooltip="List View" tooltipPosition="bottom"
                        style="font-size: 1rem"></i>
                    <i *ngIf="!showGridView" class="pi pi-th-large" pTooltip="Grid View" tooltipPosition="bottom"
                        style="font-size: 1rem"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="w-full tps-file-body">
        <div class="files-icon-view flex flex-row gap-4 flex-wrap" *ngIf="showGridView">
            <ng-container *ngFor="let file of fileList">
                <div class="file-wrapper bg-card flex cursor-pointer flex-col rounded-2xl flex flex-col justify-center items-center gap-2"
                    style="padding: 0.25rem" [pTooltip]="file.name" tooltipPosition="bottom">
                    <div class="each-file-wrapper flex flex-col">
                        <img *ngIf="isFileisImageType(file);else noImage" class="file_img rounded-2xl"
                            style="width:140px" [src]="file.src" [alt]="file.name">
                        <!-- <p-image  [alt]="file.name" [src]="file.source"
                            alt="Image" width="50" height="50" [preview]="true" /> -->
                        <ng-template #noImage>
                            <img class="file_img" [src]="getIcon(file)" [alt]="file.name"
                                style="width:80px;height: 50px;">
                        </ng-template>
                        <div class="flex flex-col gap-2 justify-center items-center">
                            <div class="truncate" style="width:120px;text-align: center;">
                                {{file.name}}</div>
                            <div>{{file.modifiedOnText}}</div>
                        </div>
                    </div>
                    <div class="file-action justify-center flex-wrap gap-3 mt-4">
                        <p-button *ngIf="isFileisImageType(file)" icon="pi pi-eye" [rounded]="true" severity="info"
                            (click)="onView(file)" />
                        <p-button icon="pi pi-trash" [rounded]="true" severity="warning" (click)="onDeleteFile(file)" />

                        <!-- <div class="tps-icon-button tps-button-secondary" (click)="onView(file)">
                            <i class="pi pi-eye" style="font-size: 1rem"></i>
                        </div>
                        <div class="tps-icon-button tps-button-danger" (click)="onDeleteFile(file)">
                            <i class="pi pi-trash" style="font-size: 1rem"></i>
                        </div> -->
                    </div>
                </div>
            </ng-container>
        </div>
        <div class="files-icon-view flex flex-col" *ngIf="!showGridView">
            <tps-table class="w-full h-full" ="'30vh'" [showTableHeader]="false" [colDefs]="colDefs"
                [data]="fileList"></tps-table>
        </div>
    </div>

</div>
<p-confirmDialog #cd>
    <ng-template pTemplate="headless" let-message>
        <div class="flex flex-column align-items-center p-5 surface-overlay border-round">
            <div class="border-circle bg-primary inline-flex justify-content-center align-items-center h-6rem w-6rem"
                style="padding: 2rem;border-radius: 50%;color: white;">
                <i class="pi pi-question text-5xl"></i>
            </div>
            <span class="font-bold text-2xl block mb-2 mt-4">
                {{message.header}}
            </span>
            <div class="mb-0" style="font-size: 16px;">Please confirm to proceed.</div>
            <div class="flex align-items-center gap-4 mt-4">
                <tps-primary-button [buttonName]="'Proceed'" (onClick)="cd.accept()"></tps-primary-button>
                <tps-secondary-button [buttonName]="'Cancel'" (onClick)="cd.reject()"></tps-secondary-button>
            </div>
        </div>
    </ng-template>
</p-confirmDialog>