import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { ACL } from 'app/core/acl/acl';
import { AuthService } from 'app/core/auth/auth.service';
import { CommonService } from 'app/shared/tapas-ui';
import { environment } from 'environments/environment';
import { of, switchMap } from 'rxjs';


export const permissionGuard: CanActivateFn = (route, state) => {
  const router: Router = inject(Router);
  const commonService: any = inject(CommonService);
  // Check the authentication status
  return inject(AuthService).check().pipe(
    switchMap((authenticated) => {
      // If the user is not authenticated...
      if (authenticated) {
        let permission: string = route.data["permission"];
        let role = commonService.getUseRole();
        if (ACL[environment.Partner] && ACL[environment.Partner][role]) {
          let roleList: any = ACL[environment.Partner][role];
          if (roleList && roleList[permission]) {
            return of(true);
          } else {
            commonService.error("You are not authorized to access");
            setTimeout(() => {
              router.navigate(['/home'], { queryParams: { returnUrl: state.url } });
            }, 500);
            return of(false);
          }
        } else {
          commonService.error("You are not authorized to access");
          setTimeout(() => {
            router.navigate(['/home'], { queryParams: { returnUrl: state.url } });
          }, 500);
          return of(false);
        }
      }
    })
  );
};


