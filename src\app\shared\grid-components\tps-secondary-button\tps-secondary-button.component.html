<button type="button" [class.button_disabled]="isDisabled" class="tfl-sec-button-wrapper w-full"
    (click)="onButtonClick()" [disabled]="isDisabled">
    <i *ngIf="icon && !isSvg" class="pi {{icon}}" style="font-size: 1rem;color:#517EBC"></i>
    <img *ngIf="isSvg" style="width:18px;height:18px" src="assets/svg/{{icon}}.svg">
    <span style="color:#517EBC !important;">
        {{buttonName}}
    </span>
</button>