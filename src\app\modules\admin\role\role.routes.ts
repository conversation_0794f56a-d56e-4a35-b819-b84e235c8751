import { Routes } from '@angular/router';
import { permissionGuard } from 'app/core/auth/guards/permission.guard';

import { RoleComponent } from './role.component';



export default [
    {
        path: '',
        component: RoleComponent,
        canActivate: [permissionGuard],
        data: { permission: "ROLE_MASTER" },
    },

    { path: 'not-found', loadChildren: () => import('app/modules/page-not-found/page-not-found.routes') },
    { path: '**', pathMatch: 'full', redirectTo: 'not-found' },


] as Routes;