//General
export const FORM_REQUIRED_MESSAGE = "Note: Fields marked with (*) are mandatory"


export enum HTTP_STATUS {
    SUCCESS = 200,
    UNAUTHORIZED = 401,
    NOT_CONTENT = 204,
    BAD_REQUEST = 400,
}
export enum FORM_CONTROL_TYPES {
    TEXT = "text",
    NUMBER = "number",
    SINGLE_SELECT = "single-select",
    MULTI_SELECT = "multi-select",
    RADIO = "radio",
    CHECKBOX = "checkbox",
    DATE = "date",
    VIEW = "view",
    INPUT = "input",
    TEXTAREA = "text-area",
    TEXT_SEARCH = "text-search",
    TIME_DURATION = "time-duration",
    PASSWORD = "password",
    MULTI_FORM_FIELD = "multi_form_field",
    TOGGLE_SWITCH = "toggle_switch",
    SLIDER = "slider",
    TOGGLE_BUTTON = 'toggle_button',
    SELECTED_BUTTON = 'selected_button'
}
export enum FORM_VALIDATION_TYPES {
    REQUIRED = 'required',
    MIN = 'min',
    MAX = 'max',
    MINLENGTH = 'minLength',
    MAXLENGTH = 'maxLength',
    PATTERN = 'pattern',
}
export enum FORM_FIELDS_CONSTANTS_VALUES {
    TEXT_AREA_LENGTH = 500,
    PHONE_NUMBER_LENGTH = 10,
    AADHAR_NUMBER_PATTERN = 12,
    EMAIL_PATTERN = "[a-zA-Z0-9.-_]{1,}@[a-zA-Z0-9.-]{2,}[.]{1}[a-zA-Z]{2,4}",
    MULTIPLE_EMAIL_PATTERN = "^([a-zA-Z][a-zA-Z0-9_.]+@([a-zA-Z0-9-]+\.)+[a-z]{2,6}(, )*)+$",
    NUMERIC_PATTERN = "^[0-9]+$",
    FLOAT_NUMBER_PATTERN = "^[0-9.]+$",
    ALPHA_PATTERN = "^[a-zA-Z](?:[a-zA-Z ]*[a-zA-Z])?$",
    ALPHA_NUMERIC_PATTERN_NO_SPACE = "^[a-zA-Z0-9]+$",
    ALPHA_NUMERIC_PATTERN_WITH_SPACE = "^[a-zA-Z0-9 ]+$",
    BLOOD_GROUP_PATTERN = "^[ aboveABOVE+-]+$",
    PHONE_NUMBER_LENGTH_MIN_LENGTH = 10,
    PHONE_NUMBER_LENGTH_MAX_LENGTH = 14,
    INDIA_PHONE_PATTERN = "^[6-9][0-9]{9}$",
    INTERNATIONAL_PHONE_PATTERN = "^\\+[1-9][0-9]{1,14}$",
    COMMA_SEPARATED_EMAIL_PATTERN = "^([a-zA-Z0-9.-_]{1,}@[a-zA-Z0-9.-]{2,}[.]{1}[a-zA-Z]{2,4}(,)*)+$",
    ALPHA_PATTERN_WITH_SPACE = "^[a-zA-Z ](?:[a-zA-Z ]*[a-zA-Z ])?$",

    PIN_CODE_LENGTH = 6,
    PIN_CODE_LENGTH_MIN_LENGTH = 4,
    PIN_CODE_LENGTH_MAX_LENGTH = 10,
    INPUT_MAX_LENGTH = 255,
    PIN_CODE_PATTERN = "^[1-9][0-9]{5}$",
    UNIVERSAL_PIN_CODE_PATTERN = "^[1-9][0-9]+$",
}

export enum TABLE_ACTION_TYPES {
    CREATE = 'CREATE',
    VIEW = 'VIEW',
    EDIT = 'EDIT',
    DELETE = 'DELETE',
    ACTIVATE = 'ACTIVATE',
    DEACTIVATE = 'DEACTIVATE',
    APPROVE = 'APPROVE',
    ANALYTICS = 'ANALYTICS',
    PIN_DROP = 'PIN_DROP'
}
export enum API_TYPES {
    GET = "GET",
    POST = "POST",
    PUT = "PUT",
    DELETE = "DELETE"
}

export enum ACTION_ICON_COLOR {
    BLUE = "#517EBC",
    GRAY = "#7B8699",
    GREEN = "#55C938",
    RED = "#ED736B"
}

export enum ICON_BUTTON_COLOR {
    PRIMARY = "#3B82F6",
    SECONDARY = "##475569",
    SUCCESS = "#22c55e",
    INFO = "#0ea5e9",
    WARNING = "#f97316",
    HELP = "#a855f7",
    DANGER = "#ef4444",
}
export enum ICON_BUTTON_SEVERITY {
    PRIMARY = "",
    SECONDARY = "secondary",
    SUCCESS = "success",
    INFO = "info",
    WARNING = "warning",
    HELP = "help",
    DANGER = "danger",
}
export enum ACTION_ICON_BUTTON_SEVERITY {
    PRIMARY = "#517EBC",
    SECONDARY = "#7B8699",
    SUCCESS = "#55C938",
    INFO = "#0ea5e9",
    WARNING = "#f97316",
    HELP = "#a855f7",
    DANGER = "#ED736B",
}

export const labelColorDefs = {
    gray: {
        text: 'text-gray-500',
        bg: 'bg-gray-500',
        combined: 'text-gray-800 bg-gray-100'
    },
    red: {
        text: 'text-red-500',
        bg: 'bg-red-500',
        combined: 'text-red-800 bg-red-100'
    },
    orange: {
        text: 'text-orange-500',
        bg: 'bg-orange-500',
        combined: 'text-orange-800 bg-orange-100'
    },
    yellow: {
        text: 'text-yellow-500',
        bg: 'bg-yellow-500',
        combined: 'text-yellow-800 bg-yellow-100'
    },
    green: {
        text: 'text-green-500',
        bg: 'bg-green-500',
        combined: 'text-green-800 bg-green-100'
    },
    teal: {
        text: 'text-teal-500',
        bg: 'bg-teal-500',
        combined: 'text-teal-800 bg-teal-100'
    },
    blue: {
        text: 'text-blue-500',
        bg: 'bg-blue-500',
        combined: 'text-blue-800 bg-blue-100'
    },
    indigo: {
        text: 'text-indigo-500',
        bg: 'bg-indigo-500',
        combined: 'text-indigo-800 bg-indigo-100'
    },
    purple: {
        text: 'text-purple-500',
        bg: 'bg-purple-500',
        combined: 'text-purple-800 bg-purple-100'
    },
    pink: {
        text: 'text-pink-500',
        bg: 'bg-pink-500',
        combined: 'text-pink-800 bg-pink-100'
    }
};
export const labelColors = [
    'gray',
    'red',
    'orange',
    'yellow',
    'green',
    'teal',
    'blue',
    'indigo',
    'purple',
    'pink'
];

export const INDIA_STATE_LIST =
    [{ "label": "Andhra Pradesh", "value": "Andhra Pradesh" }, { "label": "Arunachal Pradesh", "value": "Arunachal Pradesh" }, { "label": "Assam", "value": "Assam" }, { "label": "Bihar", "value": "Bihar" }, { "label": "Chhattisgarh", "value": "Chhattisgarh" }, { "label": "Goa", "value": "Goa" }, { "label": "Gujarat", "value": "Gujarat" }, { "label": "Haryana", "value": "Haryana" }, { "label": "Jharkhand", "value": "Jharkhand" }, { "label": "Karnataka", "value": "Karnataka" }, { "label": "Kerala", "value": "Kerala" }, { "label": "Maharashtra", "value": "Maharashtra" }, { "label": "Manipur", "value": "Manipur" }, { "label": "Meghalaya", "value": "Meghalaya" }, { "label": "Mizoram", "value": "Mizoram" }, { "label": "Nagaland", "value": "Nagaland" }, { "label": "Odisha", "value": "Odisha" }, { "label": "Punjab", "value": "Punjab" }, { "label": "Rajasthan", "value": "Rajasthan" }, { "label": "Sikkim", "value": "Sikkim" }, { "label": "Telangana", "value": "Telangana" }, { "label": "Tripura", "value": "Tripura" }, { "label": "Uttarakhand", "value": "Uttarakhand" }, { "label": "Himachal Pradesh", "value": "Himachal Pradesh" }, { "label": "Madhya Pradesh", "value": "Madhya Pradesh" }, { "label": "Tamil Nadu", "value": "Tamil Nadu" }, { "label": "Uttar Pradesh", "value": "Uttar Pradesh" }, { "label": "West Bengal", "value": "West Bengal" }, { "label": "Andaman and Nicobar Islands", "value": "Andaman and Nicobar Islands" }, { "label": "Dadra and Nagar Haveli", "value": "Dadra and Nagar Haveli" }, { "label": "Daman and Diu", "value": "Daman and Diu" }, { "label": "Jammu and Kashmir", "value": "Jammu and Kashmir" }, { "label": "Chandigarh", "value": "Chandigarh" }, { "label": "Delhi", "value": "Delhi" }, { "label": "Lakshadweep", "value": "Lakshadweep" }, { "label": "Puducherry", "value": "Puducherry" }]



