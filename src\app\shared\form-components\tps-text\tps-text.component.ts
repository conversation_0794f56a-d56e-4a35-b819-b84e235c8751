import { CommonModule, NgF<PERSON>, NgIf } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { FormGroup, FormGroupDirective, ReactiveFormsModule } from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { TooltipModule } from 'primeng/tooltip';

import { FORM_CONTROL_TYPES } from '../../constants/tps-enum-constants';
import { TpsErrorComponent } from '../tps-error/tps-error.component';

@Component({
  selector: 'tps-text',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, NgIf, TpsErrorComponent, InputTextModule, TooltipModule],
  templateUrl: './tps-text.component.html'
})
export class TpsTextComponent implements OnInit {
  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  @Input() field: any;
  @Input() formName: FormGroup;
  @Input() layout: string = 'row';
  @Input() showLabel: boolean = true;

  maxLength: number = 250;

  constructor(private formgroupDirective: FormGroupDirective) {
    this.formName = formgroupDirective.control;
  }

  ngOnInit(): void {
    if (this.field?.rules?.maxLength) {
      this.maxLength = this.field?.rules?.maxLength;
    }
  }
  public onEnterInput(): void {
    if (this.field.showInUpperCase) {
      this.formName.controls[this.field.fieldName].setValue(this.formName.controls[this.field.fieldName].value?.toUpperCase());
      this.formName.updateValueAndValidity();
    }
  }
}
