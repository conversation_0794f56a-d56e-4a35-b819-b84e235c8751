import { Routes } from '@angular/router';
import { permissionGuard } from 'app/core/auth/guards/permission.guard';

import { AuditorListComponent } from './auditor-list.component';

export default [
    {
        path: '',
        component: AuditorListComponent,
        canActivate: [permissionGuard],
        data: { permission: "AUDITOR_MASTER" },
    },
    { path: 'not-found', loadChildren: () => import('app/modules/page-not-found/page-not-found.routes') },
    { path: '**', pathMatch: 'full', redirectTo: 'not-found' },
] as Routes;