export class AuditorIncharge {
    code: string;
    id?: number;
    userName?: string;
    password?: string;
    firstName?: string;
    lastName?: string;
    addressLine1?: string;
    addressLine2?: string;
    city?: string;
    country?: string;
    pincode?: string;
    contactNo?: string;
    email?: string;
    role?: string;
    createdDate?: number;
    modifiedDate?: number;
    status?: number;
    isActive?: boolean;
    mappedWith?: number;
    alternateEmail?: string;
    deleted?: number;

    altemail?: string
    createdBy?: string
    createdTime?: 0
    designation?: string
    deviceImeiNumber?: string
    fullName?: string
    inchargeUid?: string
    loggedOn?: string
    mobileRegId?: string
    modifiedBy?: string
    modifiedTime?: 0
    parentTenantUid?: string
    state?: string
    tenantId?: 0
    tenantUid?: string
    uuid?: string
    setPasswordAsUserName?: boolean
    resetPassword?: boolean
    inspectionAgencyUid?: string
    newPassword?: string
};