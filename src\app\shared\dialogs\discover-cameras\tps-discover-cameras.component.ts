import { CommonModule, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Ng<PERSON><PERSON> } from '@angular/common';
import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';

import { FORM_CONTROL_TYPES, TABLE_ACTION_TYPES } from '../../constants/tps-enum-constants';
import { PrimeNgModule } from '../../primeng.module';
import {
  FormFactoryService,
  TpsPasswordComponent,
  TpsPrimaryButtonComponent,
  TpsSecondaryButtonComponent,
  TpsSingleSelectComponent,
  TpsTextComponent,
} from '../../tapas-ui';
import { DISCOVER_CAMERA_FORM_MODEL } from './discover-form.model';

@Component({
  selector: 'app-discover-cameras',
  standalone: true,
  imports: [TpsPrimaryB<PERSON>onComponent, TpsSecondaryButtonComponent, TpsTextComponent, TpsPasswordComponent, TpsSingleSelectComponent, CommonModule,
    PrimeNgModule,
    NgFor,
    NgIf,
    NgSwitch,
    ReactiveFormsModule,
    FormsModule,],
  templateUrl: './tps-discover-cameras.component.html',
  styleUrl: './tps-discover-cameras.component.scss'
})
export class TpsDiscoverCamerasComponent implements OnInit, OnDestroy {

  public discoverForm: FormGroup;
  public fields: any[] = [];
  public status: string;
  public discoverType: string = 'without';
  public facilityList: any[] = [];
  //constants
  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  public TABLE_ACTION_TYPES = TABLE_ACTION_TYPES;

  constructor(
    private _dialogConfig: DynamicDialogConfig,
    public _dialogConfigRef: DynamicDialogRef,
    private _formFactoryService: FormFactoryService,
  ) { }

  public ngOnInit(): void {
    this.facilityList = this._dialogConfig?.data?.facilityList;
    this.buildForm();
  }

  private buildForm() {
    const formGroupFields = this._formFactoryService.getFormControlsFields(DISCOVER_CAMERA_FORM_MODEL);
    this.fields = this._formFactoryService.getFieldsList(DISCOVER_CAMERA_FORM_MODEL);
    this.discoverForm = new FormGroup(formGroupFields);
    if (this.facilityList.length > 0) {
      this.fields = this._formFactoryService.showHideField(this.fields, 'facilityUid', true);
      this.fields = this._formFactoryService.setFieldsValidation(this.fields, 'facilityUid', { required: true, });
      this.fields = this._formFactoryService.setOptionsToDropDown(this.fields, 'facilityUid', this.facilityList);
      this.discoverForm.get("facilityUid").setValidators(Validators.required);
    } else {
      this.discoverForm.get("facilityUid").removeValidators(Validators.required);
    }

    this.discoverForm.updateValueAndValidity();
    if (this._dialogConfig?.data?.selectedFacility) {
      this.discoverForm.get("facilityUid").setValue(this._dialogConfig?.data?.selectedFacility)
      this.discoverForm.get("facilityUid").disable();
    }
  }

  public onChangeDiscoverType(): void {
    switch (this.discoverType) {
      case "without":
        this.fields = this._formFactoryService.showHideField(this.fields, 'userName', false);
        this.fields = this._formFactoryService.setFieldsValidation(this.fields, 'userName', { required: false, });
        this.discoverForm.get("userName").removeValidators(Validators.required);

        this.fields = this._formFactoryService.showHideField(this.fields, 'password', false);
        this.fields = this._formFactoryService.setFieldsValidation(this.fields, 'password', { required: false, });
        this.discoverForm.get("password").removeValidators(Validators.required);

        break;
      case "with":
        this.fields = this._formFactoryService.showHideField(this.fields, 'userName', true);
        this.fields = this._formFactoryService.setFieldsValidation(this.fields, 'userName', { required: true, });
        this.discoverForm.get("userName").setValidators(Validators.required);

        this.fields = this._formFactoryService.showHideField(this.fields, 'password', true);
        this.fields = this._formFactoryService.setFieldsValidation(this.fields, 'password', { required: true, });
        this.discoverForm.get("password").setValidators(Validators.required);
        break;

      default:
        break;
    }
    this.discoverForm.updateValueAndValidity();
  }

  public onSubmit(): void {
    this._dialogConfigRef.close({ isCredientials: this.discoverType == 'with', data: this.discoverForm.value });
  }

  public close(): void {
    this._dialogConfigRef.close(false);
  }

  public ngOnDestroy(): void {

  }

}
