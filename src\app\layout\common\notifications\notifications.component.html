<!-- Notifications toggle -->
<button mat-icon-button (click)="openPanel()" #notificationsOrigin>
    <ng-container *ngIf="unreadCount > 0">
        <span class="absolute top-0 right-0 left-0 flex items-center justify-center h-3">
            <span
                class="flex items-center justify-center shrink-0 min-w-4 h-4 px-1 ml-4 mt-2.5 rounded-full bg-primary-500 text-indigo-50 text-xs font-medium">
                {{unreadCount}}
            </span>
        </span>
    </ng-container>
    <i #notificationsOrigin class="pi pi-bell mr-4 p-text-secondary" severity="warning"
        style="font-size: 1.5rem !important;color:#517EBC !important;cursor: pointer;"></i>
</button>

<!-- Notifications panel -->
<ng-template #notificationsPanel>

    <div class="fixed inset-0 sm:static sm:inset-auto flex flex-col sm:rounded-2xl overflow-hidden shadow-lg"
        style="width:400px;max-height:500px">

        <!-- Header -->
        <div class="flex shrink-0 items-center py-4 pr-4 pl-6 text-on-primary" style="background-color: #517EBC;">
            <div class="sm:hidden -ml-1 mr-3">
                <button mat-icon-button (click)="closePanel()">
                    <i #notificationsOrigin class="pi pi-times icon-size-5 text-current"></i>

                    <!-- <mat-icon class="icon-size-5 text-current" [svgIcon]="'heroicons_solid:x-mark'"></mat-icon> -->
                </button>
            </div>
            <div class="text-3xl font-medium leading-10">Notifications</div>
            <div class="ml-auto">
                <div *ngIf="unreadCount>0" style="color: white;" class="cursor-pointer" (click)="markAllAsRead()">Mark
                    all as read</div>
            </div>
        </div>

        <!-- Content -->
        <div class="relative flex flex-col flex-auto sm:max-h-120 divide-y overflow-y-auto bg-card">
            <!-- Notifications -->
            <ng-container *ngFor="let notification of notifications; trackBy: trackByFn;let i=index">
                <div class="flex group hover:bg-gray-50 dark:hover:bg-black dark:hover:bg-opacity-5"
                    [ngClass]="{'read-content': notification.readStatus==1}">

                    <!-- Notification with a link -->
                    <ng-container *ngIf="notification.link">
                        <!-- Normal links -->
                        <ng-container *ngIf="!notification.useRouter">
                            <a class="flex flex-auto py-1 pl-6 cursor-pointer" [href]="notification.link">
                                <ng-container *ngTemplateOutlet="notificationContent"></ng-container>
                            </a>
                        </ng-container>
                        <!-- Router links -->
                        <ng-container *ngIf="notification.useRouter">
                            <a class="flex flex-auto py-1 pl-6 cursor-pointer" [routerLink]="notification.link">
                                <ng-container *ngTemplateOutlet="notificationContent"></ng-container>
                            </a>
                        </ng-container>
                    </ng-container>

                    <!-- Notification without a link -->
                    <ng-container *ngIf="!notification.link">
                        <div class="flex flex-auto py-1 pl-6">
                            <ng-container *ngTemplateOutlet="notificationContent"></ng-container>
                        </div>
                    </ng-container>

                    <!-- Actions -->
                    <div class="relative flex flex-col gap-2 justify-between items-center py-4 mr-6 ml-2">
                        <div class="unread-dart-circle" *ngIf="notification.readStatus==0"></div>
                        <div *ngIf="notification.readStatus==1"></div>
                        <notification-action-menu *ngIf="notification.readStatus==0" [notification]="notification"
                            (onClickNotificationUpdate)="toggleRead($event)"></notification-action-menu>
                    </div>
                </div>

                <!-- Notification content template -->
                <ng-template #notificationContent>
                    <!-- Icon -->
                    <ng-container *ngIf="notification.icon && !notification.image">
                        <div
                            class="flex shrink-0 items-center justify-center w-8 h-8 mr-4 rounded-full bg-gray-100 dark:bg-gray-700">
                            <i #notificationsOrigin class="pi {{notification.icon}} icon-size-5"></i>

                            <!-- <mat-icon class="icon-size-5" [svgIcon]="notification.icon">
                            </mat-icon> -->
                        </div>
                    </ng-container>
                    <!-- Image -->
                    <ng-container *ngIf="notification.image">
                        <img class="shrink-0 w-8 h-8 mr-4 rounded-full overflow-hidden object-cover object-center"
                            [src]="notification.image" [alt]="'Notification image'">
                    </ng-container>
                    <!-- Title, description & time -->
                    <div class="flex flex-col flex-auto">
                        <ng-container *ngIf="notification.shortMessage">
                            <div class="font-semibold line-clamp-1" [innerHTML]="notification.shortMessage"></div>
                        </ng-container>
                        <ng-container *ngIf="notification.detailedMessage">
                            <div class="w-full flex flex-row gap-2 ">
                                <b style="color:#517EBC">Message:</b>
                                <div class="line-clamp-2" tooltipPosition="bottom" [pTooltip]="notification.detailedMessage" [innerHTML]="notification.detailedMessage"></div>
                            </div>
                        </ng-container>
                        <ng-container *ngIf="notification.recommendedAction">
                            <div class="w-full flex flex-row gap-2 ">
                                <b style="color:#517EBC">Action:</b>
                                <div class="line-clamp-2" tooltipPosition="bottom"  [pTooltip]="notification.recommendedAction" [innerHTML]="notification.recommendedAction"></div>
                            </div>
                        </ng-container>
                        <div class="mt-2 text-sm leading-none text-secondary">
                            {{notification.createdTime | date:'MMM dd, h:mm:ss a'}}
                        </div>
                    </div>
                </ng-template>
            </ng-container>

            <!-- No notifications -->
            <ng-container *ngIf="!notifications || !notifications.length">
                <div class="flex flex-col flex-auto items-center justify-center sm:justify-start py-12 px-8">
                    <div
                        class="flex flex-0 items-center justify-center w-14 h-14 rounded-full bg-primary-100 dark:bg-primary-600">
                        <i #notificationsOrigin class="pi pi-bell text-primary-700 dark:text-primary-50"></i>
                    </div>
                    <div class="mt-5 text-2xl font-semibold tracking-tight">No notifications</div>
                    <div class="w-full max-w-60 mt-1 text-center text-secondary">When you have notifications,
                        they will appear here.</div>
                </div>
            </ng-container>
            <p-menu #notificationAction [autoZIndex]="true" [model]="notificationActionMenuItems" [popup]="true">
                <ng-template pTemplate="item" let-item>
                    <ng-template>
                        <div class="cursor-pointer" (click)="toggleRead(notifications)">
                            <span [class]="item.icon"></span>
                            <span class="ml-2">{{ item.label }}</span>
                        </div>
                    </ng-template>
                </ng-template>
            </p-menu>

        </div>

    </div>

</ng-template>