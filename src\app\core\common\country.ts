import { SelectItem } from 'primeng/api';

export interface CountryData {
  name: string;
  latitude: number;
  longitude: number;
  code: string;
  phoneCode: string;
}

export class CountryList {
  public countries: SelectItem[];
  public selectedCtry: string = "";
  private data: CountryData[];

  constructor(ctry: string=''){
    this.countries = [];
    this.data = [
      {name: 'Bangladesh', latitude: 23.6850, longitude: 90.3563, code: 'BD', phoneCode: '+880'},
      {name: 'China', latitude: 35.8617, longitude: 104.1954, code: 'CN', phoneCode: '+86'},
      {name: 'Hong Kong', latitude: 22.3193, longitude: 114.1694, code: 'HK', phoneCode: '+852'},
      {name: 'India', latitude: 20.5937, longitude: 78.9629, code: 'IN', phoneCode: '+91'},
      {name: 'Indonesia', latitude: -0.7893, longitude: 113.9213, code: 'ID', phoneCode: '+62'},
      {name: 'Italy', latitude: 41.8719, longitude: 12.5674, code: 'IT', phoneCode: '+39'},
      {name: 'Mauritius', latitude: -20.3484, longitude: 57.5522, code: 'MU', phoneCode: '+230'},
      {name: 'Nepal', latitude: 28.3949, longitude: 84.1240, code: 'NP', phoneCode: '+977'},
      {name: 'Sri Lanka', latitude: 7.8731, longitude: 80.7718, code: 'LK', phoneCode: '+94'},
      {name: 'Thailand', latitude: 15.8700, longitude: 100.9925, code: 'TH', phoneCode: '+66'},
      {name: 'UAE', latitude: 23.4241, longitude: 53.8478, code: 'AE', phoneCode: '+971'},
      {name: 'United Kingdom', latitude: 55.3781, longitude: -3.4360, code: 'GB', phoneCode: '+44'},
      {name: 'Vietnam', latitude: 14.0583, longitude: 108.2772, code: 'VN', phoneCode: '+84'},
      {name: 'Other', latitude: 0, longitude: 0, code: 'XX', phoneCode: '+1'}
    ];

    // Populate countries array for dropdown with proper sorting
    this.data.forEach(country => {
      this.countries.push({label: country.name, value: country.name});
    });

    // Sort countries alphabetically but keep "Other" at the end
    this.countries.sort((a, b) => {
      if (a.value === 'Other') return 1;
      if (b.value === 'Other') return -1;
      return a.label.localeCompare(b.label);
    });

    this.selectedCtry = ctry;
  }

  setCountry(ctry: string){
    this.selectedCtry = ctry;
  }

  getSelectedValue(key: string, attrib: string, def: string = '*') {
    let _value = '';
    if (this.data == null || this.data == undefined) return _value;
    this.data.forEach(element => {
      if (element[key] == this.selectedCtry) {
        _value = element[attrib];
      }
    });
    if (_value == null || _value == undefined || _value == '') _value = def;
    return _value;
  }

  getSelectedCountryData(): CountryData | null {
    if (!this.selectedCtry || !this.data) return null;
    return this.data.find(country => country.name === this.selectedCtry) || null;
  }

  getLatitude(): number | null {
    const countryData = this.getSelectedCountryData();
    return countryData ? countryData.latitude : null;
  }

  getLongitude(): number | null {
    const countryData = this.getSelectedCountryData();
    return countryData ? countryData.longitude : null;
  }

  getCountryCode(): string | null {
    const countryData = this.getSelectedCountryData();
    return countryData ? countryData.code : null;
  }

  getPhoneCode(): string {
    const countryData = this.getSelectedCountryData();
    return countryData ? countryData.phoneCode : '+1';
  }

  isIndia(): boolean {
    return this.selectedCtry === 'India';
  }
}
