import { SelectItem } from 'primeng/api';

export interface CountryData {
  name: string;
  latitude: number;
  longitude: number;
  code: string;
}

export class CountryList {
  public countries: SelectItem[];
  public selectedCtry: string = "";
  private data: CountryData[];

  constructor(ctry: string=''){
    this.countries = [];
    this.data = [
      {name: 'Bangladesh', latitude: 23.6850, longitude: 90.3563, code: 'BD'},
      {name: 'China', latitude: 35.8617, longitude: 104.1954, code: 'CN'},
      {name: 'Hong Kong', latitude: 22.3193, longitude: 114.1694, code: 'HK'},
      {name: 'India', latitude: 20.5937, longitude: 78.9629, code: 'IN'},
      {name: 'Indonesia', latitude: -0.7893, longitude: 113.9213, code: 'ID'},
      {name: 'Italy', latitude: 41.8719, longitude: 12.5674, code: 'IT'},
      {name: 'Mauritius', latitude: -20.3484, longitude: 57.5522, code: 'MU'},
      {name: 'Nepal', latitude: 28.3949, longitude: 84.1240, code: 'NP'},
      {name: 'Sri Lanka', latitude: 7.8731, longitude: 80.7718, code: 'LK'},
      {name: 'Thailand', latitude: 15.8700, longitude: 100.9925, code: 'TH'},
      {name: 'UAE', latitude: 23.4241, longitude: 53.8478, code: 'AE'},
      {name: 'United Kingdom', latitude: 55.3781, longitude: -3.4360, code: 'GB'},
      {name: 'Vietnam', latitude: 14.0583, longitude: 108.2772, code: 'VN'},
      {name: 'Other', latitude: 0, longitude: 0, code: 'XX'}
    ];

    // Populate countries array for dropdown with proper sorting
    this.data.forEach(country => {
      this.countries.push({label: country.name, value: country.name});
    });

    // Sort countries alphabetically but keep "Other" at the end
    this.countries.sort((a, b) => {
      if (a.value === 'Other') return 1;
      if (b.value === 'Other') return -1;
      return a.label.localeCompare(b.label);
    });

    this.selectedCtry = ctry;
  }

  setCountry(ctry: string){
    this.selectedCtry = ctry;
  }

  getSelectedValue(key: string, attrib: string, def: string = '*') {
    let _value = '';
    if (this.data == null || this.data == undefined) return _value;
    this.data.forEach(element => {
      if (element[key] == this.selectedCtry) {
        _value = element[attrib];
      }
    });
    if (_value == null || _value == undefined || _value == '') _value = def;
    return _value;
  }

  getSelectedCountryData(): CountryData | null {
    if (!this.selectedCtry || !this.data) return null;
    return this.data.find(country => country.name === this.selectedCtry) || null;
  }

  getLatitude(): number | null {
    const countryData = this.getSelectedCountryData();
    return countryData ? countryData.latitude : null;
  }

  getLongitude(): number | null {
    const countryData = this.getSelectedCountryData();
    return countryData ? countryData.longitude : null;
  }

  getCountryCode(): string | null {
    const countryData = this.getSelectedCountryData();
    return countryData ? countryData.code : null;
  }
}
