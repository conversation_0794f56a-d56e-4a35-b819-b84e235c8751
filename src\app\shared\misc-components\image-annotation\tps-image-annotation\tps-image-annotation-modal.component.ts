import { <PERSON><PERSON><PERSON>, NgI<PERSON> } from '@angular/common';
import { After<PERSON>iewInit, Component, HostListener, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import * as d3 from 'd3';
import { MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { DividerModule } from 'primeng/divider';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { ToastModule } from 'primeng/toast';
import { TooltipModule } from 'primeng/tooltip';
import { ReplaySubject } from 'rxjs';
import { v4 as uuidv4 } from 'uuid';

import { AnnotationConfiguration, CommonService, InvokeService, TpsPrimaryButtonComponent } from '../../../tapas-ui';
import { Shape } from './shape';

class selectedLabel {
  value: string
  label: string
}
const SHAPES = {
  CIRCLE: 1,
  RECTANGLE: 2,
  POLYGON: 3,
  DELETE: 4,
  CAMERA: 5,
  CROP: 6,
  UNDO: 7,
  REDO: 8,
  ZOOMIN: 9,
  ZOOMOUT: 10,
  FITIMAGE: 11,
  DIRECTION: 12,
}
const SHAPES_LIST = {
  CIRCLE: "CIRCLE",
  RECTANGLE: "RECTANGLE",
  POLYGON: "POLYGON",
  CROP: "CROP",
  DELETE: "DELETE",
  CAMERA: "CAMERA",
  UNDO: "UNDO",
  REDO: "REDO",
  ZOOMIN: "Zoom In",
  ZOOMOUT: "Zoom Out",
  FITIMAGE: "Fit Image",
  DIRECTION: 'Direction',
  DIRECTION_TOP: 'Top Direction',
  DIRECTION_BOTTOM: 'Bottom Direction'
}
const ICON_BASE64 = {
  CAMERA_BASE64: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMjBweCIgdmlld0JveD0iMCAtOTYwIDk2MCA5NjAiIHdpZHRoPSIyMHB4IiBmaWxsPSIjNTE3RUJDIj48cGF0aCBkPSJNNDgwLTI2NHE3MiAwIDEyMC00OXQ0OC0xMTlxMC02OS00OC0xMTguNVQ0ODAtNjAwcS03MiAwLTEyMCA0OS41VDMxMi00MzJxMCA3MCA0OCAxMTl0MTIwIDQ5Wm0wLTcycS00MiAwLTY5LTI3dC0yNy02OHEwLTQwIDI3LTY4LjV0NjktMjguNXE0MiAwIDY5IDI4LjV0MjcgNjguNXEwIDQxLTI3IDY4dC02OSAyN1pNMTY4LTE0NHEtMjkgMC01MC41LTIxLjVUOTYtMjE2di00MzJxMC0yOSAyMS41LTUwLjVUMTY4LTcyMGgxMjBsNzItOTZoMjQwbDcyIDk2aDEyMHEzMCAwIDUxIDIxLjV0MjEgNTAuNXY0MzJxMCAyOS0yMSA1MC41VDc5Mi0xNDRIMTY4WiIvPjwvc3ZnPg==",
  LEFT_DIRECTION_BASE64: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzNyIgaGVpZ2h0PSIzNyIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM1MTdFQkMiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIj48cGF0aCBkPSJNMTEgMTdsLTUtNSA1LTVNMTggMTdsLTUtNSA1LTUiLz48L3N2Zz4=",
  TOP_DIRECTION_BASE64: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzNyIgaGVpZ2h0PSIzNyIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM1MTdFQkMiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIj48cGF0aCBkPSJNMTcgMTFsLTUtNS01IDVNMTcgMThsLTUtNS01IDUiLz48L3N2Zz4=",
  RIGHT_DIRECTION_BASE64: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzNyIgaGVpZ2h0PSIzNyIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM1MTdFQkMiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIj48cGF0aCBkPSJNMTMgMTdsNS01LTUtNU02IDE3bDUtNS01LTUiLz48L3N2Zz4=",
  BOTTOM_DIRECTION_BASE64: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzNyIgaGVpZ2h0PSIzNyIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM1MTdFQkMiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIj48cGF0aCBkPSJNNyAxM2w1IDUgNS01TTcgNmw1IDUgNS01Ii8+PC9zdmc+"

}
@Component({
  selector: 'tps-image-annotation-modal',
  standalone: true,
  imports: [TooltipModule, NgFor, NgIf, DividerModule, ToastModule, ButtonModule, TpsPrimaryButtonComponent, OverlayPanelModule],
  templateUrl: './tps-image-annotation-modal.component.html',
  styleUrl: './tps-image-annotation-modal.component.scss',
  providers: [MessageService]
})
export class TpsImageAnnotationModalComponent implements OnInit, AfterViewInit, OnDestroy {
  selectedImageData: any;
  labelList: any[] = [];
  selectedCities: any[] = [];
  selectedLabelItem: selectedLabel = new selectedLabel();
  imageName: string;
  imageUid: string;
  showDirection: boolean = false;
  selectedLane: any;
  //annotation constants
  iconPath: string = './assets/svg';
  title: string = 'Label(s)';
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);
  annotationConfiguration: AnnotationConfiguration = new AnnotationConfiguration();
  shapesList: any[] = [
    { id: SHAPES.CIRCLE, name: 'Circle', icon: `pi-circle`, severity: 'info', type: SHAPES_LIST.CIRCLE, show: true },
    { id: SHAPES.RECTANGLE, name: 'Rectangle', icon: `pi-box`, severity: 'info', type: SHAPES_LIST.RECTANGLE, show: true },
    { id: SHAPES.POLYGON, name: 'Polygon', icon: `pi-pen-to-square`, severity: 'info', type: SHAPES_LIST.POLYGON, show: true },
    { id: SHAPES.POLYGON, name: 'Crop', icon: `pi-expand`, severity: 'warning', type: SHAPES_LIST.POLYGON, show: true },
    { id: SHAPES.CAMERA, name: 'Camera Icon', icon: `pi-camera`, severity: 'info', type: SHAPES_LIST.CAMERA, show: true },
    { id: SHAPES.DELETE, name: 'Use this tool to delete annotations. Click within a shape and hit Del Key.', icon: `pi-trash`, severity: 'danger', type: SHAPES_LIST.DELETE, show: true },
    { id: SHAPES.UNDO, name: 'Undo', icon: `pi-undo`, severity: 'info', type: SHAPES_LIST.UNDO, show: true },
    { id: SHAPES.REDO, name: 'Redo', icon: `pi-refresh`, severity: 'info', type: SHAPES_LIST.REDO, show: true },
    { id: SHAPES.ZOOMIN, name: 'Zoom In', icon: `pi-plus`, severity: 'info', type: SHAPES_LIST.ZOOMIN, show: true },
    { id: SHAPES.ZOOMOUT, name: 'Zoom Out', icon: `pi-minus`, severity: 'info', type: SHAPES_LIST.ZOOMOUT, show: true },
    { id: SHAPES.FITIMAGE, name: 'Fit Image', icon: `pi-arrows-alt`, severity: 'info', type: SHAPES_LIST.FITIMAGE, show: true },
    { id: SHAPES.DIRECTION, name: 'Up', value: 'top', icon: `pi-arrow-up`, severity: 'info', type: SHAPES_LIST.DIRECTION_TOP, show: this.showDirection },
    { id: SHAPES.DIRECTION, name: 'Down', value: 'bottom', icon: `pi-arrow-down`, severity: 'info', type: SHAPES_LIST.DIRECTION_BOTTOM, show: this.showDirection },
  ];


  //image information

  canvasElement: any;
  ctx_canvas: any;
  offsetX: any;
  offsetY: any

  imageHeight: any;
  imageWidth: any;
  imgURL: any = '';
  originalImageWidth: any = 0;
  originalImageHeight: any = 0;

  currentImageInformation: any = {};
  currentFileName: string = '';
  currentFileExtension: string = '';
  selectedFolderClass: string = '';
  currentImage: any;
  labelName: string = '';
  selectedClassForAnnotation: string = '';
  lowerCanvasImage: any;
  lowerCanvas: any;
  lower_ctx: any;
  zoomInOutCount: number = 1;
  isEdit: boolean = false;

  // Should be dynamic like CLASS_1 CLASS_2 looking from a map

  TOOL_CIRCLE = 0
  TOOL_RECTANGLE = 1
  TOOL_POLYGON = 2
  TOOL_DELETE = 9

  ANNOTATION_ANOMALY = 1
  TOOL_DEFAULT = 5




  TOOL_ZOOM_IN = 10;
  TOOL_ZOOM_OUT = 11;
  STATE_NO_SELECTION: number = -1
  STATE_DEFAULT_ANNOTAION: number = 5 // ANOMALY
  STATE_DEFAULT_SHAPE: number = this.TOOL_DEFAULT // RECTANGLE

  STATE_ANNOTATION_CLASS: number = -1
  STATE_ATTR_NONE: number = -1
  NO_ACTION: number = -1

  STATE_DRAW_NONE: number = -1
  STATE_DRAW_SHAPE: number = 1
  STATE_DRAW_SHAPE_PROGRESS: number = 2

  // on clear on init - refresh 
  defaultDrawingContext: any = {
    currentAction: this.NO_ACTION,
    currentAnnotation: this.STATE_DEFAULT_ANNOTAION,
    currentShape: this.STATE_DEFAULT_SHAPE,
    currentStateAttrs: this.STATE_ATTR_NONE,
    currentState: this.STATE_DRAW_NONE,
    currentHitShape: -1
  }

  currentDrawingContext: any = {
    currentShape: null
  };
  CURRENT_SHAPE_STATUS = {
    isShapeDrawStarted: false,
    isShapeDrawInProgress: false,
    isShapeDrawCompleted: false,
  }

  // 
  mousedown = false;
  clickedArea = { box: -1, pos: 'o' };
  current_box_x1 = -1;
  current_box_y1 = -1;
  current_box_x2 = -1;
  current_box_y2 = -1;
  boxes: any[] = [];
  tmpBox: any = null;


  //polygon code

  shapes: any[] = [];
  dragging: any;
  drawing: any;
  startPoint: any;
  svg: any;


  constructor(
    private _commonService: CommonService,
    private _dialogConfig: DynamicDialogConfig,
    public _dialogConfigRef: DynamicDialogRef,
    private _invokeService: InvokeService,
    private _messageService: MessageService
  ) {

  }

  public ngOnInit(): void {
    this.selectedImageData = this._dialogConfig.data.imageBlob;
    this.imageName = this._dialogConfig.data.imageName;
    this.labelList = this._dialogConfig.data.labelList;
    this.title = this._dialogConfig.data.title;
    this.imageUid = this._dialogConfig?.data?.imageUid;
    this.annotationConfiguration = this._dialogConfig.data.annotationConfiguration;
    this.showDirection = this._dialogConfig.data.showDirection;
    this.prepareShapes();
    this.prepareAndDisplayImage(this.selectedImageData);
    setTimeout(() => {
      if (this._dialogConfig.data.shapes && this._dialogConfig.data.shapes?.length > 0) {
        this.isEdit = true;
        let shapes = this._dialogConfig.data.shapes;
        shapes.forEach((shape, index) => {
          this.selectedLabelItem = new selectedLabel();

          this.selectedLabelItem.value = shape.code;
          this.selectedLabelItem.label = shape.name;
          if (shape.points.length == 2) {
            this.currentDrawingContext.currentShape = SHAPES.RECTANGLE;
            this.rectangleDrawaing = shape;
            this.rectangleDrawaing.type = SHAPES.RECTANGLE;
            this.rectangleDrawaing.points = [{ x: shape.points[0][0], y: shape.points[0][1] }, { x: shape.points[1][0], y: shape.points[1][1] }];
            this.shapes.push(this.rectangleDrawaing);
            this.updateRectRectange();
            this.closeRectangle(this.rectangleDrawaing);
          } else {
            this.currentDrawingContext.currentShape = shape.code?.includes("camera_field_of_view") ? SHAPES.CROP : SHAPES.POLYGON;
            // this.polygonMouseMouseUp({ uuid: shape.uuid, id: shape.id, closed: false, offsetX: shape.points[0][0], offsetY: shape.points[0][1] })
            for (let i = 0; i <= shape.points.length - 1; i++) {
              this.polygonMouseMouseUp({ uuid: shape.uuid, id: shape.id, closed: false, offsetX: shape.points[i][0], offsetY: shape.points[i][1] });
              this.handleMouseMove({ uuid: shape.uuid, id: shape.id, closed: false, offsetX: shape.points[i][0], offsetY: shape.points[i][1] });
              if (i == shape.points.length - 1) {
                this.polygonMouseMouseUp({ uuid: shape.uuid, id: shape.id, closed: true, offsetX: shape.points[0][0], offsetY: shape.points[0][1] })
              }
            }
          }
        })
      }
    }, 500);
  }

  zoomOption: any;
  public ngAfterViewInit() {
    // const svg1 = d3.select('#trainer_image_cropper').call(
    //   this.zoomOption = d3.zoom().on("zoom", (event) => {
    //     this.zoomHandler(event);
    //   })
    // );
  }
  private zoomHandler(event) {
    let { x, y, k } = event.transform;
    d3.select("#trainer_image_cropper").attr("transform", `translate(${x},${y}) scale(${k})`);
  }

  private prepareShapes(): void {
    this.shapesList = [
      { id: SHAPES.CIRCLE, name: 'Circle', icon: `pi-circle`, severity: 'info', type: SHAPES_LIST.CIRCLE, show: this.annotationConfiguration.drawCircle },
      { id: SHAPES.RECTANGLE, name: 'Rectangle', icon: `pi-box`, severity: 'info', type: SHAPES_LIST.RECTANGLE, show: this.annotationConfiguration.drawRectangle },
      { id: SHAPES.POLYGON, name: 'Polygon', icon: `pi-pen-to-square`, severity: 'info', type: SHAPES_LIST.POLYGON, show: this.annotationConfiguration.drawPolygon },
      { id: SHAPES.CROP, name: 'Crop', icon: `pi-expand`, severity: 'warning', type: SHAPES_LIST.POLYGON, show: this.annotationConfiguration.drawCrop },
      { id: SHAPES.CAMERA, name: 'Camera Icon', icon: `pi-camera`, severity: 'info', type: SHAPES_LIST.CAMERA, show: this.annotationConfiguration.drawCamera },
      { id: SHAPES.DELETE, name: 'Use this tool to delete annotations. Click within a shape and hit Del Key.', icon: `pi-trash`, severity: 'danger', type: SHAPES_LIST.DELETE, show: true },
      { id: SHAPES.UNDO, name: 'Undo', icon: `pi-undo`, severity: 'info', type: SHAPES_LIST.UNDO, show: false },
      { id: SHAPES.REDO, name: 'Redo', icon: `pi-refresh`, severity: 'info', type: SHAPES_LIST.REDO, show: false },
      { id: SHAPES.ZOOMIN, name: 'Zoom In', icon: `pi-plus-circle`, severity: 'info', type: SHAPES_LIST.ZOOMIN, show: true },
      { id: SHAPES.ZOOMOUT, name: 'Zoom Out', icon: `pi-minus-circle`, severity: 'info', type: SHAPES_LIST.ZOOMOUT, show: true },
      { id: SHAPES.FITIMAGE, name: 'Fit Image', icon: `pi-arrows-alt`, severity: 'info', type: SHAPES_LIST.FITIMAGE, show: true },
      { id: SHAPES.DIRECTION, name: 'Up', value: 'top', icon: `pi-arrow-up`, severity: 'info', type: SHAPES_LIST.DIRECTION, show: this.showDirection },
      { id: SHAPES.DIRECTION, name: 'Down', value: 'bottom', icon: `pi-arrow-down`, severity: 'info', type: SHAPES_LIST.DIRECTION, show: this.showDirection },
    ];
  }
  private prepareAndDisplayImage(image: any) {
    const reader = new FileReader();
    let svgimg = document.createElementNS('http://www.w3.org/2000/svg', 'image');
    reader.readAsDataURL(image);
    reader.onload = (_event) => {
      let img: any = new Image();
      img.onload = () => {
        this.imageWidth = img.width;
        this.imageHeight = img.height;

        svgimg.setAttributeNS(null, 'height', this.imageHeight);
        svgimg.setAttributeNS(null, 'width', this.imageWidth);
        svgimg.setAttributeNS('http://www.w3.org/1999/xlink', 'href', this.imgURL);
        svgimg.setAttributeNS(null, 'x', '0');
        svgimg.setAttributeNS(null, 'y', '0');
        svgimg.setAttributeNS(null, 'visibility', 'visible');
      };
      img.src = reader.result;
      this.imgURL = reader.result;

      document.getElementById("trainer_image_cropper").appendChild(svgimg);
      this.svg = d3.select('#trainer_image_cropper');
    }
  }
  // // Toobar action 
  public onSelectShape(shape: any): void {
    if (shape.id == SHAPES.DIRECTION) {
      this.selectedLane = shape
    } else {
      if (this.currentDrawingContext.currentShape == shape.id) {
        this.currentDrawingContext.currentShape = '';
      } else {
        if (shape.id == SHAPES.ZOOMIN || shape.id == SHAPES.ZOOMOUT) {
          this.zoom(shape.id);
        }
        else if (shape.id == SHAPES.FITIMAGE) {
          d3.select("#trainer_image_cropper").attr("transform", this.getTransformString(1, 0, 0));
        }
        else {
          this.currentDrawingContext.currentShape = shape.id;
        }
        this.isEdit = false;
      }
    }

  }

  //on cursor mouse up
  public handleMouseUp(event: any): void {
    if (this.currentDrawingContext.currentShape) {
      if (this.currentDrawingContext.currentShape == SHAPES.DELETE) {
        this.onSelectCurrentShape(event);
      } else {
        if (this.selectedLabelItem.value) {
          if (this.currentDrawingContext.currentShape == SHAPES.CIRCLE) {
          }
          if (this.currentDrawingContext.currentShape == SHAPES.RECTANGLE) {
            this.m2 = [event.offsetX, event.offsetY];
            this.rectangleDrawaing.points[1] = { x: this.m2[0], y: this.m2[1] };
            this.closeRectangle(this.rectangleDrawaing);
          }
          if (this.currentDrawingContext.currentShape == SHAPES.CIRCLE) {
            this.circleDrawaing.points[1] = { x: event.offsetX, y: event.offsetY };
            this.closeCircle(this.circleDrawaing);
          }
          if (this.currentDrawingContext.currentShape == SHAPES.POLYGON) {
            this.polygonMouseMouseUp(event);
          }
          if (this.currentDrawingContext.currentShape == SHAPES.CROP) {
            this.polygonMouseMouseUp(event);
          }
        } else {
          if (this.currentDrawingContext.currentShape == SHAPES.CROP) {
            this.polygonMouseMouseUp(event);
          }
          else if (this.currentDrawingContext.currentShape == SHAPES.CAMERA) {
            this.onSetCameraIcon(event)
          } else {
            if (this.currentDrawingContext.currentShape != SHAPES.DELETE) {
              this._messageService.add({ severity: 'error', summary: 'Error', detail: 'Please select the label to draw the annotation' });
            }
          }
        }
      }
    } else {
      this._messageService.add({ severity: 'error', summary: 'Error', detail: 'Please select the shape to draw the annotation' });
    }
  }
  //on mouse down
  public handleMouseDown(event: any): void {
    if (this.currentDrawingContext.currentShape) {
      if (this.currentDrawingContext.currentShape == SHAPES.DELETE) {
        this.onSelectCurrentShape(event);
      } else {
        if (this.selectedLabelItem.value) {
          if (this.currentDrawingContext.currentShape == SHAPES.RECTANGLE) {
            this.rectangeMouseMouseDown(event);
          }
          if (this.currentDrawingContext.currentShape == SHAPES.CIRCLE) {
            this.circleMouseDown(event);
          }
        } else {
          if (this.currentDrawingContext.currentShape != SHAPES.DELETE) {
            this._messageService.add({ severity: 'error', summary: 'Error', detail: 'Please select the label to draw the annotation' });
          }
        }
      }
    } else {
      this._messageService.add({ severity: 'error', summary: 'Error', detail: 'Please select the shape to draw the annotation' });
    }
  }

  public onSetCameraIcon(event: any): any {
    let cameraId = 'camera_' + uuidv4();
    this.svg.append('g').attr('id', cameraId);
    const g: any = this.svg.select('#' + cameraId);
    const circle = g.append("image")
      .attr('x', event.offsetX)
      .attr('y', event.offsetY)
      .attr('id', cameraId)
      .attr('xlink:href', ICON_BASE64.CAMERA_BASE64)
    circle.attr('is-camera', 'true').style('cursor', 'pointer');
    this.currentDrawingContext.currentShape = '';
    this.CURRENT_SHAPE_STATUS.isShapeDrawStarted = false;
    this.CURRENT_SHAPE_STATUS.isShapeDrawInProgress = false;
    this.CURRENT_SHAPE_STATUS.isShapeDrawCompleted = true;
  }

  ////on cursor mouse move
  public handleMouseMove(event: any): any {
    if (this.currentDrawingContext.currentShape == SHAPES.RECTANGLE) {
      if (this.isDown && !this.isDrag) {
        this.rectangleDrawaing.points[1] = { x: event.offsetX, y: event.offsetY };
        this.updateRectRectange();
      }
      else if (this.currentDrawingContext.currentShape == SHAPES.CIRCLE) {
        if (this.isCircleDown && !this.isCircleDrag) {
          this.circleDrawaing.points[1] = { x: event.offsetX, y: event.offsetY };
          this.updateCircleRectange();
        }
      }
      return;
    }
    if (event?.target?.hasAttribute('is-camera')) {
      // const g = this.svg.select('#' + this.drawing.id);
      // g.select('image').remove();
      // this.onSetCameraIcon(event);
      return;
    }
    if (!this.drawing) return;
    const g = this.svg.select('#' + this.drawing.id);
    g.select('line').remove();
    let line = g
      .insert('line', ':first-child')
      .attr('x1', this.startPoint[0])
      .attr('y1', this.startPoint[1])
      .attr('x2', event.offsetX)
      .attr('y2', event.offsetY)
      .attr('stroke', this.currentDrawingContext.currentShape == SHAPES.CROP ? 'green' : 'red')
      .attr('stroke-width', 1);

    this.drawing.points.splice(this.drawing.points.length - 1, 1, [
      event.offsetX,
      event.offsetY,
    ]);
    this.updateShape(this.drawing);

  }


  public polygonMouseMouseUp(event: any): any {
    if (this.currentDrawingContext.currentShape == SHAPES.CAMERA) {
      this.onSetCameraIcon(event);
    } else if (this.currentDrawingContext.currentShape == SHAPES.RECTANGLE) {

    }
    else {
      if (this.dragging) return;
      if (event.closed) {
        return this.closePolygon(this.drawing);
      }
      else if (event?.target?.hasAttribute('is-handle')) {
        return this.closePolygon(this.drawing);
      }
      this.startPoint = [Math.round(event.offsetX), Math.round(event.offsetY)];

      if (!this.drawing) {
        const shape: any = new Shape();
        shape.type = this.currentDrawingContext.currentShape;
        shape.imageUid = this.imageUid;
        shape.uuid = this.isEdit ? event.uuid : null; // to ensure id doesn't start with a number.for explanation
        shape.id = this.isEdit ? event.id : 'shape_' + uuidv4(); // to ensure id doesn't start with a number.for explanation
        shape.code = this.currentDrawingContext.currentShape == SHAPES.CROP ? 'camera_field_of_view' : this.selectedLabelItem.value;
        shape.name = this.currentDrawingContext.currentShape == SHAPES.CROP ? 'Camera Field Of View' : this.selectedLabelItem.label;
        shape.imageName = this.imageName;
        shape.zoneType = this.getZoneType(this.selectedLabelItem.value)
        shape.direction = this.selectedLane ? this.selectedLane.value : '';
        this.shapes.push(shape);
        this.drawing = shape;
        this.svg.append('g').attr('id', shape.id);
        this.drawing.points.push(this.startPoint);
      }
      const g = this.svg.select('#' + this.drawing.id);
      this.drawing.points.push(this.startPoint);
      g.select('polyline').remove();
      let polyline = g
        .append('polyline')
        .attr('points', this.drawing.points)
      // .style('fill', 'red')
      // .attr('stroke', '#000');
      this.CURRENT_SHAPE_STATUS.isShapeDrawStarted = true;
      this.CURRENT_SHAPE_STATUS.isShapeDrawInProgress = true;
      this.CURRENT_SHAPE_STATUS.isShapeDrawCompleted = false;
      this.updateShape(this.drawing, event.closed);
    }
  }

  public handleDrag(self): any {
    return function (d) {
      let dragCircle = d3.select(this);
      let newPoints: any = [];
      let circle: any;
      const newPoint = [d.x, d.y];
      const poly = d3.select(this.parentNode).select('polygon');
      const circles: any = d3.select(this.parentNode).selectAll('circle');
      dragCircle.attr('cx', newPoint[0]).attr('cy', newPoint[1]);
      for (let i = 0; i < circles._groups[0].length; i++) {
        circle = d3.select(circles._groups[0][i]);
        newPoints.push([Number(circle.attr('cx')), Number(circle.attr('cy'))]);
      }
      poly.attr('points', newPoints);

      self.dragging = self.shapes.find((x) => x.id === this.parentNode.id);
      self.dragging.points = newPoints;
      self.updateLineLabels(self.dragging, true);
      self.updateAngleLabels(self.dragging, true);
      self.updateAreaLabel(self.dragging);
    };
  }

  private endDrag(self: any): any {
    return function (d: any) {
      self.dragging = undefined;
    };
  }

  private updateShape(shape: Shape, closed: boolean = false): void {
    this.updateLineLabels(shape, closed);
    this.updateAngleLabels(shape, closed);
    this.updatePoints(shape, closed);
    this.updateAreaLabel(shape);
  }

  private closePolygon(shape: Shape): void {
    const svg = d3.select('#trainer_image_cropper');
    const g: any = this.svg.select('#' + shape.id);
    g.insert('polygon', ':first-child')
      .attr('points', shape.points)
      .style('fill-opacity', '0.5')
      .style('fill', this.currentDrawingContext.currentShape == SHAPES.CROP ? 'lightgrey' : 'lightgrey')
      .attr('stroke', this.currentDrawingContext.currentShape == SHAPES.CROP ? 'green' : 'red');

    g.select('polyline').remove();
    g.select('line').remove();
    this.drawing.points.splice(0, 1);
    this.updateShape(shape, true);
    this.drawing = undefined;
    this.CURRENT_SHAPE_STATUS.isShapeDrawStarted = false;
    this.CURRENT_SHAPE_STATUS.isShapeDrawInProgress = false;
    this.CURRENT_SHAPE_STATUS.isShapeDrawCompleted = true;
    this.currentDrawingContext.currentShape = null;
    this.selectedLane = '';
    this._messageService.add({ severity: 'success', summary: 'Success', detail: `Shape Completed` });
    this.selectedLabelItem = new selectedLabel();
  }

  private updateLineLabels(shape: any, closed: boolean = false): void {
    const g: any = this.svg.select('#' + shape.id);
    g.selectAll('text.line-label').remove();
    let point1;
    let point2;
    for (let i = 1; i < shape.points.length; i++) {
      point1 = shape.points[i - 1];
      point2 = shape.points[i];

      this.addLineLabel(g, point1, point2);
    }
    if (closed) {
      point1 = shape.points[0];
      point2 = shape.points[shape.points.length - 1];
      this.addLineLabel(g, point1, point2);
    }
  }

  private addLineLabel(g: any, point1: any, point2: any): void {
    const midPoint = this.findMidPoint(point1, point2);
    const text = this.findLength(point1, point2);
    if (text > 0) {
      let lineClass = 'line-label';
      let label = g
        .insert('text', ':first-child')
        .attr('x', midPoint.x)
        .attr('y', midPoint.y)
        .attr('text-anchor', 'middle')
        .attr('class', lineClass)
      // .text(text);
    }
  }

  private findMidPoint(point1: any, point2: any): any {
    return { x: (point1[0] + point2[0]) / 2, y: (point1[1] + point2[1]) / 2 };
  }

  private findLength(point1: any, point2: any): any {
    var a = point1[0] - point2[0];
    var b = point1[1] - point2[1];
    return Math.round(Math.sqrt(a * a + b * b) * 100) / 100;
  }

  private updatePoints(shape: Shape, closed: boolean = false): void {
    const g = this.svg.select('#' + shape.id);
    g.selectAll('circle').remove();
    let pointLength = shape.points.length - 1;
    if (closed) {
      pointLength++;
    }
    for (let i = 0; i < pointLength; i++) {
      const point = shape.points[i];
      const circle = g
        .append('circle')
        .attr('cx', point[0])
        .attr('cy', point[1])
        .attr('r', 4)
        .attr('fill', 'white')
        .attr('stroke', 'black')
      if (closed) {
        const dragger = d3
          .drag()
          .on('drag', this.handleDrag(this))
          .on('end', this.endDrag(this));
        circle.call(dragger).style('cursor', 'move');
      } else {
        if (i === 0) {
          circle.attr('is-handle', 'true').style('cursor', 'pointer');
        }
      }
    }
  }

  private updateAngleLabels(shape: any, closed: boolean = false): void {
    let A, B, C;
    const g = this.svg.select('#' + shape.id);
    g.selectAll('text.angle-label').remove();
    if (shape.points.length > 2) {
      for (let i = 0; i < shape.points.length - 2; i++) {
        A = shape.points[i];
        B = shape.points[i + 1];
        C = shape.points[i + 2];
        this.addAngleLabel(g, A, B, C);
      }
      if (closed) {
        A = shape.points[shape.points.length - 1];
        B = shape.points[0];
        C = shape.points[shape.points.length - 2];
        this.addAngleLabel(g, A, B, C);

        A = shape.points[shape.points.length - 2];
        B = shape.points[shape.points.length - 1];
        C = shape.points[0];
        this.addAngleLabel(g, A, B, C);
      }
    }
  }

  private addAngleLabel(g: any, A: any, B: any, C: any): void {
    const angle = this.findAngle(A, B, C);
    if (!isNaN(angle)) {
      let text = g
        .insert('text', ':first-child')
        .attr('x', B[0])
        .attr('y', B[1])
        .attr('text-anchor', 'middle')
        .attr('class', 'angle-label');
      //.text(`${Math.round(angle * 100) / 100}\xB0`);
    }
  }

  private findAngle(A: any, B: any, C: any): any {
    var AB = Math.sqrt(Math.pow(B[0] - A[0], 2) + Math.pow(B[1] - A[1], 2));
    var BC = Math.sqrt(Math.pow(B[0] - C[0], 2) + Math.pow(B[1] - C[1], 2));
    var AC = Math.sqrt(Math.pow(C[0] - A[0], 2) + Math.pow(C[1] - A[1], 2));
    const angle = Math.acos((BC * BC + AB * AB - AC * AC) / (2 * BC * AB));
    return (angle * 180) / Math.PI;
  }

  private updateAreaLabel(shape: Shape): void {
    const g = this.svg.select('#' + shape.id);
    g.select('text.area-label').remove();
    const polygon = g.select('polygon');
    if (!polygon.empty()) {
      const area = d3.polygonArea(shape.points);
      const centroid = d3.polygonCentroid(shape.points);
      let text = g
        .insert('text', ':first-child')
        .attr('x', Math.round(centroid[0]))
        .attr('y', Math.round(centroid[1]))
        .attr('text-anchor', 'middle')
        .attr('class', 'area-label')
        .attr('stroke', this.currentDrawingContext.currentShape == SHAPES.CROP ? 'green' : 'red')
        .text(shape?.name ? shape?.name : this.currentDrawingContext.currentShape == SHAPES.CROP ? 'Camera-Field of View' : this.selectedLabelItem?.label);

      if (this.selectedLane || shape.direction) {
        g.select('image.polygon-direction').remove();
        let direction = g.append("image")
          .attr('x', Math.round(centroid[0]))
          .attr('y', Math.round(centroid[1]))
          .attr('class', 'polygon-direction')
          .attr('xlink:href', this.getDirectionIcon(this.selectedLane ? this.selectedLane.value : shape.direction))
      }
    }
  }

  private getDirectionIcon(direction): any {
    let directinIcon: string;
    switch (direction) {
      case 'left':
        directinIcon = ICON_BASE64.LEFT_DIRECTION_BASE64
        break;
      case 'top':
        directinIcon = ICON_BASE64.TOP_DIRECTION_BASE64
        break;
      case 'right':
        directinIcon = ICON_BASE64.RIGHT_DIRECTION_BASE64
        break;
      case 'bottom':
        directinIcon = ICON_BASE64.BOTTOM_DIRECTION_BASE64
        break;

      default:
        break;
    }
    return directinIcon;
  }
  currentSelectedShape = null;
  // Toobar seleection 
  public onSelectCurrentShape(shape): void {
    this.currentSelectedShape = shape;
    this.currentDrawingContext.currentShape = shape.target.parentNode.id;
    const g = this.svg.select('#' + this.currentSelectedShape.target.parentNode.id);
    if (shape.target.id?.includes("camera")) {
      g.append("circle")
        .style("stroke", this.currentDrawingContext.currentShape == SHAPES.CROP ? 'green' : 'red')
        .style("fill", "none")
        .attr("r", 16)
        .attr("cx", shape.offsetX)
        .attr("cy", shape.offsetY)
        .attr('stroke-dasharray', '5');

    } else {
      g.style('fill-opacity', '0.5')
        .style('fill', 'lightgrey')
        .style('fill', 'lightgrey')
        .attr('stroke', '#f8a100')
        .attr('stroke-dasharray', '5');
    }
  }
  // on press delete
  @HostListener('document:keydown.delete', ['$event']) onDeleteHandler(event: KeyboardEvent) {
    // Everytime we hit the escape the last one is deleted
    // We reset the buffer only for one escape
    this.deleteShape(event);
  }

  private deleteShape(event): void {
    if (this.CURRENT_SHAPE_STATUS.isShapeDrawCompleted) {
      let index = this.shapes.findIndex(shape => shape.id == this.currentSelectedShape.target.parentNode.id);
      this.shapes.splice(index, 1);
      const g = this.svg.select('#' + this.currentSelectedShape.target.parentNode.id);
      g.remove();
      if (!this.currentSelectedShape.target.parentNode.id?.includes("camera")) {
        if (this.shapes.length > 0) {

        } else {
          this.shapes.forEach(shape => {
            switch (shape.type) {
              case SHAPES.POLYGON:
                this.updateShape(shape, false)
                break;
              case SHAPES.RECTANGLE:
                this.updateRectRectange()
                break;
              case SHAPES.CIRCLE:
                this.updateCircleRectange()
                break;
            }
          });
        }
      }
    } else {

    }
  }

  public onActiveLabel(labelItem): void {
    if (!this.annotationConfiguration.allowMultipleShape && this.shapes.findIndex(item => item.code == labelItem.value) > -1) {
      this._commonService.error(`${labelItem.label} already exists.`)
      this.selectedLabelItem = new selectedLabel();
    } else {
      if (this.selectedLabelItem.value == labelItem.value) {
        this.selectedLabelItem = new selectedLabel();
      } else {
        this.selectedLabelItem = labelItem;
      }
    }
  }

  public getAnnotatedLabel(labelItem): boolean {
    return this.shapes.findIndex(item => item.code == labelItem?.value) > -1;
  }

  //save draft annotations and close 
  public onSubmit(): void {
    this._dialogConfigRef.close(this.shapes);
  }

  public onSaveAnnotated(): void {
    const img = document.getElementById("trainer_image_cropper");
    this.downloadSvg(img)
  }



  copyStylesInline(destinationNode, sourceNode) {
    var containerElements = ["svg", "g"];
    for (var cd = 0; cd < destinationNode.childNodes.length; cd++) {
      var child = destinationNode.childNodes[cd];
      if (containerElements.indexOf(child.tagName) != -1) {
        this.copyStylesInline(child, sourceNode.childNodes[cd]);
        continue;
      }
      var style = sourceNode.childNodes[cd].currentStyle || window.getComputedStyle(sourceNode.childNodes[cd]);
      if (style == "undefined" || style == null) continue;
      for (var st = 0; st < style.length; st++) {
        child.style.setProperty(style[st], style.getPropertyValue(style[st]));
      }
    }
  }

  private onUploadAnnotatedDetails(imgURI) {
    let svgBlob = this.b64toBlob(imgURI);
    this._dialogConfigRef.close({ shapes: this.shapes, referenceImage: this.selectedImageData, annotatedImage: svgBlob })
  }
  private b64toBlob(b64Data) {
    // convert base64 to raw binary data held in a string
    // doesn't handle URLEncoded DataURIs - see SO answer #6850276 for code that does this
    var byteString = atob(b64Data.split(',')[1]);

    // separate out the mime component
    var mimeString = b64Data.split(',')[0].split(':')[1].split(';')[0]

    // write the bytes of the string to an ArrayBuffer
    var ab = new ArrayBuffer(byteString.length);
    var ia = new Uint8Array(ab);
    for (var i = 0; i < byteString.length; i++) {
      ia[i] = byteString.charCodeAt(i);
    }

    // write the ArrayBuffer to a blob, and you're done
    var bb = new Blob([ab], { type: "image/png" });
    return bb;
  }

  private downloadSvg(svg) {
    //get svg element.


    //get svg source.
    var serializer = new XMLSerializer();
    var source = serializer.serializeToString(svg);

    // //add name spaces.
    // if (!source.match(/^<svg[^>]+xmlns="http\:\/\/www\.w3\.org\/2000\/svg"/)) {
    //   source = source.replace(/^<svg/, '<svg xmlns="http://www.w3.org/2000/svg"');
    // }
    // if (!source.match(/^<svg[^>]+"http\:\/\/www\.w3\.org\/1999\/xlink"/)) {
    //   source = source.replace(/^<svg/, '<svg xmlns:xlink="http://www.w3.org/1999/xlink"');
    // }

    //add xml declaration
    source = '<?xml version="1.0" standalone="no"?>\r\n' + source;

    //convert svg source to URI data scheme.
    var url = "data:image/svg+xml;charset=utf-8," + encodeURIComponent(source);

    //set url value to a element's href attribute.
    //you can download svg file by right click menu.

    // let copy = svg.cloneNode(true);
    // this.copyStylesInline(copy, svg);
    let canvas: any = document.createElement("canvas");
    let bbox = svg.getBBox();
    canvas.width = bbox.width;
    canvas.height = bbox.height;
    let ctx = canvas.getContext("2d");
    // ctx.clearRect(0, 0, bbox.width, bbox.height);
    // let data = (new XMLSerializer()).serializeToString(copy);
    // let DOMURL: any = window.URL || window.webkitURL || window;
    let img = new Image();
    // let svgBlob = new Blob([data], { type: "image/svg+xml;charset=utf-8" });
    // let url = DOMURL.createObjectURL(svgBlob);
    img.onload = () => {
      ctx.drawImage(img, 0, 0);
      // DOMURL.revokeObjectURL(url);
      var imgURI = canvas
        .toDataURL("image/png")
      this.onUploadAnnotatedDetails(imgURI);
      document.removeChild(canvas);
    };
    img.src = url;

  }


  //-------------------------------------------------------------------
  //Draw Rectange
  private rectData: any[] = [];
  private isDown: boolean = false;
  private isDrag: boolean = false;
  rectangleDrawaing: any;
  m1: any[] = [];
  m2: any[] = [];
  private rect;
  private rectangleElement;
  private pointElement1;
  private pointElement2;
  private pointElement3;
  private pointElement4;

  private rectangeMouseMouseDown(e): void {
    this.m1 = [e.offsetX, e.offsetY];
    if (!this.isDown && !this.isDrag) {
      const shape: any = new Shape();
      shape.type = this.currentDrawingContext.currentShape;
      shape.imageUid = this.imageUid;
      shape.uuid = this.isEdit ? e.uuid : null; // to ensure id doesn't start with a number.for explanation
      shape.id = this.isEdit ? e.id : 'shape_' + uuidv4(); // to ensure id doesn't start with a number.for explanation
      shape.code = this.selectedLabelItem.value;
      shape.name = this.selectedLabelItem.label;
      shape.imageName = this.imageName;
      shape.zoneType = this.getZoneType(this.selectedLabelItem.value)
      shape.direction = this.selectedLane ? this.selectedLane.value : '';
      this.rectangleDrawaing = shape;
      let a = { x: this.m1[0], y: this.m1[1] };
      this.rectData = [];
      this.rectData.push(a);
      this.rectData.push(a);
      this.rectangleDrawaing.points = this.rectData;
      this.shapes.push(this.rectangleDrawaing);
      this.svg.append('g').attr('id', this.rectangleDrawaing.id);
      const g: any = this.svg.select('#' + this.rectangleDrawaing.id);

      this.rectangleElement = g.append('rect').attr('class', 'rectangle');
      this.pointElement1 = g.append('circle').attr('class', 'pointA');
      this.pointElement2 = g.append('circle').attr('class', 'pointB');
      this.pointElement3 = g.append('circle').attr('class', 'pointC');
      this.pointElement4 = g.append('circle').attr('class', 'pointD');

      this.updateRectRectange();
      this.isDrag = false;
    } else {
      this.isDrag = true;
    }
    this.isDown = !this.isDown;
  }

  private updateRectRectange(): void {
    this.shapes.forEach(eachShape => {
      if (eachShape.type == SHAPES.RECTANGLE) {
        if (this.isEdit) {
          this.svg.append('g').attr('id', this.rectangleDrawaing.id);
          const g: any = this.svg.select('#' + this.rectangleDrawaing.id);
          this.rectangleElement = g.append('rect').attr('class', 'rectangle');
          this.pointElement1 = g.append('circle').attr('class', 'pointA');
          this.pointElement2 = g.append('circle').attr('class', 'pointB');
          this.pointElement3 = g.append('circle').attr('class', 'pointC');
          this.pointElement4 = g.append('circle').attr('class', 'pointD');
        }
        const g: any = d3.select('#' + eachShape.id).select("rect");
        g.attr("x", eachShape.points[1].x - eachShape.points[0].x > 0 ? eachShape.points[0].x : eachShape.points[1].x);
        g.attr("y", eachShape.points[1].y - eachShape.points[0].y > 0 ? eachShape.points[0].y : eachShape.points[1].y);
        g.attr("width", Math.abs(eachShape.points[1].x - eachShape.points[0].x));
        g.attr("height", Math.abs(eachShape.points[1].y - eachShape.points[0].y));

        let point1 = d3.select('#' + eachShape.id).select(".pointA");
        const dragger1 = d3.drag().on('drag', (event) => this.dragPoint1(eachShape, event));
        point1.call(dragger1).style('cursor', 'move');
        point1
          .attr('cx', eachShape.points[0].x)
          .attr('cy', eachShape.points[0].y)
          .attr('r', 4)
          .attr('fill', 'white')
          .attr('stroke', 'black')

        let point2 = d3.select('#' + eachShape.id).select(".pointB");
        const dragger2 = d3.drag().on('drag', (event) => this.dragPoint2(eachShape, event));
        point2.call(dragger2).style('cursor', 'move');
        point2
          .attr('cx', eachShape.points[1].x)
          .attr('cy', eachShape.points[1].y)
          .attr('r', 4)
          .attr('fill', 'white')
          .attr('stroke', 'black')

        let point3 = d3.select('#' + eachShape.id).select(".pointC");
        const dragger3 = d3.drag().on('drag', (event) => this.dragPoint3(eachShape, event));
        point3.call(dragger3).style('cursor', 'move');
        point3
          .attr('cx', eachShape.points[1].x)
          .attr('cy', eachShape.points[0].y)
          .attr('r', 4)
          .attr('fill', 'white')
          .attr('stroke', 'black')

        let point4 = d3.select('#' + eachShape.id).select(".pointD");
        const dragger4 = d3.drag().on('drag', (event) => this.dragPoint4(eachShape, event));
        point4.call(dragger4).style('cursor', 'move');
        point4
          .attr('cx', eachShape.points[0].x)
          .attr('cy', eachShape.points[1].y)
          .attr('r', 4)
          .attr('fill', 'white')
          .attr('stroke', 'black')

        this.updateRectangeAreaLabel(eachShape);
      }
    })
  }

  private dragRect(self): any {
    const e = self;
    for (let i = 0; i < this.rectangleDrawaing.points.length; i++) {
      const g = this.svg.select('#' + this.rectangleDrawaing.id).select("rect");
      g.attr('x', (this.rectangleDrawaing.points[i].x += Number(e.offsetX)))
        .attr('y', (this.rectangleDrawaing.points[i].y += Number(e.offsetY)))
        .style('cursor', 'move');
    }
    this.updateRectRectange();
  }

  private dragPoint1(shape, event): any {
    const e = event;
    let g = this.svg.select('#' + shape.id).select(".pointA");
    g.attr('cx', e.dx)
      .attr('cy', e.dy);
    this.updateRectRectange();
  }

  private dragPoint2(shape, event): any {
    const e = event;
    let index = this.shapes.findIndex(item => item.id == shape.id);
    let g = this.svg.select('#' + shape.id).select(".pointB");
    g.attr('cx', (this.shapes[index].points[1].x += Number(e.dx)))
      .attr('cy', (this.shapes[index].points[1].y += Number(e.dy)))
    this.updateRectRectange();
  }

  private dragPoint3(shape, event): any {
    const e = event;
    let index = this.shapes.findIndex(item => item.id == shape.id);
    let g = this.svg.select('#' + shape.id).select(".pointC");
    g.attr('cx', (this.shapes[index].points[1].x += Number(e.dx)))
      .attr('cy', (this.shapes[index].points[0].y += Number(e.dy)))
    this.updateRectRectange();
  }

  private dragPoint4(shape, event): any {
    const e = event;
    let index = this.shapes.findIndex(item => item.id == shape.id);
    let g = this.svg.select('#' + shape.id).select(".pointD");
    g.attr('cx', (this.shapes[index].points[0].x += Number(e.dx)))
      .attr('cy', (this.shapes[index].points[1].y += Number(e.dy)))
    this.updateRectRectange();
  }

  private updateRectangeAreaLabel(shape: Shape): void {
    const g = this.svg.select('#' + shape.id);
    g.select('text.area-label').remove();
    const rect = g.select('rect');
    if (!rect.empty()) {
      let points: any[] = shape.points;
      let mindPointX = Math.round((points[0].x + points[1].x) / 2);
      let mindPointY = Math.round((points[0].y + points[1].y) / 2);
      let text = g
        .insert('text', ':first-child')
        .attr('x', mindPointX)
        .attr('y', mindPointY)
        .attr('text-anchor', 'middle')
        .attr('class', 'area-label')
        .attr('stroke', this.currentDrawingContext.currentShape == SHAPES.CROP ? 'green' : 'red')
        .text(shape?.name ? shape?.name : this.currentDrawingContext.currentShape == SHAPES.CROP ? 'Camera-Field of View' : this.selectedLabelItem?.label);

      if (this.selectedLane || shape.direction) {
        g.select('image.rectangle-direction')?.remove();
        let direction = g.append("image")
          .attr('x', mindPointX)
          .attr('y', mindPointY)
          .attr('class', 'rectangle-direction')
          .attr('xlink:href', this.getDirectionIcon(this.selectedLane ? this.selectedLane.value : shape.direction))
      }

    }


  }

  private closeRectangle(shape: Shape): void {
    const svg = d3.select('#trainer_image_cropper');
    const g: any = this.svg.select('#' + shape.id).select("rect");
    g.attr('points', shape.points)
      .style('fill-opacity', '0.5')
      .style('fill', this.currentDrawingContext.currentShape == SHAPES.CROP ? 'lightgrey' : 'lightgrey')
      .attr('stroke', this.currentDrawingContext.currentShape == SHAPES.CROP ? 'green' : 'red');
    this.isDown = false;
    this.isDrag = false;
    this.updateRectRectange();
    this.selectedLabelItem = new selectedLabel();
    this.currentDrawingContext.currentShape = null;
    this.CURRENT_SHAPE_STATUS.isShapeDrawCompleted = true;
    this.selectedLane = '';
  }



  //Circle
  private circleData: any[] = [];
  private isCircleDown: boolean = false;
  private isCircleDrag: boolean = false;
  circleDrawaing: any;


  private circleMouseDown(e): void {
    if (!this.isCircleDown && !this.isCircleDrag) {
      const shape: any = new Shape();
      shape.type = this.currentDrawingContext.currentShape;
      shape.imageUid = this.imageUid;
      shape.uuid = this.isEdit ? e.uuid : null; // to ensure id doesn't start with a number.for explanation
      shape.id = this.isEdit ? e.id : 'shape_' + uuidv4(); // to ensure id doesn't start with a number.for explanation
      shape.code = this.selectedLabelItem.value;
      shape.name = this.selectedLabelItem.label;
      shape.imageName = this.imageName;
      shape.zoneType = this.getZoneType(this.selectedLabelItem.value)
      shape.direction = this.selectedLane ? this.selectedLane.value : '';
      this.circleDrawaing = shape;
      let a = { x: e.offsetX, y: e.offsetY };
      this.circleData = [];
      this.circleData.push(a);
      this.circleData.push(a)
      this.circleDrawaing.points = this.circleData;
      this.shapes.push(this.circleDrawaing);
      this.svg.append('g').attr('id', this.circleDrawaing.id);
      const g: any = this.svg.select('#' + this.circleDrawaing.id);

      let circleleElement = g.append('circle').attr('class', 'circle');
      let pointElement1 = g.append('circle').attr('class', 'pointX');

      this.updateCircleRectange();
      this.isCircleDrag = false;
    } else {
      this.isCircleDrag = true;
    }
    this.isCircleDown = !this.isCircleDown;
  }

  private updateCircleRectange(): void {
    this.shapes.forEach(eachShape => {
      if (eachShape.type == SHAPES.CIRCLE) {
        const g: any = d3.select('#' + eachShape.id).select("circle");
        let h = Math.pow((eachShape.points[1].x - eachShape.points[0].x), 2);
        let y = Math.pow((eachShape.points[1].y - eachShape.points[0].y), 2);
        let radius = Math.round(Math.sqrt(h + y));
        g.attr("cx", eachShape.points[0].x)
          .attr("cy", eachShape.points[0].y)
          .attr('r', radius)
          .attr('stroke', 'green')

        let point1 = d3.select('#' + eachShape.id).select(".pointX");
        const dragger1 = d3.drag().on('drag', (event) => this.dragCircle2(eachShape, event));
        point1.call(dragger1).style('cursor', 'move');
        point1
          .attr('cx', eachShape.points[1].x)
          .attr('cy', eachShape.points[1].y)
          .attr('r', 4)
          .attr('fill', 'white')
          .attr('stroke', 'black')
      }

    })
  }

  private dragCircle2(shape, event): any {
    const e = event;
    let index = this.shapes.findIndex(item => item.id == shape.id);
    let g = this.svg.select('#' + shape.id).select(".pointB");
    g.attr('cx', (this.shapes[index].points[1].x += Number(e.dx)))
      .attr('cy', (this.shapes[index].points[1].y += Number(e.dy)))
    this.updateCircleRectange();
  }

  private closeCircle(shape: Shape): void {
    const svg = d3.select('#trainer_image_cropper');
    const g: any = this.svg.select('#' + shape.id).select("circle");
    let index = this.shapes.findIndex(item => item.id == shape.id);
    this.shapes[index].points[1] = this.circleDrawaing.points[1];
    g.attr('points', shape.points)
      .style('fill-opacity', '0.5')
      .style('fill', this.currentDrawingContext.currentShape == SHAPES.CROP ? 'lightgrey' : 'lightgrey')
      .attr('stroke', this.currentDrawingContext.currentShape == SHAPES.CROP ? 'green' : 'red');
    this.isCircleDown = false;
    this.isCircleDrag = false;
    this.updateCircleRectange();
    this.selectedLabelItem = new selectedLabel();
    this.currentDrawingContext.currentShape = null;
    this.CURRENT_SHAPE_STATUS.isShapeDrawCompleted = true;
    this.selectedLane = '';
  }


  private zoom(direction) {
    const { scale, x, y } = this.getTransformParameters(document.getElementById("trainer_image_cropper"));
    let dScale = 0.1;
    if (direction == SHAPES.ZOOMOUT) {
      dScale *= -1;
    }
    if (scale == 0.1 && direction == SHAPES.ZOOMOUT) {
      dScale = 0;
    }
    d3.select("#trainer_image_cropper").attr("transform", this.getTransformString(scale + dScale, x, y));
  };

  private getTransformParameters(element) {
    const transform = d3.select("#trainer_image_cropper").attr("transform");
    let scale = 1,
      x = 0,
      y = 0;

    if (transform?.includes("scale"))
      scale = parseFloat(transform.slice(transform.indexOf("scale") + 6));
    if (transform?.includes("translateX"))
      x = parseInt(transform.slice(transform.indexOf("translateX") + 11));
    if (transform?.includes("translateY"))
      y = parseInt(transform.slice(transform.indexOf("translateY") + 11));

    return { scale, x, y };
  };
  private getTransformString(scale, x, y) {
    return `translate(${x},${y}) scale(${scale})`
  }


  public ngOnDestroy(): void {

  }



  private getZoneType(uuid): void {
    return this.labelList.find(item => item.value == uuid)?.raw?.zoneType || '';

  }
}

