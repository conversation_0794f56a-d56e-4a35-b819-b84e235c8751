import { Routes } from '@angular/router';
import { permissionGuard } from 'app/core/auth/guards/permission.guard';



export default [
    {
        path: 'tech-park',
        canActivate: [permissionGuard],
        data: { permission: "TECH_PARK" },
        loadChildren: () => import('./tech-park/tech-park.routes')
    },
    { path: 'not-found', loadChildren: () => import('app/modules/page-not-found/page-not-found.routes') },
    { path: '**', pathMatch: 'full', redirectTo: 'not-found' },
] as Routes;
