import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ase } from '@angular/common';
import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { FORM_CONTROL_TYPES, FormFactoryService, TABLE_ACTION_TYPES } from 'app/shared/tapas-ui';
import { DividerModule } from 'primeng/divider';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { InputTextModule } from 'primeng/inputtext';
import { TooltipModule } from 'primeng/tooltip';
import { BehaviorSubject, ReplaySubject, takeUntil } from 'rxjs';

import { TpsDatePickerComponent } from '../../form-components/tps-date-picker/tps-date-picker.component';
import { TpsMultiSelectComponent } from '../../form-components/tps-multi-select/tps-multi-select.component';
import { TpsNumberComponent } from '../../form-components/tps-number/tps-number.component';
import { TpsRadioComponent } from '../../form-components/tps-radio/tps-radio.component';
import { TpsSingleSelectComponent } from '../../form-components/tps-single-select/tps-single-select.component';
import { TpsTextComponent } from '../../form-components/tps-text/tps-text.component';
import { TpsPrimaryButtonComponent } from '../tps-primary-button/tps-primary-button.component';

@Component({
  selector: 'tps-filter',
  standalone: true,
  imports: [
    NgFor,
    NgIf,
    NgSwitch,
    NgSwitchCase,
    InputTextModule,
    IconFieldModule,
    InputIconModule,
    TooltipModule,
    FormsModule,
    TpsPrimaryButtonComponent,
    DividerModule,
    ReactiveFormsModule,
    TpsDatePickerComponent,
    TpsMultiSelectComponent,
    TpsNumberComponent,
    TpsRadioComponent,
    TpsSingleSelectComponent,
    TpsTextComponent],
  templateUrl: './tps-filter.component.html',
})
export class TpsFilterComponent implements OnInit, OnDestroy {
  @Input() mainFilterModel: BehaviorSubject<any> = new BehaviorSubject<any>(null);
  @Output() onChangeFilter: EventEmitter<any> = new EventEmitter();
  mainFilterFormGroup: FormGroup;
  mainFilterFields: any[] = [];

  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  public TABLE_ACTION_TYPES = TABLE_ACTION_TYPES;


  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);
  constructor(
    private _formFactoryService: FormFactoryService,
  ) { }



  public ngOnInit(): void {
    this.mainFilterModel
      .pipe(takeUntil(this.$destroyed))
      .subscribe((value) => {
        let model = value;
        this.buildMainFilterForm(model);
      });
  }
  private buildMainFilterForm(mainFilterModel): void {
    const formGroupFields = this._formFactoryService.getFormControlsFields(mainFilterModel);
    this.mainFilterFormGroup = new FormGroup(formGroupFields);
    this.mainFilterFields = this._formFactoryService.getFieldsList(mainFilterModel);
    this.onMainFilterChange();
  }

  public onMainFilterChange(): void {
    this.onChangeFilter.emit(this.mainFilterFormGroup.value)
  }




  public ngOnDestroy(): void {

  }

}
