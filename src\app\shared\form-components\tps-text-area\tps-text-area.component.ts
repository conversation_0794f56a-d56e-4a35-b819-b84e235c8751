import { CommonModule, NgF<PERSON>, NgIf } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { FormGroup, FormGroupDirective, ReactiveFormsModule } from '@angular/forms';
import { TextareaModule } from 'primeng/textarea';
import { TooltipModule } from 'primeng/tooltip';

import { FORM_CONTROL_TYPES } from '../../constants/tps-enum-constants';
import { TpsErrorComponent } from '../tps-error/tps-error.component';


@Component({
  selector: 'tps-text-area',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, NgIf, TpsErrorComponent, TextareaModule, TooltipModule],
  templateUrl: './tps-text-area.component.html'
})
export class TpsTextAreaComponent implements OnInit {
  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  @Input() field: any;
  @Input() formName: FormGroup;
  @Input() layout: string = 'row';

  maxLength: number = 250;

  constructor(private formgroupDirective: FormGroupDirective) {
    this.formName = formgroupDirective.control;
  }

  ngOnInit(): void {
    if (this.field?.rules?.maxLength) {
      this.maxLength = this.field?.rules?.maxLength;
    }
  }

}

