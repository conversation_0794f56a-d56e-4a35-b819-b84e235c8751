import { Routes } from '@angular/router';
import { AuthResetPasswordComponent } from 'app/modules/auth/reset-password/reset-password.component';

export default [
    {
        path     : '',
        component: AuthResetPasswordComponent,
    },
    { path: 'not-found', loadChildren: () => import('app/modules/page-not-found/page-not-found.routes') },
  { path: '', pathMatch: 'full', redirectTo: 'not-found' },
] as Routes;
