.annotation_wrapper {
    padding: 1.25rem;

    .annotation-preview-wrapper {
        width: 85vw;
        height: 73vh;
        overflow: scroll;
        padding-bottom: 1rem;
        // border: 1px solid lightgray;
        border-radius: 5px;
        padding: 5px;

        .upperCanvas {
            z-index: 9999999999999 !important;
        }
    }

    .shape {
        padding: 0.5rem;
    }



    .each_label {
        padding: 0.5rem 0.75rem;
        border: 0 none;
        color: #334155;
        transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s;
        border-radius: 4px;
        cursor: pointer;
        border-bottom: 1px solid #cbddef;
    }

    .each_label:hover {
        color: #ffffff;
        background: #0ea5e9;
    }

    .selected-label {
        color: #ffffff;
        background: #22c55e;
    }

    .active-label {
        color: #ffffff;
        background: #0ea5e9;
    }
}

// ::ng-deep path {
//     fill: lightsalmon;
//     stroke: salmon;
//     stroke-width: 10px;
// }

.lineLabel {
    color: black;
}

#trainer_image_cropper {
    transition: transform 0.1s linear 0.1s;
}