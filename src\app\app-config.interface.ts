export interface IAppConfig {
    configuration: {
        getTenant: {
            url: string,
            type: string,
        },
        getTenantConfig: {
            url: string,
            type: string,
            paramList: { tenantUid: string }
        }
    },
    auth: {
        login: {
            url: string,
            type: string,
        },
        mfaRegistration: {
            url: string,
            type: string,
        },
        mfaValidation: {
            url: string,
            type: string,
            paramList: { mfaCode: string }
        }
    },
    master: {
        auditor: {
            get: {
                url: string,
                type: string,
            },
            getAllAuditors: {
                url: string,
                type: string,
            },
            create: {
                url: string,
                type: string,
            },
            update: {
                url: string,
                type: string,
                paramList: { uuid: string }
            },
            activate: {
                url: string,
                type: string,
                paramList: { uuid: string }
            },
            deActivate: {
                url: string,
                type: string,
                paramList: { uuid: string }
            }
            delete: {
                url: string,
                type: string,
                paramList: { uuid: string }
            }

        },
        auditorInCharge: {
            get: {
                url: string,
                type: string,
            },
            getAllAuditorInCharge: {
                url: string,
                type: string,
            },
            create: {
                url: string,
                type: string,
            },
            update: {
                url: string,
                type: string,
                paramList: { uuid: string }
            },
            delete: {
                url: string,
                type: string,
                paramList: { uuid: string }
            },
        },
        brand: {
            get: {
                url: string,
                type: string,
            },
            create: {
                url: string,
                type: string,
            },
            update: {
                url: string,
                type: string,
                paramList: { brandUid: string }
            },
            delete: {
                url: string,
                type: string,
                paramList: { brandUid: string }
            },
            activate: {
                url: string,
                type: string,
                paramList: { brandUid: string }
            },
            deactivate: {
                url: string,
                type: string,
                paramList: { brandUid: string }
            }
        },
        categories: {
            get: {
                url: string,
                type: string,
            },
            create: {
                url: string,
                type: string,
            },
            update: {
                url: string,
                type: string,
                paramList: { uuid: string }
            },
            delete: {
                url: string,
                type: string,
                paramList: { uuid: string }
            }
        },
        defectType: {
            get: {
                url: string,
                type: string,
            },
            create: {
                url: string,
                type: string,
            },
            update: {
                url: string,
                type: string,
            },
            activate: {
                url: string,
                type: string,
                payload: any,
                paramList: { defectUid: string }
            },
            deactivate: {
                url: string,
                type: string,
                paramList: { defectUid: string }
            },
        },
        productType: {
            get: {
                url: string,
                type: string,
            },
            create: {
                url: string,
                type: string,
            },
            update: {
                url: string,
                type: string,
            },
            delete: {
                url: string,
                type: string,
                paramList: { productTypeUid: string }
            },
            activate: {
                url: string,
                type: string,
                paramList: { productTypeUid: string }
            },
            deactivate: {
                url: string,
                type: string,
                paramList: { productTypeUid: string }
            },
        },
        factory: {
            get: {
                url: string,
                type: string,
                param: string
            },
            getByUid: {
                url: string,
                type: string,
                paramList: { uuid: string }
            },
            activate: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            deactivate: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            add: {
                url: string,
                type: string,
                payload: any
            },
            addFactory: {
                url: string,
                type: string,
                payload: any
            },
            update: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            getByVendor: {
                url: string,
                type: string,
                id: any,
                paramList: { id: any }
            },
            delete: {
                url: string,
                type: string,
                paramList: { uuid: any }
            }
        },
         
    },
    admin: {
        roles: {
            getUserRoleList: {
                url: string,
                type: string,
            },
            createORUpdate: {
                url: string,
                type: string,
            },
            update: {
                url: string,
                type: string,
                paramList: { uuid: string }
            },
            delete: {
                url: string,
                type: string
            }

        },
        users: {
            get: {
                url: string,
                type: string,
                paramList: { role: string }
            },
            create: {
                url: string,
                type: string,
            },
            update: {
                url: string,
                type: string,
                paramList: { uuid: string }
            },
            delete: {
                url: string,
                type: string,
                paramList: { uuid: string }
            },
            activateUser: {
                url: string,
                type: string,
                paramList: { uuid: string }
            },
            deActivateUser: {
                url: string,
                type: string,
                paramList: { uuid: string }
            }
        },
         technician: {
            get: {
                url: string,
                type: string,
            },
            create: {
                url: string,
                type: string,
            },
            update: {
                url: string,
                type: string,
                paramList: { technicianUid: string }
            },
            delete: {
                url: string,
                type: string,
                paramList: { technicianUid: string }
            }
        },
        vendors: {
            get: {
                url: string,
                type: string,
            },
            create: {
                url: string,
                type: string,
            },
            update: {
                url: string,
                type: string,
                paramList: { vendorUid: string }
            },
            delete: {
                url: string,
                type: string,
                paramList: { vendorUid: string }
            },
            activate: {
                url: string,
                type: string,
                paramList: { vendorUid: string }
            },
            deactivate: {
                url: string,
                type: string,
                paramList: { vendorUid: string }
            }
        },
    },
    document: {
        techPark: {
            get: {
                url: string,
                type: string,
            },
            create: {
                url: string,
                type: string,
            },
            update: {
                url: string,
                type: string,
                paramList: { uuid: string }
            },
            delete: {
                url: string,
                type: string,
                paramList: { uuid: string }
            }
        }
    },
    notification: {
        get: {
            url: string,
            type: string
        },
        getLatestNotification: {
            url: string,
            type: string
        },
        updateNotificationStatusByUid: {
            url: string,
            type: string,
            paramList: { uuid: any, status: any },
        },
        getNotificationHistory: {
            url: string,
            type: string,
            paramList: { from: any, to: any },
        },
        readAllNotifications: {
            url: string,
            type: string,
        }
    },

}
