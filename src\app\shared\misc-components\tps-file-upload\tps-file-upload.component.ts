import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@angular/common';
import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { PrimeNG } from 'primeng/config';
import { FileUpload } from 'primeng/fileupload';

import { CommonService } from '../../tapas-ui';


interface UploadEvent {
  originalEvent: Event;
  files: File[];
}

@Component({
  selector: 'tps-file-upload',
  standalone: true,
  imports: [FileUpload, ButtonModule],
  templateUrl: './tps-file-upload.component.html',
  styleUrl: './tps-file-upload.component.scss'
})
export class TpsFileUploadComponent implements OnInit, OnDestroy {
  files: any[] = [];

  @Input() isShowFileList: boolean = true;
  @Input() required: boolean = false;
  @Input() multiple: boolean = false;
  @Input() message: string = '';
  @Input() fileSize: any = null;
  @Input() accept: string = '*';
  @Output() onUpload: EventEmitter<any> = new EventEmitter();
  @Output() deleteFile: EventEmitter<any> = new EventEmitter();

  constructor(
    public _commonService: CommonService,
    private config: PrimeNG
  ) { }
  public ngOnInit(): void {
    let allowFileMessage: String = this.multiple ? 'Multiple files can be uploaded' : 'Only single file can be uploaded';
    let fileSizeMessage = this.fileSize ? `and file should be less than ${this.fileSize}` : '';
    this.message = `Please upload ${this.accept} only. ${allowFileMessage} ${fileSizeMessage}`;
    this.files = [];
  }

  public uploadFile(event: UploadEvent) {
    this.onUpload.emit(event.files);
  }
  public onRemove(event): void {
    this.onUpload.emit(event.files);
  }
  public ngOnDestroy(): void {

  }
}
