import { CommonModule, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgS<PERSON>, Ng<PERSON><PERSON><PERSON><PERSON>, NgSwitchDefault } from '@angular/common';
import { Component, EventEmitter, Input, OnChanges, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { fuseAnimations } from '@fuse/animations';
import {
  CommonService,
  FORM_CONTROL_TYPES,
  FormFactoryService,
  TABLE_ACTION_TYPES,
  TpsFilterComponent,
} from 'app/shared/tapas-ui';
import { BadgeModule } from 'primeng/badge';
import { ButtonModule } from 'primeng/button';
import { CheckboxModule } from 'primeng/checkbox';
import { DividerModule } from 'primeng/divider';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { InputTextModule } from 'primeng/inputtext';
import { MenuModule } from 'primeng/menu';
import { PopoverModule } from 'primeng/popover';
import { SkeletonModule } from 'primeng/skeleton';
import { TableModule } from 'primeng/table';
import { TagModule } from 'primeng/tag';
import { TooltipModule } from 'primeng/tooltip';
import { BehaviorSubject, ReplaySubject, takeUntil } from 'rxjs';

import { TpsDatePickerComponent } from '../../form-components/tps-date-picker/tps-date-picker.component';
import { TpsMultiSelectComponent } from '../../form-components/tps-multi-select/tps-multi-select.component';
import { TpsNumberComponent } from '../../form-components/tps-number/tps-number.component';
import { TpsRadioComponent } from '../../form-components/tps-radio/tps-radio.component';
import { TpsSingleSelectComponent } from '../../form-components/tps-single-select/tps-single-select.component';
import { TpsTextComponent } from '../../form-components/tps-text/tps-text.component';
import { buttonActions, toolbarActions } from '../../models/tps-table-action-model';
import { TpsHeaderComponent } from '../tps-header/tps-header.component';
import { TpsPrimaryButtonComponent } from '../tps-primary-button/tps-primary-button.component';
import { TpsSecondaryButtonComponent } from '../tps-secondary-button/tps-secondary-button.component';
import { TpsCellActionComponent } from './tps-cell-action/tps-cell-action.component';
import { HtmlCellComponent } from './tps-table-cell-html';

@Component({
  selector: 'tps-table',
  standalone: true,
  imports: [
    NgFor,
    NgIf,
    NgSwitch,
    NgSwitchCase,
    TpsHeaderComponent,
    InputTextModule,
    IconFieldModule,
    InputIconModule,
    TooltipModule,
    FormsModule,
    PopoverModule,
    BadgeModule,
    TpsPrimaryButtonComponent,
    TpsSecondaryButtonComponent,
    DividerModule,
    CommonModule,
    ReactiveFormsModule,
    TpsDatePickerComponent,
    TpsMultiSelectComponent,
    TpsNumberComponent,
    TpsRadioComponent,
    TpsSingleSelectComponent,
    TpsTextComponent,
    TableModule,
    MatMenuModule,
    MenuModule,
    MatButtonModule,
    ButtonModule,
    TooltipModule,
    SkeletonModule,
    TpsCellActionComponent,
    TagModule,
    HtmlCellComponent,
    CheckboxModule
  ],

  templateUrl: './tps-table.component.html',
  styleUrl: './tps-table.component.css',
  animations: fuseAnimations
})
export class TpsTableComponent implements OnInit {
  @Input() showTableHeader: boolean = true;
  @Input() showRefreshIcon: boolean = true;
  @Input() tableHeader: string = '';
  @Input() showBackIcon: boolean = false;
  @Input() showNoOfRows: boolean = true;
  @Input() height: any = '75vh';
  @Input() headerTooltip: string = '';
  @Input() isLoading: boolean = false;
  @Input() showSelectionColumn: boolean = false;
  @Input() selectionMode: string = 'multiple';

  //inputs for table

  @Input() set colDefs(_data) {
    this.columnsDef = _data;
    this.setColumnsDefs(_data)
  }
  @Input() set data(_data) {
    this.rowData = _data;
    this.filteredData = _data;
    this.tableDataCount = this.filteredData.length;
  }

  @Input() buttonActions: buttonActions[] = [];
  @Input() toolBarActions: toolbarActions[] = [];
  @Input() quickFilterFormFields: any = {};
  @Input() mainFilterModel: BehaviorSubject<any> = new BehaviorSubject<any>(null);
  @Input() metaData: any;

  //Emitters
  @Output() onAction: EventEmitter<any> = new EventEmitter();
  @Output() onRefresh: EventEmitter<any> = new EventEmitter();
  @Output() onSelectionChange: EventEmitter<any> = new EventEmitter();
  @Output() onChangeQuickFilter: EventEmitter<any> = new EventEmitter();
  @Output() onChangeFilter: EventEmitter<any> = new EventEmitter();
  @Output() selectionChange = new EventEmitter<any[]>();

  // Add this getter/setter for two-way binding
  private _selection: any[] = [];

  @Input()
  get selection(): any[] {
    return this._selection;
  }
  set selection(value: any[]) {
    this._selection = value || [];
    this.selectionChange.emit(this._selection);
  }


  //declarations:
  filteredData: any[] = [];
  rowData: any[] = [];
  public sortingOrder = ["desc", "asc"];
  public filterText: string = '';
  public tableDataCount: number = 0;

  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  public TABLE_ACTION_TYPES = TABLE_ACTION_TYPES;
  public status: string;
  filterFormGroup: FormGroup;
  fields: any[] = [];
  public isShowQuickFilter: boolean = false;
  public showActiveFiltersCount: number = 0;
  public columnsDef: any[] = [];
  public isMobile: boolean = false;

  mainFilterFormGroup: FormGroup;
  mainFilterFields: any[] = [];

  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);

  private actionColumn: any = null;

  first = 0;
  rows = 10;
  itemsPerPage = 15;

  constructor(
    private _commonService: CommonService,
    private _formFactoryService: FormFactoryService,
  ) {
  }

  public ngOnInit(): void {
    this._commonService.isMobile().subscribe(result => {
      this.isMobile = result.matches;
    });
    this._commonService.loadingTableData
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => this.isLoading = response
      })
    this.isShowQuickFilter = Object.keys(this.quickFilterFormFields)?.length > 0;
    if (this.isShowQuickFilter) {
      this.buildQuickFilterForm();
    }
    this.mainFilterModel
      .pipe(takeUntil(this.$destroyed))
      .subscribe((model) => {
        if (model && Object.keys(model).length > 0) {
          this.buildMainFilterForm(model);
        }
      });
    this.actionColumn = this.columnsDef.find(col => col.isActionCol);
  }

  private setColumnsDefs(columnsDefs: any[]): any {
    this.columnsDef = columnsDefs;
  }

  private buildQuickFilterForm(): void {
    const formGroupFields = this._formFactoryService.getFormControlsFields(this.quickFilterFormFields);
    this.filterFormGroup = new FormGroup(formGroupFields);
    this.fields = this._formFactoryService.getFieldsList(this.quickFilterFormFields);
    this.showActiveFiltersCount = Object.keys(this.quickFilterFormFields).filter(key => this.quickFilterFormFields[key] != null && this.quickFilterFormFields[key] != undefined && this.quickFilterFormFields[key] != '').length;
    this.onClickQuickFilter();
  }

  private buildMainFilterForm(mainFilterModel): void {
    const formGroupFields = this._formFactoryService.getFormControlsFields(mainFilterModel);
    this.mainFilterFormGroup = new FormGroup(formGroupFields);
    this.mainFilterFields = this._formFactoryService.getFieldsList(mainFilterModel);
    this.onMainFilterChange();
  }

  getRowActions(rowData: any): any[] {
    return this.actionColumn ? this.actionColumn.actions(rowData) : [];
  }

  private sortField: string | null = null;
  private sortOrder: number = 0; // 0: none, 1: ascending, -1: descending

  isSortedColumn(field: string): boolean {
    return this.sortField === field;
  }

  getSortOrder(field: string): number {
    return this.sortField === field ? this.sortOrder : 0;
  }

  onSort(event: any) {
    this.sortField = event.field;
    this.sortOrder = event.order;
    // Your existing sort logic
  }

  //
  public onFilterTextBoxChanged(filterValue: string): void {
    if (this.filterText) {
      this.filteredData = this.rowData.filter(user =>
        Object.values(user).some(value =>
          value.toString().toLowerCase().includes(this.filterText.toLowerCase())
        )
      );
    } else {
      this.filteredData = this.rowData; // Show all if no search term
    }
    this.tableDataCount = this.filteredData.length;
  }

  public onSelectChanged(row: any[]): void {
    if (this.selection.includes(row)) {
      this.selection = this.selection.filter(item => item != row);
    } else {
      this.selection.push(row);
    }

    this.onSelectionChange.emit(this.selection);
  }

  public getSelectedRow(row: any): boolean {
    return this.selection.includes(row);
  }

  public pageChange(event) {
    this.first = event.first;
    this.rows = event.rows;
  }


  public onViewRefresh(): void {
    this.onRefresh.emit(true);
  }
  public onClickQuickFilter(): void {
    this.onChangeQuickFilter.emit(this.filterFormGroup.value)
  }
  public onMainFilterChange(): void {
    this.onChangeFilter.emit(this.mainFilterFormGroup.value)
  }


}

