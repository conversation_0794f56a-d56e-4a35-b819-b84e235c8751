import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { SharedModule } from 'app/shared/shared.module';
import {
  actionType,
  buttonActions,
  CommonService,
  HTTP_STATUS,
  ICON_BUTTON_SEVERITY,
  ICON_PRIMENG_LIST,
  InvokeService,
  TABLE_ACTION_TYPES,
  toolbarActions,
} from 'app/shared/tapas-ui';
import { DialogService } from 'primeng/dynamicdialog';
import { ReplaySubject, takeUntil } from 'rxjs';

@Component({
  selector: 'tps-complaints',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './complaints.component.html'
})
export class ComplaintsComponent implements OnInit, OnDestroy {
  hdr: string = 'Complaints';
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);
  columnDefs: any[] = [];
  complaints: any[] = [];
  ACL_LIST = this._commonService.getACLList();
  buttonActions: buttonActions[] = [];
  toolBarActions: toolbarActions[] = [];

  constructor(
    private _commonService: CommonService,
    private _invokeService: InvokeService,
    private _dialogService: DialogService,
  ) { }

  public ngOnInit(): void {
    this.prepareTableActions();
    this.onRefresh();
  }

  private prepareTableActions(): void {
    this.buttonActions = [
      {
        name: 'Create Complaint',
        icon: 'pi-plus',
        primary: true,
        isSvg: false,
        show: true, // Update with proper ACL when available
        command: () => {
          // Add dialog open logic here
        }
      }
    ];
    this.toolBarActions = [];
  }

  public onRefresh(): void {
    this.getComplaints();
    this.prepareTableColumns();
  }

  private prepareTableColumns(): void {
    this.columnDefs = [
      {
        headerName: "ID",
        field: "id",
        sortable: true,
      },
      {
        headerName: "Subject",
        field: "subject",
        sortable: true,
      },
      {
        headerName: "Status",
        field: "status",
        sortable: true,
      },
      {
        headerName: "Priority",
        field: "priority",
        sortable: true,
      },
      {
        headerName: "Created On",
        field: "createdTimeText",
        sortable: true,
      },
      {
        headerName: 'Actions',
        pinned: 'right',
        maxWidth: 100,
        sortable: false,
        isActionCol: true,
        actions: (params) => this.prepareTableActionIcons(params)
      }
    ];
  }

  private getComplaints(): void {
    // Implement API call to get complaints
    // Example:
    // this._invokeService.getComplaints()
    //   .pipe(
    //     takeUntil(this.$destroyed),
    //     finalize(() => {})
    //   )
    //   .subscribe({
    //     next: (response) => {
    //       if (response.status === HTTP_STATUS.SUCCESS) {
    //         this.complaints = response.data || [];
    //       }
    //     }
    //   });

    // For now, using mock data
    this.complaints = [];
  }

  private prepareTableActionIcons(params): actionType[] {
    const actions: actionType[] = [];



    return actions;
  }

  public ngOnDestroy(): void {
    this.$destroyed.next(null);
    this.$destroyed.complete();
  }
}