import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { ImageModule } from 'primeng/image';

@Component({
  selector: 'app-view-image',
  standalone: true,
  imports: [ImageModule],
  templateUrl: './tps-view-image.component.html',
  styleUrl: './tps-view-image.component.scss'
})
export class TpsViewImageComponent implements OnInit, OnDestroy {
  previewImage: any;
  constructor(private _dialogConfig: DynamicDialogConfig,
    public _dialogConfigRef: DynamicDialogRef,) { }

  public ngOnInit(): void {
    this.previewImage = this._dialogConfig.data;
  }


  public ngOnDestroy(): void {

  }

}
