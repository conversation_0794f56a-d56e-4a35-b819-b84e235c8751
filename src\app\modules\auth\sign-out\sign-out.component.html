<div class="flex flex-col flex-auto items-center justify-center min-h-screen">
    <div class="w-full max-w-md p-8 m-4 bg-white rounded-xl shadow-lg">
        <!-- Logo Container -->
        <div class="flex justify-center mb-8">
            <img [src]="headerLogo" alt="Logo image" class="w-48 h-auto">
        </div>

        <!-- Content Container -->
        <div class="text-center">
            <!-- Success Icon -->
            <div class="mb-6">
                <i class="pi pi-check-circle text-6xl text-green-500"></i>
            </div>

            <!-- Title -->
            <h1 class="text-3xl font-bold text-gray-800 mb-4">
                Successfully Signed Out
            </h1>

            <!-- Status Message -->
            <div class="text-gray-600 mb-8">
                <ng-container *ngIf="countdown > 0">
                    <div class="flex items-center justify-center gap-2">
                        <i class="pi pi-spin pi-spinner"></i>
                        <span>Redirecting in {{countdown | i18nPlural: countdownMapping }}</span>
                    </div>
                </ng-container>
                <ng-container *ngIf="countdown === 0">
                    <span>You are now being redirected!</span>
                </ng-container>
            </div>

            <!-- Action Button -->
            <div class="flex flex-col items-center gap-4">
                <a [routerLink]="['/sign-in']"
                    class="px-6 py-2 bg-primary-500 text-white rounded-full hover:bg-primary-600 transition-colors duration-200">
                    Return to Sign In
                </a>
                <span class="text-sm text-gray-500">
                    Thank you for using our application
                </span>
            </div>
        </div>
    </div>
</div>