import { Routes } from '@angular/router';
import { permissionGuard } from 'app/core/auth/guards/permission.guard';

import { CategoryComponent } from './category.component';

export default [
    {
        path: '',
        component: CategoryComponent,
        canActivate: [permissionGuard],
        data: { permission: "CATEGORY_MASTER" },
    },
    { path: 'not-found', loadChildren: () => import('app/modules/page-not-found/page-not-found.routes') },
    { path: '**', pathMatch: 'full', redirectTo: 'not-found' },
] as Routes;