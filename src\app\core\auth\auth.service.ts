import { HttpClient, HttpHeaders } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { environment } from 'environments/environment';
import { catchError, map, Observable, of, switchMap, throwError } from 'rxjs';

import { iUser } from '../models/user.model';

@Injectable({ providedIn: 'root' })
export class AuthService {
    private _authenticated: boolean = false;
    private _httpClient = inject(HttpClient);
    private _router = inject(Router);


    forgotPassword(email: string): Observable<any> {
        return this._httpClient.post('api/auth/forgot-password', email);
    }
    resetPassword(password: string): Observable<any> {
        return this._httpClient.post('api/auth/reset-password', password);
    }

    /**
     * Sign in
     *
     * @param credentials
     */
    signIn(credentials: { username: string; password: string }): Observable<any> {
        let token = 'Basic ' + btoa(credentials.username + ':' + credentials.password);
        let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': environment.tenantUid })
            .set("Authorization", token);
        let url = `${environment.apiUrl}${APP_UI_CONFIG.auth.login.url}`;
        return this._httpClient.get<iUser>(url, { headers: headers }).pipe(map(res => {
            // store user details and jwt token in local storage to keep user logged in between page refreshes
            let user: any = res;
            user.token = token
            user.pic = './assets/media/misc/user-profile-icon-20-400x400.png';
            return user;
        }))
    }

    //API Calls
    public getTenant(username, password): Observable<any> {
        let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'username': username, 'password': password })
        let url = `${environment.apiUrl}${APP_UI_CONFIG.configuration.getTenant.url}`;
        return this._httpClient.get<any>(url, { headers: headers, observe: 'response' }).pipe(map(res => {
            return res;
        }))
    }

    public getTenantConfig(credentials: { username: string; password: string },tenantUid:string): Observable<any> {
        let token = 'Basic ' + btoa(credentials.username + ':' + credentials.password);
        let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': tenantUid })
            .set("Authorization", token);
       let url = `${environment.apiUrl}config/tenantConfig/${tenantUid}`;
        return this._httpClient.get<any>(url, { headers: headers })
    }

    public validateMFACode(username, password, mfaCode,tenantUid): Observable<any> {
        var token = 'Basic ' + btoa(username + ':' + password);
        const headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': tenantUid })
            .set("Authorization", token);
        let url = `${environment.apiUrl}${APP_UI_CONFIG.auth.mfaValidation.url}${mfaCode}`;
        return this._httpClient.post<any>(url, { headers: headers })
    }

    public getQRImage(username, password): Observable<any> {
        var token = 'Basic ' + btoa(username + ':' + password);
        let headers = new HttpHeaders({ 'Client': environment.tenantUid })
            .set("Authorization", token);
        let url = `${environment.apiUrl}${APP_UI_CONFIG.auth.mfaRegistration.url}`;
        return this._httpClient.get(url, { headers: headers, responseType: 'text' })
    }



    /**
     * Sign out
     */
    signOut(): Observable<any> {
        sessionStorage.clear();
        // Return the observable
        this._authenticated = false;
        this._router.navigate(['/sign-out']);
        return of(true);
    }
    /**
   * Check the authentication status
   */
    check(): Observable<boolean> {
        // Check if the user is logged in
        if (this.getLoggedInUser()) {
            return of(true);
        } else {
            return of(false);
        }
    }



    //set logged in user data
    public setLoggeInUser(user): void {
        sessionStorage.setItem('currentUser', JSON.stringify(user));
    }

    //get logged in user data
    public getLoggedInUser(): any {
        if (sessionStorage.hasOwnProperty('currentUser')) {
            return JSON.parse(sessionStorage.getItem('currentUser'));
        } else {
            return null;
        }
    }
}
