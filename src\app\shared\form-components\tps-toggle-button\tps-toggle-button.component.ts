import { CommonModule, NgIf } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { FormGroup, FormGroupDirective, ReactiveFormsModule } from '@angular/forms';
import { FORM_CONTROL_TYPES } from 'app/shared/tapas-ui';
import { ToggleButtonModule } from 'primeng/togglebutton';
import { TooltipModule } from 'primeng/tooltip';

import { TpsErrorComponent } from '../tps-error/tps-error.component';

class tpsToggleButton {
  type: string
  fieldName: string
  onLabel?: string
  offLabel?: string
  tooltip?: string
  hint?: string
  value: any
  label: string
  placeholder: string
  show: boolean
  onChange: (any) => void
  rules: {
    required?: boolean,
    step: 1
  }
}

@Component({
  selector: 'tps-toggle-button',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, NgIf, TpsErrorComponent, ToggleButtonModule, TooltipModule],
  templateUrl: './tps-toggle-button.component.html',
})
export class TpsToggleButtonComponent implements OnInit {
  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  @Input() field: tpsToggleButton;
  @Input() formName: FormGroup;

  constructor(private formGroupDirective: FormGroupDirective) {
    this.formName = formGroupDirective.control;
  }

  public ngOnInit(): void {
    console.log("field:",this.field)
    console.log("formName:",this.formName)
  }
}
