<!-- Loading bar -->
<fuse-loading-bar class="w-full"></fuse-loading-bar>

<!-- Navigation -->
<ng-container *ngIf="isScreenSmall">
    <fuse-vertical-navigation class="dark bg-gray-900 print:hidden" [mode]="'over'" [name]="'mainNavigation'"
        [navigation]="navigation" [opened]="false">
        <!-- Navigation header hook -->
        <ng-container fuseVerticalNavigationContentHeader>
            <!-- Logo -->
            <div class="flex items-center h-20 pt-6 px-8">
                <img class="w-24 max-h-full object-contain" [src]="headerLogo" alt="Logo image">
            </div>
        </ng-container>
    </fuse-vertical-navigation>
</ng-container>

<!-- Wrapper -->
<div class="flex flex-col flex-auto w-full min-w-0">
    <!-- Header -->
    <div
        class="tps-header-wrapper tps-body-padding relative flex flex-0 items-center w-full z-49 bg-card dark:bg-transparent print:hidden min-h-[64px]">
        <ng-container *ngIf="!isScreenSmall">
            <!-- Logo -->
            <div class="flex items-center lg:mr-8">
                <div class="hidden lg:flex">
                    <img class="max-h-[50px] w-auto object-contain" [src]="headerLogo">
                </div>
                <img class="flex lg:hidden max-h-[40px] w-auto object-contain" [src]="headerLogo">
            </div>
            <!-- Horizontal navigation -->
            <fuse-horizontal-navigation class="mr-2" [name]="'mainNavigation'" [navigation]="navigation">
            </fuse-horizontal-navigation>
        </ng-container>

        <!-- Navigation toggle button -->
        <ng-container *ngIf="isScreenSmall">
            <div class="flex items-center">
                <p-button icon="pi pi-bars" (click)="toggleNavigation('mainNavigation')" severity="secondary"
                    class="mr-4" />
                <div class="flex items-center">
                    <img class="max-h-[40px] w-auto object-contain" [src]="headerLogo" alt="Logo">
                </div>
            </div>
        </ng-container>

        <!-- Components -->
        <div class="flex items-center pl-2 ml-auto space-x-0.5 sm:space-x-2">
            <!-- <notifications></notifications> -->
            <user></user>
        </div>
    </div>

    <!-- Content -->
    <div class="flex flex-col flex-auto w-full tps-body-padding tps-main-body-wrapper" id="tps-main-content">
        <div class="w-full h-full bg-card " [class.rounded-2xl]="!isMobile">
            <router-outlet *ngIf="true"></router-outlet>
        </div>
    </div>

    <!-- Footer -->
    <div
        class="relative flex flex-0 items-center w-full px-4 md:px-6 z-49 border-t bg-card dark:bg-transparent print:hidden">
        <div class="flex flex-col sm:flex-row justify-between w-full pl-3">
            <span class="font-medium text-sm sm:text-base">{{projectPoweredBy}}</span>
            <div class="flex flex-row gap-4 sm:gap-8 text-sm sm:text-base">
                <!-- Add your footer content here -->
            </div>
        </div>
    </div>
</div>