/* Root navigation specific */
* {
    box-sizing: border-box !important;
}

fuse-horizontal-navigation {

    .fuse-horizontal-navigation-wrapper {
        display: flex;
        align-items: center;
        gap: 8px;
        box-sizing: border-box;

        /* Basic, Branch */
        fuse-horizontal-navigation-basic-item,
        fuse-horizontal-navigation-branch-item {
            @screen sm {}

            &:hover {

                .fuse-horizontal-navigation-item {
                    // border: 1px solid #3366FF !important;
                    // border-radius: 2px;
                }
            }

            .fuse-horizontal-navigation-item-wrapper {
                overflow: hidden;

                .fuse-horizontal-navigation-item {
                    box-sizing: border-box;
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    padding: 8px 0px 8px 8px;
                    gap: 8px;
                    cursor: pointer;
                    border: 1px solid white;
                    border-radius: 2px;


                    .fuse-horizontal-navigation-item-icon {
                        margin-left: 8px;
                    }
                }

                .normal-menu-icon {
                    display: block;
                }

                .active-menu-icon {
                    display: none;
                }
            }
        }

        fuse-horizontal-navigation-basic-item {
            .fuse-horizontal-navigation-item {
                border-bottom: 5px solid transparent;
                padding: 8px 16px 8px 16px !important;
            }
        }

        /* Basic - When item active (current link) */
        fuse-horizontal-navigation-basic-item {
            .fuse-horizontal-navigation-item-wrapper {
                .fuse-horizontal-navigation-item-active {
                    border-bottom: 5px solid #517EBC !important;
                    border-radius: 7px 4px 0px 0px;
                }
            }

            .fuse-horizontal-navigation-item-active,
            .fuse-horizontal-navigation-item-active-forced {
                .fuse-horizontal-navigation-item {
                    // background: #517EBC;
                    // border-radius: 7px 4px 0px 0px;   // active item
                }

                .fuse-horizontal-navigation-item-title {
                    color: #3366FF;
                }

                .fuse-horizontal-navigation-item-subtitle {
                    color: var(--primary-400) !important;

                    :host-context(.dark) & {
                        color: var(--primary-600) !important;
                    }
                }

                .fuse-horizontal-navigation-item-icon {
                   @apply text-primary #{'!important'};
                }
            }
        }

        /* Branch - When menu open */
        fuse-horizontal-navigation-branch-item {

            .fuse-horizontal-navigation-menu-active,
            .fuse-horizontal-navigation-menu-active-forced {
                border-bottom: 5px solid #517EBC !important;
                border-radius: 7px 4px 0px 0px;

                .fuse-horizontal-navigation-item {
                    // background: #517EBC;
                    // border-radius: 7px 4px 0px 0px;  // active item
                }

                .fuse-horizontal-navigation-item-wrapper {
                    // background: #D6E4FF;
                }

                .fuse-horizontal-navigation-item-title {
                    color: #517EBC;
                    font-weight: 600 !important;
                    font-size: 14px;
                    line-height: 18px;

                    .active-menu-icon {
                        display: block;
                    }

                    .normal-menu-icon {
                        display: none;
                    }
                }
            }
        }

        /* Spacer */
        fuse-horizontal-navigation-spacer-item {
            margin: 12px 0;
        }
    }
}


#cdk-overlay-0 {
    .cdk-overlay-pane {
        z-index: 999999999;
        border: 1px solid lightgray;
    }
}

/* Menu panel specific */
.fuse-horizontal-navigation-menu-panel {
    background: #FFFFFF;
    border: 1px solid #E7EAF0;
    box-shadow: 0px 1px 20px rgba(123, 134, 153, 0.2) !important;
    margin-right: 4px;
    // border-radius: 10px;

    .fuse-horizontal-navigation-menu-item {
        height: auto;
        min-height: 0;
        line-height: normal;
        white-space: normal;
        // padding-left: 15px;
        // padding-top: 7px;
        // padding-bottom: 7px;
        width: 100%;
        // margin-bottom: 4px;
        // margin-top: 4px;
        background: #FFFFFF;

        /* Basic, Branch */
        fuse-horizontal-navigation-basic-item,
        fuse-horizontal-navigation-branch-item,
        fuse-horizontal-navigation-divider-item {
            display: flex;
            flex: 1 1 auto;
        }

        /* Divider */
        fuse-horizontal-navigation-divider-item {
            margin: 8px -12px;


            .fuse-horizontal-navigation-item-wrapper {
                height: 1px;
                box-shadow: 0 1px 0 0;
            }
        }
    }
}

/* Navigation menu item common */
.fuse-horizontal-navigation-menu-item {
    border-right: 1px solid #E7EAF0;
    padding-right: 8px;

    /* Basic - When item active (current link) */
    fuse-horizontal-navigation-basic-item {

        .fuse-horizontal-navigation-item-active,
        .fuse-horizontal-navigation-item-active-forced {
            color: #0F1925;

            .fuse-horizontal-navigation-item-title {
                color: #0F1925;
                font-weight: 600 !important;
            }

            .fuse-horizontal-navigation-item-subtitle {
                color: #0F1925
            }

            .fuse-horizontal-navigation-item-icon {
                color: #0F1925;
            }
        }
    }

    .fuse-horizontal-navigation-item-wrapper {
        width: 100%;

        &.fuse-horizontal-navigation-item-has-subtitle {

            .fuse-horizontal-navigation-item {}
        }

        .fuse-horizontal-navigation-item {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            font-family: 'Source Sans Pro';
            font-style: normal;
            font-weight: 400;
            font-size: 14px;
            line-height: 18px;
            color: #59667A;
            height: 40px;
            gap:8px;
            padding-left: 16px;
            padding-right: 16px;
            width: 100%;

            .fuse-horizontal-navigation-item-title-wrapper {

                .fuse-horizontal-navigation-item-subtitle {
                    color: #7B8699 !important;
                    font-family: 'Source Sans Pro';
                    font-style: normal;
                    font-weight: 600;
                    font-size: 14px;
                    line-height: 18px;
                    font-style: normal;
                }

                .menu_icon {
                    color: #3366FF !important;
                    width: 20px;
                    height: 20px;
                    margin-left: 8px;
                    //margin-right: 8px;
                }
            }

            .fuse-horizontal-navigation-item-badge {
                margin-left: auto;

                .fuse-horizontal-navigation-item-badge-content {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 10px;
                    font-weight: 600;
                    white-space: nowrap;
                    height: 20px;
                }
            }
        }
    }
}

.mat-mdc-menu-submenu-icon {
    fill: #3366FF !important;
    font-size: 20px !important;
}

.mat-mdc-menu-item {
    .fuse-horizontal-navigation-item-title {
        font-family: 'Source Sans Pro';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 18px;
        color: #7B8699;
        font-style: normal;
    }

    .fuse-horizontal-navigation-item-active-forced {
        color: #0F1925 !important;
        font-weight: 600 !important;
    }

}


.mat-mdc-menu-content .mat-mdc-menu-item .mdc-list-item__primary-text {
    width: 100% !important;
}

.fuse-horizontal-navigation-item-title {
    font-size: 14px;
}
